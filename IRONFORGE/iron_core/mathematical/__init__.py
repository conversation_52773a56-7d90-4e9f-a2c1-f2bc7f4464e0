"""Core modules: domain types, invariants, mathematical layers, engines.

This package contains the mathematical foundation of the Oracle system:
- constraints: Mathematical constants and business rules
- cascade_classifier: Event classification system
- temporal_correlator: Temporal correlation analysis
- fisher_information_monitor: Fisher Information spike detection
- grammar_fisher_correlator: Grammar-Fisher correlation engine
- hawkes_engine: Hawkes process implementation
- mathematical_hooks: Mathematical computation hooks
- mathematical_layers: Core mathematical abstractions
- rg_scaler_production: RG scaling implementation
- scaling_patterns: Computational scaling patterns
"""

__all__ = [
    "constraints",
    "cascade_classifier",
    "temporal_correlator",
    "fisher_information_monitor",
    "grammar_fisher_correlator",
    "hawkes_engine",
    "mathematical_hooks",
    "mathematical_layers",
    "rg_scaler_production",
    "scaling_patterns",
]

