#!/usr/bin/env python3
"""
Invariant Guards - Lightweight Architectural Control System
==========================================================

Feature drift occurs when implementation entropy exceeds semantic binding energy.
Solution: Lightweight Invariant Guards - computable contracts that detect when 
code deviates from intent in real-time.

Mathematical Foundation:
- System state: S(t) = (F, D, I) where F=features, D=dependencies, I=intent
- Drift metric: δ(t) = ||I(t) - I₀|| / ||I₀||
- Critical threshold: δ_crit = 0.15 (15% semantic drift → architectural failure)
"""

import ast
import hashlib
import inspect
import time
from typing import Dict, Callable, Any, List
from dataclasses import dataclass
from pathlib import Path

@dataclass
class DriftEvent:
    """Record of architectural drift detection"""
    function: str
    timestamp: float
    expected_inputs: str
    actual_inputs: str
    expected_outputs: str
    purpose: str
    severity: str

@dataclass
class Contract:
    """Semantic binding contract for functions"""
    signature: str
    inputs: str
    outputs: str
    purpose: str
    func: Callable
    created_at: float
    call_count: int = 0
    drift_count: int = 0

class InvariantGuard:
    """Minimal viable architectural control system"""
    
    def __init__(self):
        self.contracts: Dict[str, Contract] = {}
        self.drift_log: List[DriftEvent] = []
        self.baseline_established = False
        
    def register(self, name: str, 
                 inputs: str, 
                 outputs: str, 
                 purpose: str) -> Callable:
        """Decorator for semantic binding with O(1) drift detection"""
        def decorator(func):
            # Compute semantic hash for contract verification
            semantic_content = f"{inputs}→{outputs}:{purpose}"
            semantic_sig = hashlib.sha256(semantic_content.encode()).hexdigest()[:8]
            
            # Store contract
            self.contracts[name] = Contract(
                signature=semantic_sig,
                inputs=inputs,
                outputs=outputs,
                purpose=purpose,
                func=func,
                created_at=time.time()
            )
            
            def wrapper(*args, **kwargs):
                # Pre-execution drift detection
                contract = self.contracts[name]
                contract.call_count += 1
                
                drift_detected = self._detect_drift(name, args, kwargs)
                if drift_detected:
                    contract.drift_count += 1
                    self._log_drift(name, args, kwargs, contract)
                    print(f"⚠️ DRIFT: {name} deviating from '{contract.purpose}'")
                
                return func(*args, **kwargs)
            
            # Preserve original function metadata
            wrapper.__name__ = func.__name__
            wrapper.__doc__ = func.__doc__
            wrapper._contract = self.contracts[name]
            
            return wrapper
        return decorator
    
    def _detect_drift(self, name: str, args: tuple, kwargs: dict) -> bool:
        """O(1) drift detection via heuristic analysis"""
        contract = self.contracts[name]
        
        try:
            # Heuristic 1: Input type/shape validation
            if args and hasattr(args[0], '__class__'):
                actual_type = args[0].__class__.__name__
                expected_keywords = contract.inputs.lower().split(',')
                
                # Check if actual type aligns with expected inputs
                type_match = any(keyword.strip() in actual_type.lower() 
                               for keyword in expected_keywords)
                
                if not type_match and 'array' not in contract.inputs.lower():
                    return True
            
            # Heuristic 2: Argument count validation
            expected_args = len([x for x in contract.inputs.split(',') if x.strip()])
            actual_args = len(args) + len(kwargs)
            
            if abs(actual_args - expected_args) > 2:  # Allow some flexibility
                return True
            
            # Heuristic 3: Purpose keyword violation
            # If function is supposed to be "ONLY" for something, check context
            if "ONLY" in contract.purpose.upper():
                # This is a strict contract - any deviation is suspicious
                func_source = inspect.getsource(contract.func)
                
                # Simple check: if function has grown significantly, flag drift
                line_count = len(func_source.split('\n'))
                if line_count > 50:  # Arbitrary threshold for function complexity
                    return True
                    
        except Exception as e:
            # If we can't analyze, assume no drift to avoid false positives
            return False
        
        return False
    
    def _log_drift(self, name: str, args: tuple, kwargs: dict, contract: Contract):
        """Log drift event for analysis"""
        try:
            actual_inputs = f"{type(args[0]).__name__}" if args else "None"
        except:
            actual_inputs = "Unknown"
            
        drift_event = DriftEvent(
            function=name,
            timestamp=time.time(),
            expected_inputs=contract.inputs,
            actual_inputs=actual_inputs,
            expected_outputs=contract.outputs,
            purpose=contract.purpose,
            severity="WARNING"
        )
        
        self.drift_log.append(drift_event)
        
        # Keep only last 100 drift events to prevent memory bloat
        if len(self.drift_log) > 100:
            self.drift_log = self.drift_log[-100:]
    
    def checkpoint(self) -> Dict[str, Any]:
        """System coherence snapshot - O(1) operation"""
        total_calls = sum(c.call_count for c in self.contracts.values())
        total_drifts = sum(c.drift_count for c in self.contracts.values())
        
        # Coherence metric: 1.0 = perfect, 0.0 = complete drift
        coherence = 1.0 - (total_drifts / max(total_calls, 1))
        
        return {
            'total_functions': len(self.contracts),
            'total_calls': total_calls,
            'drift_events': len(self.drift_log),
            'total_drift_count': total_drifts,
            'coherence': coherence,
            'recent_drift_rate': len([d for d in self.drift_log if time.time() - d.timestamp < 3600]) / 3600,  # per hour
            'high_drift_functions': [
                name for name, contract in self.contracts.items() 
                if contract.call_count > 0 and (contract.drift_count / contract.call_count) > 0.1
            ]
        }
    
    def function_health(self, name: str) -> Dict[str, Any]:
        """Health metrics for specific function"""
        if name not in self.contracts:
            return {'error': 'Function not registered'}
        
        contract = self.contracts[name]
        drift_rate = contract.drift_count / max(contract.call_count, 1)
        
        # Health assessment
        if drift_rate < 0.05:
            health = "EXCELLENT"
        elif drift_rate < 0.15:
            health = "GOOD"
        elif drift_rate < 0.30:
            health = "CONCERNING"
        else:
            health = "CRITICAL"
        
        return {
            'health': health,
            'drift_rate': drift_rate,
            'call_count': contract.call_count,
            'drift_count': contract.drift_count,
            'purpose': contract.purpose,
            'last_drift': max([d.timestamp for d in self.drift_log if d.function == name], default=0)
        }
    
    def export_report(self, filename: str = "architectural_health_report.md"):
        """Generate comprehensive architectural health report"""
        checkpoint = self.checkpoint()
        
        report = f"""# Architectural Health Report
Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}

## System Overview
- **Total Functions**: {checkpoint['total_functions']}
- **Total Function Calls**: {checkpoint['total_calls']}
- **System Coherence**: {checkpoint['coherence']:.1%}
- **Recent Drift Rate**: {checkpoint['recent_drift_rate']:.2f} events/hour

## Function Health Status
"""
        
        for name, contract in self.contracts.items():
            health = self.function_health(name)
            report += f"""
### {name}
- **Health**: {health['health']}
- **Purpose**: {contract.purpose}
- **Drift Rate**: {health['drift_rate']:.1%}
- **Calls**: {health['call_count']}
- **Inputs**: {contract.inputs}
- **Outputs**: {contract.outputs}
"""
        
        if checkpoint['high_drift_functions']:
            report += f"""
## ⚠️ High Drift Functions
These functions are deviating from their intended purpose:
"""
            for func in checkpoint['high_drift_functions']:
                health = self.function_health(func)
                report += f"- **{func}**: {health['drift_rate']:.1%} drift rate\n"
        
        # Recent drift events
        recent_drifts = [d for d in self.drift_log if time.time() - d.timestamp < 3600]
        if recent_drifts:
            report += f"""
## Recent Drift Events (Last Hour)
"""
            for drift in recent_drifts[-10:]:  # Last 10 events
                report += f"""
- **{drift.function}**: {drift.purpose}
  - Expected: {drift.expected_inputs}
  - Actual: {drift.actual_inputs}
  - Time: {time.strftime('%H:%M:%S', time.localtime(drift.timestamp))}
"""
        
        # Write report
        with open(filename, 'w') as f:
            f.write(report)
        
        print(f"📊 Architectural health report saved: {filename}")
        return filename

# Global guard instance for immediate use
guard = InvariantGuard()

# Convenience function for quick setup
def architectural_control(name: str, inputs: str, outputs: str, purpose: str):
    """Convenience wrapper for guard.register"""
    return guard.register(name, inputs, outputs, purpose)

if __name__ == "__main__":
    # Demonstration
    @guard.register(
        name="demo_function",
        inputs="market_events",
        outputs="pattern_id", 
        purpose="Demonstrate invariant guard system"
    )
    def demo_function(events):
        return "pattern_123"
    
    # Test the system
    result = demo_function(["test_event"])
    print(f"Result: {result}")
    
    # Generate health report
    report = guard.checkpoint()
    print(f"System health: {report}")