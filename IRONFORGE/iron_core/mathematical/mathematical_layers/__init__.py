# IRONPULSE Mathematical Layers Framework

from .theory_abstraction import (
    MathematicalModel,
    TheoryAbstractionLayer,
    HawkesTheoryAbstraction,
    HTFTheoryAbstraction
)

from .core_algorithms import (
    CoreAlgorithmLayer,
    HawkesAlgorithmImplementation,
    FFTOptimizedCorrelator,
    QuantumInspiredOptimizer
)

from .integration_layer import (
    <PERSON><PERSON><PERSON>er,
    ModelChain,
    MathematicalModelRegistry,
    create_oracle_prediction_chain,
)

from .validation_framework import (
    ValidationLayer,
    MathematicalPropertyTest,
    NumericalStabilityTest,
    PerformanceBenchmarkTest
)

from .api_interface import (
    APIInterfaceLayer,
    MathematicalModelAPI
)

__version__ = "1.0.0"
__author__ = "Oracle Mathematical Architecture Team"

__all__ = [
    # Theory Layer
    'MathematicalModel',
    'TheoryAbstractionLayer', 
    'HawkesTheoryAbstraction',
    'HTFTheoryAbstraction',
    
    # Algorithm Layer
    'CoreAlgorithmLayer',
    'HawkesAlgorithmImplementation',
    'FFTOptimizedCorrelator',
    'QuantumInspiredOptimizer',
    
    # Integration Layer  
    'IntegrationLayer',
    'ModelChain',
    'MathematicalModelRegistry',
    
    # Validation Layer
    'ValidationLayer',
    'MathematicalPropertyTest',
    'NumericalStabilityTest', 
    'PerformanceBenchmarkTest',
    
    # API Layer
    'APIInterfaceLayer',
    'MathematicalModelAPI'
]