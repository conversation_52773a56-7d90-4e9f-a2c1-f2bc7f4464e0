{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22
    ],
    "5m": [],
    "15m": [
      23,
      24,
      25,
      26,
      27,
      28,
      29,
      30,
      31,
      32,
      33,
      34,
      35,
      36,
      37,
      38,
      39,
      40
    ],
    "1h": [],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=0.0005207112145925218, price_delta_5m=0.016784054626321643, price_delta_15m=0.008096135153170375, volatility_window=0.04074135290375717, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23144.25, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231375, price_delta_1m=0.004030584940716027, price_delta_5m=-0.0022764829432771754, price_delta_15m=0.0018118429892935628, volatility_window=0.03891035678706763, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=116, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23137.5, 'movement_type': 'session_low_immediate'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=-0.0011984828291496758, price_delta_5m=-0.008910666032493943, price_delta_15m=-0.002960868704571757, volatility_window=0.02877357811749575, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=117, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price_level': 23165.0, 'movement_type': 'premarket_fpfvg_formation_premium'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23158, price_delta_1m=-0.012641407598908692, price_delta_5m=0.023601249796693567, price_delta_15m=-0.009269489557683227, volatility_window=0.037604322092871574, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=118, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price_level': 23158.0, 'movement_type': 'premarket_fpfvg_formation_discount'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231725, price_delta_1m=-0.0052973275676687515, price_delta_5m=0.0032083749621881126, price_delta_15m=-0.004147809851863611, volatility_window=0.04829757327516058, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:02:00', 'price_level': 23172.5, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=8.0, daily_phase_sin=0.9563047559630355, daily_phase_cos=-0.29237170472273666, session_position=0.053691275167785234, time_to_close=141.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315225, price_delta_1m=-0.0006489041015057808, price_delta_5m=-0.006074356275036259, price_delta_15m=0.0028457551722727093, volatility_window=0.012017863000816958, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:08:00', 'price_level': 23152.25, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=11.0, daily_phase_sin=0.9523957996432784, daily_phase_cos=-0.30486429902801077, session_position=0.0738255033557047, time_to_close=138.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317575, price_delta_1m=-0.010395106652204913, price_delta_5m=-0.02278086339401272, price_delta_15m=0.0002553380116070605, volatility_window=0.010954960986676459, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:11:00', 'price_level': 23175.75, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=12.0, daily_phase_sin=0.9510565162951536, daily_phase_cos=-0.30901699437494734, session_position=0.08053691275167785, time_to_close=137.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315425, price_delta_1m=-0.0007742702153277411, price_delta_5m=-0.010824998611384803, price_delta_15m=-0.012995561773637752, volatility_window=0.021883486189031562, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:12:00', 'price_level': 23154.25, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=22.0, daily_phase_sin=0.9366721892483976, daily_phase_cos=-0.35020738125946754, session_position=0.1476510067114094, time_to_close=127.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23178, price_delta_1m=0.0030289869450319125, price_delta_5m=0.015694821950610643, price_delta_15m=0.00858470105099282, volatility_window=0.019641405941078625, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:22:00', 'price_level': 23178.0, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=26.0, daily_phase_sin=0.9304175679820246, daily_phase_cos=-0.3665012267242972, session_position=0.174496644295302, time_to_close=123.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=-0.00864473428221966, price_delta_5m=0.005541138341757655, price_delta_15m=-0.0012406610169475273, volatility_window=0.046319989060478385, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:26:00', 'price_level': 23165.0, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319675, price_delta_1m=0.011491005185114136, price_delta_5m=-0.008752240123593024, price_delta_15m=-0.012245862369494922, volatility_window=0.04203405669613113, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=34, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:32:00', 'price_level': 23196.75, 'movement_type': 'session_high_reversal_point'})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23181, price_delta_1m=0.013677062791343158, price_delta_5m=-0.015102762943634274, price_delta_15m=0.0037000206580624037, volatility_window=0.018930718728960558, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:33:00', 'price_level': 23181.0, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317825, price_delta_1m=0.008467588802984373, price_delta_5m=0.00021403489823271959, price_delta_15m=0.007978434447818365, volatility_window=0.022269708395700326, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:52:00', 'price_level': 23178.25, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=66.0, daily_phase_sin=0.8526401643540923, daily_phase_cos=-0.5224985647159488, session_position=0.4429530201342282, time_to_close=83.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314825, price_delta_1m=0.0023599064884757843, price_delta_5m=0.001889865537103462, price_delta_15m=-0.001364537884018064, volatility_window=0.016384284399776974, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:06:00', 'price_level': 23148.25, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=68.0, daily_phase_sin=0.8480480961564261, daily_phase_cos=-0.5299192642332048, session_position=0.4563758389261745, time_to_close=81.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316025, price_delta_1m=0.0008626854139040845, price_delta_5m=0.0182842783622245, price_delta_15m=-0.012139531603451704, volatility_window=0.020081024981643096, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=36, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:08:00', 'price_level': 23160.25, 'movement_type': 'retracement_high'})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=0.014287692696523122, price_delta_5m=0.0036016405367316527, price_delta_15m=-0.01717071689347743, volatility_window=0.04407312276809225, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=119, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:12:00', 'price_level': 23144.25, 'movement_type': 'premarket_open_price_touch'})",
    "RichNodeFeature(time_minutes=73.0, daily_phase_sin=0.8362861558477594, daily_phase_cos=-0.548293229519914, session_position=0.4899328859060403, time_to_close=76.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314025, price_delta_1m=-0.0006207523102275551, price_delta_5m=0.004340758075944509, price_delta_15m=-0.0007454085568781392, volatility_window=0.04477349960289112, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=53, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:13:00', 'price_level': 23140.25, 'movement_type': 'expansion_low_reversal_point'})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2318625, price_delta_1m=0.0033757310410589946, price_delta_5m=0.0010129957758426755, price_delta_15m=0.006672383222966888, volatility_window=0.02089265342378448, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:22:00', 'price_level': 23186.25, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=92.0, daily_phase_sin=0.788010753606722, daily_phase_cos=-0.6156614753256582, session_position=0.6174496644295302, time_to_close=57.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314675, price_delta_1m=0.01133566040393377, price_delta_5m=0.008505467756774122, price_delta_15m=-0.005574139172022695, volatility_window=0.04365272132359838, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:32:00', 'price_level': 23146.75, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=97.0, daily_phase_sin=0.7743926440821857, daily_phase_cos=-0.6327053285625159, session_position=0.6510067114093959, time_to_close=52.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231775, price_delta_1m=-0.011714290789942558, price_delta_5m=0.0029002072463965574, price_delta_15m=0.006150356009102788, volatility_window=0.015834949636558446, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:37:00', 'price_level': 23177.5, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=137.0, daily_phase_sin=0.6527597524627223, daily_phase_cos=-0.7575649843840497, session_position=0.9194630872483222, time_to_close=12.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319375, price_delta_1m=-0.012678774748518496, price_delta_5m=0.004807404004263069, price_delta_15m=0.01081471653487286, volatility_window=0.04782966796493429, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=61, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:17:00', 'price_level': 23193.75, 'movement_type': 'expansion_high_reversal_point'})",
    "RichNodeFeature(time_minutes=145.0, daily_phase_sin=0.6259234721840592, daily_phase_cos=-0.7798844830928817, session_position=0.9731543624161074, time_to_close=4.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23167, price_delta_1m=-0.004564454555961626, price_delta_5m=-0.0025777062699363124, price_delta_15m=0.01431067827655401, volatility_window=0.02325190808856239, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:25:00', 'price_level': 23167.0, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=-0.006137118169186026, price_delta_5m=0.013108293488204427, price_delta_15m=-0.007384421389607078, volatility_window=0.041850574780617864, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23168.75, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=-93.0, daily_phase_sin=0.9896513868196702, daily_phase_cos=0.1434926219911793, session_position=-0.6241610738255033, time_to_close=242.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.008863190531159047, price_delta_5m=-0.018515443364755076, price_delta_15m=-0.011457706340578615, volatility_window=0.031053196094716393, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=115, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '05:27:00', 'event_type': 'pre_session_takeout', 'liquidity_type': 'cross_session', 'target_level': 'london_session_low', 'magnitude': 'medium', 'context': 'london_session_low_taken_before_premarket_open'})",
    "RichNodeFeature(time_minutes=-89.0, daily_phase_sin=0.992004949679715, daily_phase_cos=0.1261989691358297, session_position=-0.5973154362416108, time_to_close=238.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.008682404675760145, price_delta_5m=0.0014504069790523154, price_delta_15m=0.0003070153904802028, volatility_window=0.04338343893760208, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=120, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.8, structural_importance=0.8, raw_json={'timestamp': '05:31:00', 'event_type': 'pre_session_redelivery', 'liquidity_type': 'cross_session', 'target_level': 'three_day_am_session_fpfvg', 'magnitude': 'high', 'context': 'three_day_am_session_fpfvg_pre_session_redelivery'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.006281257329999995, price_delta_5m=0.006135897586986975, price_delta_15m=-0.01110875080333456, volatility_window=0.04639165780268157, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=40, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:01:00', 'event_type': 'fpfvg_formation', 'liquidity_type': 'native_session', 'target_level': 'premarket_fpfvg_premium_discount', 'magnitude': 'medium', 'context': 'premarket_native_fpfvg_formation_7_00_gap'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.02040840850745556, price_delta_5m=0.004011051754538504, price_delta_15m=-0.005315425728523919, volatility_window=0.04952620408859615, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:01:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'previous_day_lunch_fpfvg_redelivery_premarket_formation'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.023169128871458843, price_delta_5m=0.0010505523576289822, price_delta_15m=0.01149227316176144, volatility_window=0.04524499691432349, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.3, structural_importance=0.3, raw_json={'timestamp': '07:02:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'midnight_opening_range_fpfvg', 'magnitude': 'low', 'context': 'midnight_opening_range_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=9.0, daily_phase_sin=0.9550199444571866, daily_phase_cos=-0.29654157497557104, session_position=0.06040268456375839, time_to_close=140.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.018679926050038303, price_delta_5m=-0.0166984914913364, price_delta_15m=0.0012605945326746569, volatility_window=0.036781323418787835, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:09:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'second_previous_day_lunch_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=10.0, daily_phase_sin=0.9537169507482269, daily_phase_cos=-0.30070579950427295, session_position=0.06711409395973154, time_to_close=139.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.012587512601500022, price_delta_5m=0.010165939878588603, price_delta_15m=0.008888658135058859, volatility_window=0.03130820739041312, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.3, structural_importance=0.3, raw_json={'timestamp': '07:10:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'midnight_opening_range_fpfvg', 'magnitude': 'low', 'context': 'midnight_opening_range_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=30.0, daily_phase_sin=0.9238795325112867, daily_phase_cos=-0.3826834323650897, session_position=0.20134228187919462, time_to_close=119.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.008322534422845765, price_delta_5m=0.011233515944188711, price_delta_15m=-0.00194619410630993, volatility_window=0.03689062543330898, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:30:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.005040024129279012, price_delta_5m=0.005735813819235129, price_delta_15m=-0.002642561616574537, volatility_window=0.048177655261788924, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:33:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'second_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=51.0, daily_phase_sin=0.8849876374630419, daily_phase_cos=-0.4656145203251114, session_position=0.3422818791946309, time_to_close=98.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.015245801084219338, price_delta_5m=-0.002476784748766778, price_delta_15m=0.01009720150416943, volatility_window=0.0395973907288823, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:51:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'third_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=58.0, daily_phase_sin=0.8703556959398997, daily_phase_cos=-0.49242356010346694, session_position=0.38926174496644295, time_to_close=91.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.006051230729814876, price_delta_5m=0.0006851502114086942, price_delta_15m=-0.003907963231298753, volatility_window=0.04314895780055831, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:58:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'third_previous_day_lunch_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=62.0, daily_phase_sin=0.8616291604415257, daily_phase_cos=-0.5075383629607042, session_position=0.4161073825503356, time_to_close=87.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.00030936600828076897, price_delta_5m=0.0010470669085302232, price_delta_15m=-0.010236426156528415, volatility_window=0.018814039118737042, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '08:02:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'previous_day_lunch_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.0028122503261557848, price_delta_5m=0.010204903802513678, price_delta_15m=-0.003510983376748517, volatility_window=0.03881729123474922, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.8, structural_importance=0.8, raw_json={'timestamp': '08:12:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'three_day_am_session_fpfvg', 'magnitude': 'high', 'context': 'three_day_am_session_fpfvg_redelivery_with_open_price'})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=9.25185568057388e-05, price_delta_5m=-0.013258260958558624, price_delta_15m=-0.0045379369908331215, volatility_window=0.015700441599795573, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '08:22:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'london_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=85.0, daily_phase_sin=0.8064446042674828, daily_phase_cos=-0.591309648363582, session_position=0.5704697986577181, time_to_close=64.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.010263620188373892, price_delta_5m=0.0076724879034238795, price_delta_15m=0.0054182427094911344, volatility_window=0.028367624450088606, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '08:25:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'fourth_previous_day_lunch_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=134.0, daily_phase_sin=0.6626200482157374, daily_phase_cos=-0.7489557207890023, session_position=0.8993288590604027, time_to_close=15.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.0031790890534488265, price_delta_5m=0.012184414219526285, price_delta_15m=0.00770752869362121, volatility_window=0.04008344994919412, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '09:14:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'fourth_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=135.0, daily_phase_sin=0.6593458151000686, daily_phase_cos=-0.7518398074789776, session_position=0.9060402684563759, time_to_close=14.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.00874106241150276, price_delta_5m=-0.01466675313543812, price_delta_15m=0.000713156091976716, volatility_window=0.037642228870312266, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '09:15:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'second_london_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=147.0, daily_phase_sin=0.6190939493098342, daily_phase_cos=-0.7853169308807447, session_position=0.9865771812080537, time_to_close=2.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.0056382215659448965, price_delta_5m=0.0109668009891719, price_delta_15m=0.0073104851645056056, volatility_window=0.020889897955290723, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '09:27:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'final_previous_day_lunch_fpfvg_rebalance'})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 1,
        "feature_idx": 0
      },
      {
        "source": 1,
        "target": 2,
        "feature_idx": 1
      },
      {
        "source": 2,
        "target": 3,
        "feature_idx": 2
      },
      {
        "source": 3,
        "target": 4,
        "feature_idx": 3
      },
      {
        "source": 4,
        "target": 5,
        "feature_idx": 4
      },
      {
        "source": 5,
        "target": 6,
        "feature_idx": 5
      },
      {
        "source": 6,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 8,
        "feature_idx": 7
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 8
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 9
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 10
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 11
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 12
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 13
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 14
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 15
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 16
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 17
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 18
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 19
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 20
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 21
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 22
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 23
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 24
      },
      {
        "source": 26,
        "target": 27,
        "feature_idx": 25
      },
      {
        "source": 27,
        "target": 28,
        "feature_idx": 26
      },
      {
        "source": 28,
        "target": 29,
        "feature_idx": 27
      },
      {
        "source": 29,
        "target": 30,
        "feature_idx": 28
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 29
      },
      {
        "source": 31,
        "target": 32,
        "feature_idx": 30
      },
      {
        "source": 32,
        "target": 33,
        "feature_idx": 31
      },
      {
        "source": 33,
        "target": 34,
        "feature_idx": 32
      },
      {
        "source": 34,
        "target": 35,
        "feature_idx": 33
      },
      {
        "source": 35,
        "target": 36,
        "feature_idx": 34
      },
      {
        "source": 36,
        "target": 37,
        "feature_idx": 35
      },
      {
        "source": 37,
        "target": 38,
        "feature_idx": 36
      },
      {
        "source": 38,
        "target": 39,
        "feature_idx": 37
      },
      {
        "source": 39,
        "target": 40,
        "feature_idx": 38
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [],
    "temporal_echo": [
      {
        "source": 0,
        "target": 25,
        "feature_idx": 39
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 40
      },
      {
        "source": 0,
        "target": 27,
        "feature_idx": 41
      },
      {
        "source": 0,
        "target": 28,
        "feature_idx": 42
      },
      {
        "source": 0,
        "target": 29,
        "feature_idx": 43
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 44
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 45
      },
      {
        "source": 1,
        "target": 27,
        "feature_idx": 46
      },
      {
        "source": 1,
        "target": 28,
        "feature_idx": 47
      },
      {
        "source": 1,
        "target": 29,
        "feature_idx": 48
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 49
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 50
      },
      {
        "source": 2,
        "target": 27,
        "feature_idx": 51
      },
      {
        "source": 2,
        "target": 28,
        "feature_idx": 52
      },
      {
        "source": 2,
        "target": 29,
        "feature_idx": 53
      },
      {
        "source": 2,
        "target": 30,
        "feature_idx": 54
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 55
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 56
      },
      {
        "source": 3,
        "target": 27,
        "feature_idx": 57
      },
      {
        "source": 3,
        "target": 28,
        "feature_idx": 58
      },
      {
        "source": 3,
        "target": 29,
        "feature_idx": 59
      },
      {
        "source": 3,
        "target": 30,
        "feature_idx": 60
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 61
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 62
      },
      {
        "source": 4,
        "target": 27,
        "feature_idx": 63
      },
      {
        "source": 4,
        "target": 28,
        "feature_idx": 64
      },
      {
        "source": 4,
        "target": 29,
        "feature_idx": 65
      },
      {
        "source": 4,
        "target": 30,
        "feature_idx": 66
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 67
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 68
      },
      {
        "source": 5,
        "target": 27,
        "feature_idx": 69
      },
      {
        "source": 5,
        "target": 28,
        "feature_idx": 70
      },
      {
        "source": 5,
        "target": 29,
        "feature_idx": 71
      },
      {
        "source": 5,
        "target": 30,
        "feature_idx": 72
      },
      {
        "source": 5,
        "target": 31,
        "feature_idx": 73
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 74
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 75
      },
      {
        "source": 6,
        "target": 27,
        "feature_idx": 76
      },
      {
        "source": 6,
        "target": 28,
        "feature_idx": 77
      },
      {
        "source": 6,
        "target": 29,
        "feature_idx": 78
      },
      {
        "source": 6,
        "target": 30,
        "feature_idx": 79
      },
      {
        "source": 6,
        "target": 31,
        "feature_idx": 80
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 81
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 82
      },
      {
        "source": 7,
        "target": 27,
        "feature_idx": 83
      },
      {
        "source": 7,
        "target": 28,
        "feature_idx": 84
      },
      {
        "source": 7,
        "target": 29,
        "feature_idx": 85
      },
      {
        "source": 7,
        "target": 30,
        "feature_idx": 86
      },
      {
        "source": 7,
        "target": 31,
        "feature_idx": 87
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 88
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 89
      },
      {
        "source": 8,
        "target": 27,
        "feature_idx": 90
      },
      {
        "source": 8,
        "target": 28,
        "feature_idx": 91
      },
      {
        "source": 8,
        "target": 29,
        "feature_idx": 92
      },
      {
        "source": 8,
        "target": 30,
        "feature_idx": 93
      },
      {
        "source": 8,
        "target": 31,
        "feature_idx": 94
      },
      {
        "source": 8,
        "target": 32,
        "feature_idx": 95
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 96
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 97
      },
      {
        "source": 9,
        "target": 27,
        "feature_idx": 98
      },
      {
        "source": 9,
        "target": 28,
        "feature_idx": 99
      },
      {
        "source": 9,
        "target": 29,
        "feature_idx": 100
      },
      {
        "source": 9,
        "target": 30,
        "feature_idx": 101
      },
      {
        "source": 9,
        "target": 31,
        "feature_idx": 102
      },
      {
        "source": 9,
        "target": 32,
        "feature_idx": 103
      },
      {
        "source": 10,
        "target": 28,
        "feature_idx": 104
      },
      {
        "source": 10,
        "target": 29,
        "feature_idx": 105
      },
      {
        "source": 10,
        "target": 30,
        "feature_idx": 106
      },
      {
        "source": 10,
        "target": 31,
        "feature_idx": 107
      },
      {
        "source": 10,
        "target": 32,
        "feature_idx": 108
      },
      {
        "source": 10,
        "target": 33,
        "feature_idx": 109
      },
      {
        "source": 11,
        "target": 28,
        "feature_idx": 110
      },
      {
        "source": 11,
        "target": 29,
        "feature_idx": 111
      },
      {
        "source": 11,
        "target": 30,
        "feature_idx": 112
      },
      {
        "source": 11,
        "target": 31,
        "feature_idx": 113
      },
      {
        "source": 11,
        "target": 32,
        "feature_idx": 114
      },
      {
        "source": 11,
        "target": 33,
        "feature_idx": 115
      },
      {
        "source": 11,
        "target": 34,
        "feature_idx": 116
      },
      {
        "source": 12,
        "target": 30,
        "feature_idx": 117
      },
      {
        "source": 12,
        "target": 31,
        "feature_idx": 118
      },
      {
        "source": 12,
        "target": 32,
        "feature_idx": 119
      },
      {
        "source": 12,
        "target": 33,
        "feature_idx": 120
      },
      {
        "source": 12,
        "target": 34,
        "feature_idx": 121
      },
      {
        "source": 12,
        "target": 35,
        "feature_idx": 122
      },
      {
        "source": 13,
        "target": 32,
        "feature_idx": 123
      },
      {
        "source": 13,
        "target": 33,
        "feature_idx": 124
      },
      {
        "source": 13,
        "target": 34,
        "feature_idx": 125
      },
      {
        "source": 13,
        "target": 35,
        "feature_idx": 126
      },
      {
        "source": 13,
        "target": 36,
        "feature_idx": 127
      },
      {
        "source": 13,
        "target": 37,
        "feature_idx": 128
      },
      {
        "source": 14,
        "target": 32,
        "feature_idx": 129
      },
      {
        "source": 14,
        "target": 33,
        "feature_idx": 130
      },
      {
        "source": 14,
        "target": 34,
        "feature_idx": 131
      },
      {
        "source": 14,
        "target": 35,
        "feature_idx": 132
      },
      {
        "source": 14,
        "target": 36,
        "feature_idx": 133
      },
      {
        "source": 14,
        "target": 37,
        "feature_idx": 134
      },
      {
        "source": 15,
        "target": 32,
        "feature_idx": 135
      },
      {
        "source": 15,
        "target": 33,
        "feature_idx": 136
      },
      {
        "source": 15,
        "target": 34,
        "feature_idx": 137
      },
      {
        "source": 15,
        "target": 35,
        "feature_idx": 138
      },
      {
        "source": 15,
        "target": 36,
        "feature_idx": 139
      },
      {
        "source": 15,
        "target": 37,
        "feature_idx": 140
      },
      {
        "source": 16,
        "target": 32,
        "feature_idx": 141
      },
      {
        "source": 16,
        "target": 33,
        "feature_idx": 142
      },
      {
        "source": 16,
        "target": 34,
        "feature_idx": 143
      },
      {
        "source": 16,
        "target": 35,
        "feature_idx": 144
      },
      {
        "source": 16,
        "target": 36,
        "feature_idx": 145
      },
      {
        "source": 16,
        "target": 37,
        "feature_idx": 146
      },
      {
        "source": 17,
        "target": 33,
        "feature_idx": 147
      },
      {
        "source": 17,
        "target": 34,
        "feature_idx": 148
      },
      {
        "source": 17,
        "target": 35,
        "feature_idx": 149
      },
      {
        "source": 17,
        "target": 36,
        "feature_idx": 150
      },
      {
        "source": 17,
        "target": 37,
        "feature_idx": 151
      },
      {
        "source": 18,
        "target": 35,
        "feature_idx": 152
      },
      {
        "source": 18,
        "target": 36,
        "feature_idx": 153
      },
      {
        "source": 18,
        "target": 37,
        "feature_idx": 154
      },
      {
        "source": 19,
        "target": 35,
        "feature_idx": 155
      },
      {
        "source": 19,
        "target": 36,
        "feature_idx": 156
      },
      {
        "source": 19,
        "target": 37,
        "feature_idx": 157
      },
      {
        "source": 20,
        "target": 38,
        "feature_idx": 158
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 159
      },
      {
        "source": 20,
        "target": 40,
        "feature_idx": 160
      },
      {
        "source": 21,
        "target": 38,
        "feature_idx": 161
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 162
      },
      {
        "source": 21,
        "target": 40,
        "feature_idx": 163
      },
      {
        "source": 22,
        "target": 38,
        "feature_idx": 164
      },
      {
        "source": 22,
        "target": 39,
        "feature_idx": 165
      },
      {
        "source": 22,
        "target": 40,
        "feature_idx": 166
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    