{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18
    ],
    "5m": [
      19,
      20,
      21,
      22
    ],
    "15m": [
      23,
      24
    ],
    "1h": [
      25
    ],
    "D": [
      26
    ],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23599, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'price_level': 23599.0, 'open': 23599.0, 'high': 23599.0, 'low': 23599.0, 'close': 23599.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.236135, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '12:00:00', 'price_level': 23613.5, 'open': 23613.5, 'high': 23613.5, 'low': 23613.5, 'close': 23613.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235725, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '12:30:00', 'price_level': 23572.5, 'open': 23572.5, 'high': 23572.5, 'low': 23572.5, 'close': 23572.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235955, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '09:29:00', 'price_level': 23595.5, 'open': 23595.5, 'high': 23595.5, 'low': 23595.5, 'close': 23595.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23599, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 4, 'timestamp': '07:00:00', 'price_level': 23599.0, 'open': 23599.0, 'high': 23599.0, 'low': 23599.0, 'close': 23599.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.236135, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 5, 'timestamp': '12:00:00', 'price_level': 23613.5, 'open': 23613.5, 'high': 23613.5, 'low': 23613.5, 'close': 23613.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235725, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 6, 'timestamp': '12:30:00', 'price_level': 23572.5, 'open': 23572.5, 'high': 23572.5, 'low': 23572.5, 'close': 23572.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235955, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 7, 'timestamp': '09:29:00', 'price_level': 23595.5, 'open': 23595.5, 'high': 23595.5, 'low': 23595.5, 'close': 23595.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 8, 'timestamp': '07:01:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Expansion phase lower initiated from opening', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 9, 'timestamp': '07:02:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market First Presentation FVG premium high created', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=40.0, daily_phase_sin=0.90630778703665, daily_phase_cos=-0.42261826174069933, session_position=0.2684563758389262, time_to_close=109.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 10, 'timestamp': '07:40:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Reversal point during expansion lower', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=61.0, daily_phase_sin=0.8638355052043958, daily_phase_cos=-0.5037739770455262, session_position=0.40939597315436244, time_to_close=88.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 11, 'timestamp': '08:01:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market First Presentation FVG balanced', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=67.0, daily_phase_sin=0.8503522249955631, daily_phase_cos=-0.5262139236518693, session_position=0.44966442953020136, time_to_close=82.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 12, 'timestamp': '08:07:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market session high created', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=71.0, daily_phase_sin=0.8410390129643925, daily_phase_cos=-0.5409744713679938, session_position=0.47651006711409394, time_to_close=78.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 13, 'timestamp': '08:11:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market First Presentation FVG redelivered', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=84.0, daily_phase_sin=0.8090169943749475, daily_phase_cos=-0.587785252292473, session_position=0.5637583892617449, time_to_close=65.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 14, 'timestamp': '08:24:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market First Presentation FVG redelivered again', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=115.0, daily_phase_sin=0.7223639620597557, daily_phase_cos=-0.6915130557822693, session_position=0.7718120805369127, time_to_close=34.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 15, 'timestamp': '08:55:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'London session high taken out between sessions', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=117.0, daily_phase_sin=0.7163019434246545, daily_phase_cos=-0.69779045984168, session_position=0.785234899328859, time_to_close=32.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 16, 'timestamp': '08:57:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': \"Pre-market session low created, redelivers previous day's London FVG\", 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=144.0, daily_phase_sin=0.6293203910498377, daily_phase_cos=-0.7771459614569707, session_position=0.9664429530201343, time_to_close=5.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 17, 'timestamp': '09:24:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Touches London opening price during expansion', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 18, 'timestamp': '09:29:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Final redelivery of Pre-market First Presentation FVG at close', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23599, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0017373617526166363, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23572.5, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.000614432814949786, structural_importance=0.75, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23599.0, 'high': 23613.5, 'low': 23572.5, 'close': 23599.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23613.5}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23572.5}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 41.0}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23572.5, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=0.5, raw_json={'id': 1, 'timestamp': '12:00:00', 'open': 23613.5, 'high': 23613.5, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23572.5}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23572.5}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 23613.5}})",
    "RichNodeFeature(time_minutes=40.0, daily_phase_sin=0.90630778703665, daily_phase_cos=-0.42261826174069933, session_position=0.2684563758389262, time_to_close=109.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '07:40:00', 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': None, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 0}})",
    "RichNodeFeature(time_minutes=115.0, daily_phase_sin=0.7223639620597557, daily_phase_cos=-0.6915130557822693, session_position=0.7718120805369127, time_to_close=34.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.4, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '08:55:00', 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 4, 'pd_array': None, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 4, 'period_minutes': 5, 'price_range': 0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23572.5, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=4.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23599.0, 'high': 23613.5, 'low': 0, 'close': 0, 'timeframe': '15m', 'source_movements': 15, 'pd_array': {'type': 'swing_high', 'level': 23613.5}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23572.5}, 'meta': {'coverage': 15, 'period_minutes': 15, 'price_range': 23613.5}})",
    "RichNodeFeature(time_minutes=115.0, daily_phase_sin=0.7223639620597557, daily_phase_cos=-0.6915130557822693, session_position=0.7718120805369127, time_to_close=34.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.4, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '08:55:00', 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'timeframe': '15m', 'source_movements': 4, 'pd_array': None, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 4, 'period_minutes': 15, 'price_range': 0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=23572.5, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=5.699999999999999, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23599.0, 'high': 23613.5, 'low': 0, 'close': 0, 'timeframe': '60m', 'source_movements': 19, 'pd_array': {'type': 'swing_high', 'level': 23613.5}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23572.5}, 'meta': {'coverage': 19, 'period_minutes': 60, 'price_range': 23613.5}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=4, liquidity_type=0, fpfvg_gap_size=23572.5, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=5.699999999999999, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23599.0, 'high': 23613.5, 'low': 0, 'close': 0, 'timeframe': '1440m', 'source_movements': 19, 'pd_array': {'type': 'swing_high', 'level': 23613.5}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23572.5}, 'meta': {'coverage': 19, 'period_minutes': 1440, 'price_range': 23613.5}})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 5
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 6
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 7
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 8
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 9
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 10
      },
      {
        "source": 17,
        "target": 3,
        "feature_idx": 11
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 12
      },
      {
        "source": 7,
        "target": 18,
        "feature_idx": 13
      },
      {
        "source": 18,
        "target": 1,
        "feature_idx": 14
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 15
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 16
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 17
      },
      {
        "source": 19,
        "target": 21,
        "feature_idx": 18
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 19
      },
      {
        "source": 22,
        "target": 20,
        "feature_idx": 20
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 21
      }
    ],
    "scale": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 22
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 23
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 24
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 25
      },
      {
        "source": 12,
        "target": 21,
        "feature_idx": 26
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 27
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 28
      },
      {
        "source": 17,
        "target": 22,
        "feature_idx": 29
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 30
      },
      {
        "source": 23,
        "target": 25,
        "feature_idx": 31
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 32
      },
      {
        "source": 0,
        "target": 19,
        "feature_idx": 33,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 20,
        "feature_idx": 34,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23572.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 35,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 36,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 37,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 38,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23572.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 19,
        "feature_idx": 39,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 19,
        "feature_idx": 40,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 41,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 42,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 43,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 19,
        "feature_idx": 44,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 19,
        "feature_idx": 45,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 19,
        "feature_idx": 46,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 19,
        "feature_idx": 47,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 48,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 49,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 19,
        "feature_idx": 50,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 51,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 52,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 53,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 54,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 55,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 56,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 57,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 58,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 23,
        "feature_idx": 59,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 60,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 61,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 23,
        "feature_idx": 62,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 23,
        "feature_idx": 63,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 64,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 65,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 66,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 67,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 68,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 23,
        "feature_idx": 69,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 23,
        "feature_idx": 70,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 25,
        "feature_idx": 71,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 72,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 73,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 74,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 75,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 76,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 77,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 78,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 79,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 80,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 81,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 82,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 83,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 25,
        "feature_idx": 84,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 25,
        "feature_idx": 85,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 25,
        "feature_idx": 86,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 25,
        "feature_idx": 87,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 25,
        "feature_idx": 88,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 25,
        "feature_idx": 89,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 90,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 91,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 92,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 93,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 94,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 95,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 96,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 97,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 98,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 99,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 26,
        "feature_idx": 100,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 26,
        "feature_idx": 101,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 102,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 103,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 104,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 105,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 106,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 107,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 108,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23613.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23572.5
          },
          "liquidity_sweep": true
        }
      }
    ],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 109
      },
      {
        "source": 1,
        "target": 19,
        "feature_idx": 110
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 111
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 112
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 113
      },
      {
        "source": 5,
        "target": 19,
        "feature_idx": 114
      },
      {
        "source": 6,
        "target": 19,
        "feature_idx": 115
      },
      {
        "source": 7,
        "target": 19,
        "feature_idx": 116
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 117
      },
      {
        "source": 8,
        "target": 21,
        "feature_idx": 118
      },
      {
        "source": 8,
        "target": 22,
        "feature_idx": 119
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 120
      },
      {
        "source": 8,
        "target": 24,
        "feature_idx": 121
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 122
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 123
      },
      {
        "source": 9,
        "target": 20,
        "feature_idx": 124
      },
      {
        "source": 9,
        "target": 21,
        "feature_idx": 125
      },
      {
        "source": 9,
        "target": 22,
        "feature_idx": 126
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 127
      },
      {
        "source": 9,
        "target": 24,
        "feature_idx": 128
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 129
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 130
      },
      {
        "source": 10,
        "target": 20,
        "feature_idx": 131
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 132
      },
      {
        "source": 10,
        "target": 22,
        "feature_idx": 133
      },
      {
        "source": 10,
        "target": 23,
        "feature_idx": 134
      },
      {
        "source": 10,
        "target": 24,
        "feature_idx": 135
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 136
      },
      {
        "source": 10,
        "target": 26,
        "feature_idx": 137
      },
      {
        "source": 11,
        "target": 20,
        "feature_idx": 138
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 139
      },
      {
        "source": 11,
        "target": 22,
        "feature_idx": 140
      },
      {
        "source": 11,
        "target": 23,
        "feature_idx": 141
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 142
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 143
      },
      {
        "source": 11,
        "target": 26,
        "feature_idx": 144
      },
      {
        "source": 12,
        "target": 20,
        "feature_idx": 145
      },
      {
        "source": 12,
        "target": 21,
        "feature_idx": 146
      },
      {
        "source": 12,
        "target": 22,
        "feature_idx": 147
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 148
      },
      {
        "source": 12,
        "target": 24,
        "feature_idx": 149
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 150
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 151
      },
      {
        "source": 13,
        "target": 20,
        "feature_idx": 152
      },
      {
        "source": 13,
        "target": 21,
        "feature_idx": 153
      },
      {
        "source": 13,
        "target": 22,
        "feature_idx": 154
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 155
      },
      {
        "source": 13,
        "target": 24,
        "feature_idx": 156
      },
      {
        "source": 13,
        "target": 25,
        "feature_idx": 157
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 158
      },
      {
        "source": 14,
        "target": 20,
        "feature_idx": 159
      },
      {
        "source": 14,
        "target": 21,
        "feature_idx": 160
      },
      {
        "source": 14,
        "target": 22,
        "feature_idx": 161
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 162
      },
      {
        "source": 14,
        "target": 24,
        "feature_idx": 163
      },
      {
        "source": 14,
        "target": 25,
        "feature_idx": 164
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 165
      },
      {
        "source": 15,
        "target": 20,
        "feature_idx": 166
      },
      {
        "source": 15,
        "target": 21,
        "feature_idx": 167
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 168
      },
      {
        "source": 15,
        "target": 23,
        "feature_idx": 169
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 170
      },
      {
        "source": 15,
        "target": 25,
        "feature_idx": 171
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 172
      },
      {
        "source": 16,
        "target": 20,
        "feature_idx": 173
      },
      {
        "source": 16,
        "target": 21,
        "feature_idx": 174
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 175
      },
      {
        "source": 16,
        "target": 23,
        "feature_idx": 176
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 177
      },
      {
        "source": 16,
        "target": 25,
        "feature_idx": 178
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 179
      },
      {
        "source": 17,
        "target": 20,
        "feature_idx": 180
      },
      {
        "source": 17,
        "target": 21,
        "feature_idx": 181
      },
      {
        "source": 17,
        "target": 22,
        "feature_idx": 182
      },
      {
        "source": 17,
        "target": 23,
        "feature_idx": 183
      },
      {
        "source": 17,
        "target": 24,
        "feature_idx": 184
      },
      {
        "source": 17,
        "target": 25,
        "feature_idx": 185
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 186
      },
      {
        "source": 18,
        "target": 20,
        "feature_idx": 187
      },
      {
        "source": 18,
        "target": 21,
        "feature_idx": 188
      },
      {
        "source": 18,
        "target": 22,
        "feature_idx": 189
      },
      {
        "source": 18,
        "target": 23,
        "feature_idx": 190
      },
      {
        "source": 18,
        "target": 24,
        "feature_idx": 191
      },
      {
        "source": 18,
        "target": 25,
        "feature_idx": 192
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 193
      },
      {
        "source": 20,
        "target": 23,
        "feature_idx": 194
      },
      {
        "source": 20,
        "target": 24,
        "feature_idx": 195
      },
      {
        "source": 20,
        "target": 25,
        "feature_idx": 196
      },
      {
        "source": 20,
        "target": 26,
        "feature_idx": 197
      },
      {
        "source": 21,
        "target": 23,
        "feature_idx": 198
      },
      {
        "source": 21,
        "target": 24,
        "feature_idx": 199
      },
      {
        "source": 21,
        "target": 25,
        "feature_idx": 200
      },
      {
        "source": 21,
        "target": 26,
        "feature_idx": 201
      },
      {
        "source": 22,
        "target": 23,
        "feature_idx": 202
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 203
      },
      {
        "source": 22,
        "target": 25,
        "feature_idx": 204
      },
      {
        "source": 22,
        "target": 26,
        "feature_idx": 205
      },
      {
        "source": 23,
        "target": 25,
        "feature_idx": 206
      },
      {
        "source": 23,
        "target": 26,
        "feature_idx": 207
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 208
      },
      {
        "source": 24,
        "target": 26,
        "feature_idx": 209
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 210
      }
    ],
    "temporal_echo": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 211
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 212
      },
      {
        "source": 0,
        "target": 25,
        "feature_idx": 213
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 214
      },
      {
        "source": 1,
        "target": 20,
        "feature_idx": 215
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 216
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 217
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 218
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 219
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 220
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 221
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 222
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 223
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 224
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 225
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 226
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 227
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 228
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 229
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 230
      },
      {
        "source": 12,
        "target": 21,
        "feature_idx": 231
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 232
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 233
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 234
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 235
      },
      {
        "source": 17,
        "target": 22,
        "feature_idx": 236
      },
      {
        "source": 17,
        "target": 24,
        "feature_idx": 237
      },
      {
        "source": 19,
        "target": 23,
        "feature_idx": 238
      },
      {
        "source": 19,
        "target": 25,
        "feature_idx": 239
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 240
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 241
      },
      {
        "source": 23,
        "target": 25,
        "feature_idx": 242
      },
      {
        "source": 23,
        "target": 26,
        "feature_idx": 243
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 244
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    