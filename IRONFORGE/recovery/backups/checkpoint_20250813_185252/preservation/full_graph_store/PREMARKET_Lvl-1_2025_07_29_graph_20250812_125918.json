{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18
    ],
    "5m": [],
    "15m": [],
    "1h": [
      19,
      20
    ],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23599, price_delta_1m=-0.007369624558751267, price_delta_5m=0.01110907236602079, price_delta_15m=0.015438319769499085, volatility_window=0.029582090701974123, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23599.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.236135, price_delta_1m=-0.005448544443597261, price_delta_5m=-0.0030826495107494677, price_delta_15m=-0.0043695110214046476, volatility_window=0.010942108340135058, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23613.5, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235725, price_delta_1m=-0.014972835566573568, price_delta_5m=0.0003271037200551052, price_delta_15m=0.002374682512489856, volatility_window=0.02523903012384756, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23572.5, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235955, price_delta_1m=-0.012932373800604254, price_delta_5m=-0.002601947636063513, price_delta_15m=-0.010638905700167997, volatility_window=0.021706901855101792, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23595.5, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23599, price_delta_1m=-0.0076355146148476575, price_delta_5m=0.0017708509575258464, price_delta_15m=0.008332151766854631, volatility_window=0.029250822431462924, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23599.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.236135, price_delta_1m=0.016477347811852602, price_delta_5m=-0.007631715973289571, price_delta_15m=0.0063535769882590715, volatility_window=0.026134982318942644, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23613.5, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235725, price_delta_1m=0.00941861767606914, price_delta_5m=0.0035813195185273555, price_delta_15m=-0.0013722142101974778, volatility_window=0.04856888348652959, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23572.5, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235955, price_delta_1m=-0.0034189570997204596, price_delta_5m=0.009794780995046177, price_delta_15m=0.01147141637680016, volatility_window=0.048979215380053384, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23595.5, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.2360675, price_delta_1m=-0.007229482347182519, price_delta_5m=-0.0087926996716258, price_delta_15m=-0.000554496923853001, volatility_window=0.02109029826824087, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price': 23606.75, 'action': 'break', 'context': 'Expansion phase lower initiated from opening'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.2360275, price_delta_1m=0.001188730163863392, price_delta_5m=-0.027230894226691546, price_delta_15m=-0.008618790005525123, volatility_window=0.04046416392237192, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:02:00', 'price': 23602.75, 'action': 'touch', 'context': 'Pre-market First Presentation FVG premium high created'})",
    "RichNodeFeature(time_minutes=40.0, daily_phase_sin=0.90630778703665, daily_phase_cos=-0.42261826174069933, session_position=0.2684563758389262, time_to_close=109.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235785, price_delta_1m=0.007425963217423389, price_delta_5m=0.013659879454042494, price_delta_15m=-0.018787076710303607, volatility_window=0.04946164775516203, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:40:00', 'price': 23578.5, 'action': 'touch', 'context': 'Reversal point during expansion lower'})",
    "RichNodeFeature(time_minutes=61.0, daily_phase_sin=0.8638355052043958, daily_phase_cos=-0.5037739770455262, session_position=0.40939597315436244, time_to_close=88.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23601875, price_delta_1m=0.0072712475745611614, price_delta_5m=-0.022242065821286863, price_delta_15m=-0.004548819311109167, volatility_window=0.02644876904260411, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:01:00', 'price': 23601.875, 'action': 'touch', 'context': 'Pre-market First Presentation FVG balanced'})",
    "RichNodeFeature(time_minutes=67.0, daily_phase_sin=0.8503522249955631, daily_phase_cos=-0.5262139236518693, session_position=0.44966442953020136, time_to_close=82.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.236135, price_delta_1m=0.005411117725173719, price_delta_5m=0.001352424471635957, price_delta_15m=0.011835433256942238, volatility_window=0.04711897687729498, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:07:00', 'price': 23613.5, 'action': 'touch', 'context': 'Pre-market session high created'})",
    "RichNodeFeature(time_minutes=71.0, daily_phase_sin=0.8410390129643925, daily_phase_cos=-0.5409744713679938, session_position=0.47651006711409394, time_to_close=78.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23602125, price_delta_1m=-0.003345158449533516, price_delta_5m=0.013627662763110584, price_delta_15m=0.008349760142856886, volatility_window=0.027180640563062554, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:11:00', 'price': 23602.125, 'action': 'delivery', 'context': 'Pre-market First Presentation FVG redelivered'})",
    "RichNodeFeature(time_minutes=84.0, daily_phase_sin=0.8090169943749475, daily_phase_cos=-0.587785252292473, session_position=0.5637583892617449, time_to_close=65.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.2360175, price_delta_1m=0.007543122334979956, price_delta_5m=0.003347145255780714, price_delta_15m=-0.008923121853575796, volatility_window=0.04145473600434899, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:24:00', 'price': 23601.75, 'action': 'delivery', 'context': 'Pre-market First Presentation FVG redelivered again'})",
    "RichNodeFeature(time_minutes=115.0, daily_phase_sin=0.7223639620597557, daily_phase_cos=-0.6915130557822693, session_position=0.7718120805369127, time_to_close=34.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235825, price_delta_1m=-0.008542246575030107, price_delta_5m=-0.005239399607580439, price_delta_15m=-0.006311887347383724, volatility_window=0.016810142610731608, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:55:00', 'price': 23582.5, 'action': 'sweep', 'context': 'London session high taken out between sessions'})",
    "RichNodeFeature(time_minutes=117.0, daily_phase_sin=0.7163019434246545, daily_phase_cos=-0.69779045984168, session_position=0.785234899328859, time_to_close=32.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.235725, price_delta_1m=-0.009436914527527509, price_delta_5m=0.009587406319411471, price_delta_15m=-0.0011238812198543103, volatility_window=0.03957541132447957, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:57:00', 'price': 23572.5, 'action': 'touch', 'context': \"Pre-market session low created, redelivers previous day's London FVG\"})",
    "RichNodeFeature(time_minutes=144.0, daily_phase_sin=0.6293203910498377, daily_phase_cos=-0.7771459614569707, session_position=0.9664429530201343, time_to_close=5.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23599, price_delta_1m=-0.022031420984139346, price_delta_5m=-0.008439722112303259, price_delta_15m=-0.004221882917845629, volatility_window=0.01372193399726999, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:24:00', 'price': 23599.0, 'action': 'touch', 'context': 'Touches London opening price during expansion'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.23602, price_delta_1m=0.011615590385105725, price_delta_5m=0.002001202659073523, price_delta_15m=0.002687725443065636, volatility_window=0.04680184431557905, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price': 23602.0, 'action': 'delivery', 'context': 'Final redelivery of Pre-market First Presentation FVG at close'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=-0.03527731579174479, price_delta_5m=0.014739340603604871, price_delta_15m=0.0038362010930670393, volatility_window=0.01378271052801284, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 2, 'trigger_source': 'Price Movement'})",
    "RichNodeFeature(time_minutes=99.0, daily_phase_sin=0.7688418320734597, daily_phase_cos=-0.6394390019805846, session_position=0.6644295302013423, time_to_close=50.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753743600, day_of_week=1, month_phase=0.9354838709677419, normalized_price=0.0, price_delta_1m=-0.0005861223068140404, price_delta_5m=-0.009860716319912864, price_delta_15m=-0.0054583163359110546, volatility_window=0.025104553660647327, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:39:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 5
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 6
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 7
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 8
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 9
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 10
      },
      {
        "source": 17,
        "target": 3,
        "feature_idx": 11
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 12
      },
      {
        "source": 7,
        "target": 18,
        "feature_idx": 13
      },
      {
        "source": 18,
        "target": 1,
        "feature_idx": 14
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 15
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 16
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 17
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 18
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [],
    "temporal_echo": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 19
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 20
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 21
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 22
      },
      {
        "source": 13,
        "target": 20,
        "feature_idx": 23
      },
      {
        "source": 14,
        "target": 20,
        "feature_idx": 24
      },
      {
        "source": 15,
        "target": 20,
        "feature_idx": 25
      },
      {
        "source": 16,
        "target": 20,
        "feature_idx": 26
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    