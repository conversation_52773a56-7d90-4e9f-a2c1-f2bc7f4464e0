{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11
    ],
    "5m": [],
    "15m": [],
    "1h": [
      12,
      13,
      14,
      15
    ],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=-0.007573095557930069, price_delta_5m=-0.010159196735653424, price_delta_15m=0.013187040533010301, volatility_window=0.04173554113382929, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23388.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23389, price_delta_1m=0.007765755855847584, price_delta_5m=0.00200126419136034, price_delta_15m=-0.0010876279172188853, volatility_window=0.03618665302673379, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23389.0, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.012184534334191626, price_delta_5m=0.01536364055311715, price_delta_15m=0.0030398517221725038, volatility_window=0.04212105786991314, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23346.75, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.233605, price_delta_1m=0.00469520489164534, price_delta_5m=-0.0023267899543971756, price_delta_15m=-0.02212906319923968, volatility_window=0.023041225355957103, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23360.5, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=-0.00014963407244278826, price_delta_5m=0.007181515510519751, price_delta_15m=-0.0013119161886720235, volatility_window=0.04231243182151899, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23388.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23389, price_delta_1m=0.0013508834239128998, price_delta_5m=-0.003271823772161538, price_delta_15m=-0.010682851495671065, volatility_window=0.024303703370243648, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23389.0, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.015863974629177605, price_delta_5m=-0.00019452656456224728, price_delta_15m=0.003918456515527021, volatility_window=0.04559223608554622, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23346.75, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.233605, price_delta_1m=8.935509504464231e-05, price_delta_5m=0.012824255904213013, price_delta_15m=0.0013437137774806973, volatility_window=0.04489198512958731, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23360.5, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2338525, price_delta_1m=-0.014190487851818047, price_delta_5m=-0.005848365095410213, price_delta_15m=0.014679962928241126, volatility_window=0.01041787961387823, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price': 23385.25, 'action': 'touch', 'context': 'PreMarket_FPFVG_formation_premium_high'})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2336075, price_delta_1m=-0.006812482390248016, price_delta_5m=-0.010545175153497087, price_delta_15m=-0.0069756697229181866, volatility_window=0.021211903365567646, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:52:00', 'price': 23360.75, 'action': 'touch', 'context': 'initial_expansion_low_formation'})",
    "RichNodeFeature(time_minutes=93.0, daily_phase_sin=0.7853169308807448, daily_phase_cos=-0.6190939493098341, session_position=0.6241610738255033, time_to_close=56.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2338625, price_delta_1m=-0.001218654197514448, price_delta_5m=-0.00254369909667048, price_delta_15m=0.0006548703444439956, volatility_window=0.048670727739290064, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:33:00', 'price': 23386.25, 'action': 'touch', 'context': 'retracement_high_and_FVG_rebalance'})",
    "RichNodeFeature(time_minutes=125.0, daily_phase_sin=0.6915130557822694, daily_phase_cos=-0.7223639620597555, session_position=0.8389261744966443, time_to_close=24.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=-0.00340949027705668, price_delta_5m=0.0009628001876006868, price_delta_15m=-0.00742077679722348, volatility_window=0.02984817798431564, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:05:00', 'price': 23346.75, 'action': 'break', 'context': 'London_session_low_taken_out_creating_session_low'})",
    "RichNodeFeature(time_minutes=43.0, daily_phase_sin=0.9006982393225879, daily_phase_cos=-0.43444525740441703, session_position=0.28859060402684567, time_to_close=106.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2337975, price_delta_1m=0.015930873307505707, price_delta_5m=-0.010516628363331924, price_delta_15m=-0.008061964171818328, volatility_window=0.014971238249791553, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=14, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=19.0, structural_importance=19.0, raw_json={'timestamp': '07:43:00', 'event_type': 'expansion_phase', 'price_level': 23379.75, 'magnitude': 19.0, 'duration_minutes': 9.0})",
    "RichNodeFeature(time_minutes=76.0, daily_phase_sin=0.8290375725550418, daily_phase_cos=-0.5591929034707467, session_position=0.5100671140939598, time_to_close=73.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2336125, price_delta_1m=-0.009925000823923035, price_delta_5m=0.002395969286349875, price_delta_15m=0.0031614026773366874, volatility_window=0.04522610455175771, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=15, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=25.0, structural_importance=25.0, raw_json={'timestamp': '08:16:00', 'event_type': 'retracement_expansion', 'price_level': 23361.25, 'magnitude': 25.0, 'duration_minutes': 17.0})",
    "RichNodeFeature(time_minutes=93.0, daily_phase_sin=0.7853169308807448, daily_phase_cos=-0.6190939493098341, session_position=0.6241610738255033, time_to_close=56.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2338625, price_delta_1m=0.004392113779606016, price_delta_5m=-0.009236007349415602, price_delta_15m=0.0037929220903279897, volatility_window=0.041721872181655836, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=14, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=19.5, structural_importance=19.5, raw_json={'timestamp': '08:33:00', 'event_type': 'expansion_phase', 'price_level': 23386.25, 'magnitude': 19.5, 'duration_minutes': 17.0})",
    "RichNodeFeature(time_minutes=123.0, daily_phase_sin=0.6977904598416802, daily_phase_cos=-0.7163019434246543, session_position=0.825503355704698, time_to_close=26.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23373, price_delta_1m=-0.006786703553262678, price_delta_5m=-0.0009062505722871416, price_delta_15m=-0.006994116212244324, volatility_window=0.038446381646691785, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=16, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=26.25, structural_importance=26.25, raw_json={'timestamp': '09:03:00', 'event_type': 'final_cascade', 'price_level': 23373.0, 'magnitude': 26.25, 'duration_minutes': 2.0})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 3,
        "feature_idx": 5
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 1,
        "feature_idx": 7
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 8
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 9
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 10
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 11
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 12
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 13
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 12,
        "feature_idx": 14
      },
      {
        "source": 0,
        "target": 13,
        "feature_idx": 15
      },
      {
        "source": 0,
        "target": 14,
        "feature_idx": 16
      },
      {
        "source": 0,
        "target": 15,
        "feature_idx": 17
      },
      {
        "source": 1,
        "target": 12,
        "feature_idx": 18
      },
      {
        "source": 1,
        "target": 13,
        "feature_idx": 19
      },
      {
        "source": 1,
        "target": 14,
        "feature_idx": 20
      },
      {
        "source": 1,
        "target": 15,
        "feature_idx": 21
      },
      {
        "source": 2,
        "target": 12,
        "feature_idx": 22
      },
      {
        "source": 2,
        "target": 13,
        "feature_idx": 23
      },
      {
        "source": 2,
        "target": 14,
        "feature_idx": 24
      },
      {
        "source": 2,
        "target": 15,
        "feature_idx": 25
      },
      {
        "source": 3,
        "target": 12,
        "feature_idx": 26
      },
      {
        "source": 3,
        "target": 13,
        "feature_idx": 27
      },
      {
        "source": 3,
        "target": 14,
        "feature_idx": 28
      },
      {
        "source": 3,
        "target": 15,
        "feature_idx": 29
      },
      {
        "source": 4,
        "target": 12,
        "feature_idx": 30
      },
      {
        "source": 4,
        "target": 13,
        "feature_idx": 31
      },
      {
        "source": 4,
        "target": 14,
        "feature_idx": 32
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 33
      },
      {
        "source": 5,
        "target": 12,
        "feature_idx": 34
      },
      {
        "source": 5,
        "target": 13,
        "feature_idx": 35
      },
      {
        "source": 5,
        "target": 14,
        "feature_idx": 36
      },
      {
        "source": 5,
        "target": 15,
        "feature_idx": 37
      },
      {
        "source": 6,
        "target": 12,
        "feature_idx": 38
      },
      {
        "source": 6,
        "target": 13,
        "feature_idx": 39
      },
      {
        "source": 6,
        "target": 14,
        "feature_idx": 40
      },
      {
        "source": 6,
        "target": 15,
        "feature_idx": 41
      },
      {
        "source": 7,
        "target": 12,
        "feature_idx": 42
      },
      {
        "source": 7,
        "target": 13,
        "feature_idx": 43
      },
      {
        "source": 7,
        "target": 14,
        "feature_idx": 44
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 45
      },
      {
        "source": 8,
        "target": 12,
        "feature_idx": 46
      },
      {
        "source": 8,
        "target": 13,
        "feature_idx": 47
      },
      {
        "source": 8,
        "target": 14,
        "feature_idx": 48
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 49
      },
      {
        "source": 9,
        "target": 12,
        "feature_idx": 50
      },
      {
        "source": 9,
        "target": 13,
        "feature_idx": 51
      },
      {
        "source": 9,
        "target": 14,
        "feature_idx": 52
      },
      {
        "source": 9,
        "target": 15,
        "feature_idx": 53
      },
      {
        "source": 10,
        "target": 12,
        "feature_idx": 54
      },
      {
        "source": 10,
        "target": 13,
        "feature_idx": 55
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 56
      },
      {
        "source": 10,
        "target": 15,
        "feature_idx": 57
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 58
      },
      {
        "source": 11,
        "target": 13,
        "feature_idx": 59
      },
      {
        "source": 11,
        "target": 14,
        "feature_idx": 60
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 61
      }
    ],
    "temporal_echo": [
      {
        "source": 3,
        "target": 15,
        "feature_idx": 62
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 63
      },
      {
        "source": 9,
        "target": 12,
        "feature_idx": 64
      },
      {
        "source": 9,
        "target": 13,
        "feature_idx": 65
      },
      {
        "source": 10,
        "target": 13,
        "feature_idx": 66
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 67
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 68
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    