{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11
    ],
    "5m": [],
    "15m": [],
    "1h": [
      12,
      13,
      14,
      15
    ],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=0.008738456571611245, price_delta_5m=0.0005098629460019399, price_delta_15m=-0.001431359045717158, volatility_window=0.04789234723279402, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23388.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23389, price_delta_1m=0.0005712396129157142, price_delta_5m=-0.01038772112179052, price_delta_15m=0.017335953309198012, volatility_window=0.03865840335198228, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23389.0, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.014977009238022324, price_delta_5m=-0.003938704395622229, price_delta_15m=-0.008116997507978516, volatility_window=0.010415786818398023, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23346.75, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.233605, price_delta_1m=-0.005098814396958251, price_delta_5m=-0.011035037649148982, price_delta_15m=-0.003166842868395237, volatility_window=0.03131323114905538, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23360.5, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=-0.008784261095375966, price_delta_5m=-0.007844505730845178, price_delta_15m=-0.010627580427805186, volatility_window=0.04311080449982943, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23388.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23389, price_delta_1m=-0.009364047984856395, price_delta_5m=0.005949487767217071, price_delta_15m=0.007163172872968202, volatility_window=0.024942074828912683, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23389.0, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.008398183908025564, price_delta_5m=0.009753099457069505, price_delta_15m=0.008903988486812857, volatility_window=0.01814168184662057, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23346.75, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.233605, price_delta_1m=0.009350788781927577, price_delta_5m=-0.00039196120332934877, price_delta_15m=-0.017690941138381273, volatility_window=0.041993167144277956, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23360.5, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2338525, price_delta_1m=-0.003685078605073831, price_delta_5m=-0.005214913916677685, price_delta_15m=0.02127926926058135, volatility_window=0.01795630937582863, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price': 23385.25, 'action': 'touch', 'context': 'PreMarket_FPFVG_formation_premium_high'})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2336075, price_delta_1m=-0.007085543842419343, price_delta_5m=-0.0207290891619278, price_delta_15m=-0.002591725559635082, volatility_window=0.013224822477233493, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:52:00', 'price': 23360.75, 'action': 'touch', 'context': 'initial_expansion_low_formation'})",
    "RichNodeFeature(time_minutes=93.0, daily_phase_sin=0.7853169308807448, daily_phase_cos=-0.6190939493098341, session_position=0.6241610738255033, time_to_close=56.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2338625, price_delta_1m=0.009968854204269273, price_delta_5m=0.03148009128497201, price_delta_15m=0.003945559974972097, volatility_window=0.01967675094907777, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:33:00', 'price': 23386.25, 'action': 'touch', 'context': 'retracement_high_and_FVG_rebalance'})",
    "RichNodeFeature(time_minutes=125.0, daily_phase_sin=0.6915130557822694, daily_phase_cos=-0.7223639620597555, session_position=0.8389261744966443, time_to_close=24.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.006147956247554087, price_delta_5m=0.0015337919558712058, price_delta_15m=-0.0076063841592302305, volatility_window=0.04257531785323137, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:05:00', 'price': 23346.75, 'action': 'break', 'context': 'London_session_low_taken_out_creating_session_low'})",
    "RichNodeFeature(time_minutes=43.0, daily_phase_sin=0.9006982393225879, daily_phase_cos=-0.43444525740441703, session_position=0.28859060402684567, time_to_close=106.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2337975, price_delta_1m=-0.009564154162490844, price_delta_5m=0.003860475799023573, price_delta_15m=0.013504873787954771, volatility_window=0.04695693189936603, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=14, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=19.0, structural_importance=19.0, raw_json={'timestamp': '07:43:00', 'event_type': 'expansion_phase', 'price_level': 23379.75, 'magnitude': 19.0, 'duration_minutes': 9.0})",
    "RichNodeFeature(time_minutes=76.0, daily_phase_sin=0.8290375725550418, daily_phase_cos=-0.5591929034707467, session_position=0.5100671140939598, time_to_close=73.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2336125, price_delta_1m=0.0009756119890658263, price_delta_5m=-0.006737017662314195, price_delta_15m=-0.003397682064897172, volatility_window=0.042272309586337105, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=15, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=25.0, structural_importance=25.0, raw_json={'timestamp': '08:16:00', 'event_type': 'retracement_expansion', 'price_level': 23361.25, 'magnitude': 25.0, 'duration_minutes': 17.0})",
    "RichNodeFeature(time_minutes=93.0, daily_phase_sin=0.7853169308807448, daily_phase_cos=-0.6190939493098341, session_position=0.6241610738255033, time_to_close=56.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2338625, price_delta_1m=0.0009822010163660188, price_delta_5m=0.0016853054786447172, price_delta_15m=0.01830903007001137, volatility_window=0.02357271646051714, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=14, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=19.5, structural_importance=19.5, raw_json={'timestamp': '08:33:00', 'event_type': 'expansion_phase', 'price_level': 23386.25, 'magnitude': 19.5, 'duration_minutes': 17.0})",
    "RichNodeFeature(time_minutes=123.0, daily_phase_sin=0.6977904598416802, daily_phase_cos=-0.7163019434246543, session_position=0.825503355704698, time_to_close=26.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23373, price_delta_1m=0.017122346651844193, price_delta_5m=-0.0007157634473043298, price_delta_15m=-0.020980457200282215, volatility_window=0.022467004404954707, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=16, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=26.25, structural_importance=26.25, raw_json={'timestamp': '09:03:00', 'event_type': 'final_cascade', 'price_level': 23373.0, 'magnitude': 26.25, 'duration_minutes': 2.0})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 3,
        "feature_idx": 5
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 1,
        "feature_idx": 7
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 8
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 9
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 10
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 11
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 12
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 13
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 12,
        "feature_idx": 14
      },
      {
        "source": 0,
        "target": 13,
        "feature_idx": 15
      },
      {
        "source": 0,
        "target": 14,
        "feature_idx": 16
      },
      {
        "source": 0,
        "target": 15,
        "feature_idx": 17
      },
      {
        "source": 1,
        "target": 12,
        "feature_idx": 18
      },
      {
        "source": 1,
        "target": 13,
        "feature_idx": 19
      },
      {
        "source": 1,
        "target": 14,
        "feature_idx": 20
      },
      {
        "source": 1,
        "target": 15,
        "feature_idx": 21
      },
      {
        "source": 2,
        "target": 12,
        "feature_idx": 22
      },
      {
        "source": 2,
        "target": 13,
        "feature_idx": 23
      },
      {
        "source": 2,
        "target": 14,
        "feature_idx": 24
      },
      {
        "source": 2,
        "target": 15,
        "feature_idx": 25
      },
      {
        "source": 3,
        "target": 12,
        "feature_idx": 26
      },
      {
        "source": 3,
        "target": 13,
        "feature_idx": 27
      },
      {
        "source": 3,
        "target": 14,
        "feature_idx": 28
      },
      {
        "source": 3,
        "target": 15,
        "feature_idx": 29
      },
      {
        "source": 4,
        "target": 12,
        "feature_idx": 30
      },
      {
        "source": 4,
        "target": 13,
        "feature_idx": 31
      },
      {
        "source": 4,
        "target": 14,
        "feature_idx": 32
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 33
      },
      {
        "source": 5,
        "target": 12,
        "feature_idx": 34
      },
      {
        "source": 5,
        "target": 13,
        "feature_idx": 35
      },
      {
        "source": 5,
        "target": 14,
        "feature_idx": 36
      },
      {
        "source": 5,
        "target": 15,
        "feature_idx": 37
      },
      {
        "source": 6,
        "target": 12,
        "feature_idx": 38
      },
      {
        "source": 6,
        "target": 13,
        "feature_idx": 39
      },
      {
        "source": 6,
        "target": 14,
        "feature_idx": 40
      },
      {
        "source": 6,
        "target": 15,
        "feature_idx": 41
      },
      {
        "source": 7,
        "target": 12,
        "feature_idx": 42
      },
      {
        "source": 7,
        "target": 13,
        "feature_idx": 43
      },
      {
        "source": 7,
        "target": 14,
        "feature_idx": 44
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 45
      },
      {
        "source": 8,
        "target": 12,
        "feature_idx": 46
      },
      {
        "source": 8,
        "target": 13,
        "feature_idx": 47
      },
      {
        "source": 8,
        "target": 14,
        "feature_idx": 48
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 49
      },
      {
        "source": 9,
        "target": 12,
        "feature_idx": 50
      },
      {
        "source": 9,
        "target": 13,
        "feature_idx": 51
      },
      {
        "source": 9,
        "target": 14,
        "feature_idx": 52
      },
      {
        "source": 9,
        "target": 15,
        "feature_idx": 53
      },
      {
        "source": 10,
        "target": 12,
        "feature_idx": 54
      },
      {
        "source": 10,
        "target": 13,
        "feature_idx": 55
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 56
      },
      {
        "source": 10,
        "target": 15,
        "feature_idx": 57
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 58
      },
      {
        "source": 11,
        "target": 13,
        "feature_idx": 59
      },
      {
        "source": 11,
        "target": 14,
        "feature_idx": 60
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 61
      }
    ],
    "temporal_echo": [
      {
        "source": 3,
        "target": 15,
        "feature_idx": 62
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 63
      },
      {
        "source": 9,
        "target": 12,
        "feature_idx": 64
      },
      {
        "source": 9,
        "target": 13,
        "feature_idx": 65
      },
      {
        "source": 10,
        "target": 13,
        "feature_idx": 66
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 67
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 68
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    