{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11
    ],
    "5m": [
      12,
      13,
      14
    ],
    "15m": [
      15
    ],
    "1h": [
      16
    ],
    "D": [
      17
    ],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'price_level': 23388.0, 'open': 23388.0, 'high': 23388.0, 'low': 23388.0, 'close': 23388.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23389, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '12:00:00', 'price_level': 23389.0, 'open': 23389.0, 'high': 23389.0, 'low': 23389.0, 'close': 23389.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '12:30:00', 'price_level': 23346.75, 'open': 23346.75, 'high': 23346.75, 'low': 23346.75, 'close': 23346.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.233605, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '09:29:00', 'price_level': 23360.5, 'open': 23360.5, 'high': 23360.5, 'low': 23360.5, 'close': 23360.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 4, 'timestamp': '07:00:00', 'price_level': 23388.0, 'open': 23388.0, 'high': 23388.0, 'low': 23388.0, 'close': 23388.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23389, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 5, 'timestamp': '12:00:00', 'price_level': 23389.0, 'open': 23389.0, 'high': 23389.0, 'low': 23389.0, 'close': 23389.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.2334675, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 6, 'timestamp': '12:30:00', 'price_level': 23346.75, 'open': 23346.75, 'high': 23346.75, 'low': 23346.75, 'close': 23346.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.233605, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 7, 'timestamp': '09:29:00', 'price_level': 23360.5, 'open': 23360.5, 'high': 23360.5, 'low': 23360.5, 'close': 23360.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 8, 'timestamp': '07:01:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'PreMarket_FPFVG_formation_premium_high', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 9, 'timestamp': '07:52:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'initial_expansion_low_formation', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=93.0, daily_phase_sin=0.7853169308807448, daily_phase_cos=-0.6190939493098341, session_position=0.6241610738255033, time_to_close=56.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 10, 'timestamp': '08:33:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'retracement_high_and_FVG_rebalance', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=125.0, daily_phase_sin=0.6915130557822694, daily_phase_cos=-0.7223639620597555, session_position=0.8389261744966443, time_to_close=24.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 11, 'timestamp': '09:05:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'London_session_low_taken_out_creating_session_low', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.23388, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.001806481956558919, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23346.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=4.275696938600992e-05, structural_importance=0.75, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23388.0, 'high': 23389.0, 'low': 23346.75, 'close': 23388.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23389.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23346.75}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 42.25}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23346.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=0.5, raw_json={'id': 1, 'timestamp': '12:00:00', 'open': 23389.0, 'high': 23389.0, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23346.75}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23346.75}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 23389.0}})",
    "RichNodeFeature(time_minutes=93.0, daily_phase_sin=0.7853169308807448, daily_phase_cos=-0.6190939493098341, session_position=0.6241610738255033, time_to_close=56.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.2, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '08:33:00', 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 2, 'pd_array': None, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 2, 'period_minutes': 5, 'price_range': 0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23346.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=3.5999999999999996, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23388.0, 'high': 23389.0, 'low': 0, 'close': 0, 'timeframe': '15m', 'source_movements': 12, 'pd_array': {'type': 'swing_high', 'level': 23389.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23346.75}, 'meta': {'coverage': 12, 'period_minutes': 15, 'price_range': 23389.0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=23346.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=3.5999999999999996, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23388.0, 'high': 23389.0, 'low': 0, 'close': 0, 'timeframe': '60m', 'source_movements': 12, 'pd_array': {'type': 'swing_high', 'level': 23389.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23346.75}, 'meta': {'coverage': 12, 'period_minutes': 60, 'price_range': 23389.0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.14285714285714285, absolute_timestamp=1753398000, day_of_week=4, month_phase=0.8064516129032258, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=4, liquidity_type=0, fpfvg_gap_size=23346.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=3.5999999999999996, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23388.0, 'high': 23389.0, 'low': 0, 'close': 0, 'timeframe': '1440m', 'source_movements': 12, 'pd_array': {'type': 'swing_high', 'level': 23389.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23346.75}, 'meta': {'coverage': 12, 'period_minutes': 1440, 'price_range': 23389.0}})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 3,
        "feature_idx": 5
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 1,
        "feature_idx": 7
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 8
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 9
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 10
      },
      {
        "source": 12,
        "target": 14,
        "feature_idx": 11
      },
      {
        "source": 14,
        "target": 13,
        "feature_idx": 12
      }
    ],
    "scale": [
      {
        "source": 0,
        "target": 12,
        "feature_idx": 13
      },
      {
        "source": 4,
        "target": 12,
        "feature_idx": 14
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 15
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 16
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 17
      },
      {
        "source": 0,
        "target": 12,
        "feature_idx": 18,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 13,
        "feature_idx": 19,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23346.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 12,
        "feature_idx": 20,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 12,
        "feature_idx": 21,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 12,
        "feature_idx": 22,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 13,
        "feature_idx": 23,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23346.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 12,
        "feature_idx": 24,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 12,
        "feature_idx": 25,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 12,
        "feature_idx": 26,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 12,
        "feature_idx": 27,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 28,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 2,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 29,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 15,
        "feature_idx": 30,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 15,
        "feature_idx": 31,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 15,
        "feature_idx": 32,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 15,
        "feature_idx": 33,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 34,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 15,
        "feature_idx": 35,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 15,
        "feature_idx": 36,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 37,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 38,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 15,
        "feature_idx": 39,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 15,
        "feature_idx": 40,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 41,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 16,
        "feature_idx": 42,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 16,
        "feature_idx": 43,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 16,
        "feature_idx": 44,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 16,
        "feature_idx": 45,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 16,
        "feature_idx": 46,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 16,
        "feature_idx": 47,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 16,
        "feature_idx": 48,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 16,
        "feature_idx": 49,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 16,
        "feature_idx": 50,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 16,
        "feature_idx": 51,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 16,
        "feature_idx": 52,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 16,
        "feature_idx": 53,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 17,
        "feature_idx": 54,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 17,
        "feature_idx": 55,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 17,
        "feature_idx": 56,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 17,
        "feature_idx": 57,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 17,
        "feature_idx": 58,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 17,
        "feature_idx": 59,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 17,
        "feature_idx": 60,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 17,
        "feature_idx": 61,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 17,
        "feature_idx": 62,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 17,
        "feature_idx": 63,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 17,
        "feature_idx": 64,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 17,
        "feature_idx": 65,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 12,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23389.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23346.75
          },
          "liquidity_sweep": true
        }
      }
    ],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 12,
        "feature_idx": 66
      },
      {
        "source": 1,
        "target": 12,
        "feature_idx": 67
      },
      {
        "source": 2,
        "target": 12,
        "feature_idx": 68
      },
      {
        "source": 3,
        "target": 12,
        "feature_idx": 69
      },
      {
        "source": 4,
        "target": 12,
        "feature_idx": 70
      },
      {
        "source": 5,
        "target": 12,
        "feature_idx": 71
      },
      {
        "source": 6,
        "target": 12,
        "feature_idx": 72
      },
      {
        "source": 7,
        "target": 12,
        "feature_idx": 73
      },
      {
        "source": 8,
        "target": 13,
        "feature_idx": 74
      },
      {
        "source": 8,
        "target": 14,
        "feature_idx": 75
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 76
      },
      {
        "source": 8,
        "target": 16,
        "feature_idx": 77
      },
      {
        "source": 8,
        "target": 17,
        "feature_idx": 78
      },
      {
        "source": 9,
        "target": 13,
        "feature_idx": 79
      },
      {
        "source": 9,
        "target": 14,
        "feature_idx": 80
      },
      {
        "source": 9,
        "target": 15,
        "feature_idx": 81
      },
      {
        "source": 9,
        "target": 16,
        "feature_idx": 82
      },
      {
        "source": 9,
        "target": 17,
        "feature_idx": 83
      },
      {
        "source": 10,
        "target": 13,
        "feature_idx": 84
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 85
      },
      {
        "source": 10,
        "target": 15,
        "feature_idx": 86
      },
      {
        "source": 10,
        "target": 16,
        "feature_idx": 87
      },
      {
        "source": 10,
        "target": 17,
        "feature_idx": 88
      },
      {
        "source": 11,
        "target": 13,
        "feature_idx": 89
      },
      {
        "source": 11,
        "target": 14,
        "feature_idx": 90
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 91
      },
      {
        "source": 11,
        "target": 16,
        "feature_idx": 92
      },
      {
        "source": 11,
        "target": 17,
        "feature_idx": 93
      },
      {
        "source": 13,
        "target": 15,
        "feature_idx": 94
      },
      {
        "source": 13,
        "target": 16,
        "feature_idx": 95
      },
      {
        "source": 13,
        "target": 17,
        "feature_idx": 96
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 97
      },
      {
        "source": 14,
        "target": 16,
        "feature_idx": 98
      },
      {
        "source": 14,
        "target": 17,
        "feature_idx": 99
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 100
      },
      {
        "source": 15,
        "target": 17,
        "feature_idx": 101
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 102
      }
    ],
    "temporal_echo": [
      {
        "source": 0,
        "target": 12,
        "feature_idx": 103
      },
      {
        "source": 0,
        "target": 15,
        "feature_idx": 104
      },
      {
        "source": 0,
        "target": 16,
        "feature_idx": 105
      },
      {
        "source": 0,
        "target": 17,
        "feature_idx": 106
      },
      {
        "source": 1,
        "target": 13,
        "feature_idx": 107
      },
      {
        "source": 4,
        "target": 12,
        "feature_idx": 108
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 109
      },
      {
        "source": 4,
        "target": 16,
        "feature_idx": 110
      },
      {
        "source": 4,
        "target": 17,
        "feature_idx": 111
      },
      {
        "source": 5,
        "target": 13,
        "feature_idx": 112
      },
      {
        "source": 8,
        "target": 12,
        "feature_idx": 113
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 114
      },
      {
        "source": 8,
        "target": 16,
        "feature_idx": 115
      },
      {
        "source": 8,
        "target": 17,
        "feature_idx": 116
      },
      {
        "source": 10,
        "target": 14,
        "feature_idx": 117
      },
      {
        "source": 12,
        "target": 15,
        "feature_idx": 118
      },
      {
        "source": 12,
        "target": 16,
        "feature_idx": 119
      },
      {
        "source": 12,
        "target": 17,
        "feature_idx": 120
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 121
      },
      {
        "source": 15,
        "target": 17,
        "feature_idx": 122
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 123
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    