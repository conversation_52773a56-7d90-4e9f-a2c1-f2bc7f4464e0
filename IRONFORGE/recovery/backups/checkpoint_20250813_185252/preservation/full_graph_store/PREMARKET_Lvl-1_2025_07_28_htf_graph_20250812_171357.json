{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14
    ],
    "5m": [
      15,
      16,
      17
    ],
    "15m": [
      18
    ],
    "1h": [
      19
    ],
    "D": [
      20
    ],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.234935, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'price_level': 23493.5, 'open': 23493.5, 'high': 23493.5, 'low': 23493.5, 'close': 23493.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.23521, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '12:00:00', 'price_level': 23521.0, 'open': 23521.0, 'high': 23521.0, 'low': 23521.0, 'close': 23521.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.2346625, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '12:30:00', 'price_level': 23466.25, 'open': 23466.25, 'high': 23466.25, 'low': 23466.25, 'close': 23466.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.234725, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '09:29:00', 'price_level': 23472.5, 'open': 23472.5, 'high': 23472.5, 'low': 23472.5, 'close': 23472.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.234935, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 4, 'timestamp': '07:00:00', 'price_level': 23493.5, 'open': 23493.5, 'high': 23493.5, 'low': 23493.5, 'close': 23493.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.23521, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 5, 'timestamp': '12:00:00', 'price_level': 23521.0, 'open': 23521.0, 'high': 23521.0, 'low': 23521.0, 'close': 23521.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.2346625, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 6, 'timestamp': '12:30:00', 'price_level': 23466.25, 'open': 23466.25, 'high': 23466.25, 'low': 23466.25, 'close': 23466.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.234725, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 7, 'timestamp': '09:29:00', 'price_level': 23472.5, 'open': 23472.5, 'high': 23472.5, 'low': 23472.5, 'close': 23472.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 8, 'timestamp': '07:01:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market first presentation FVG premium high created', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=74.0, daily_phase_sin=0.8338858220671682, daily_phase_cos=-0.5519369853120581, session_position=0.4966442953020134, time_to_close=75.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 9, 'timestamp': '08:14:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Reversal point after expansion lower', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=92.0, daily_phase_sin=0.788010753606722, daily_phase_cos=-0.6156614753256582, session_position=0.6174496644295302, time_to_close=57.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 10, 'timestamp': '08:32:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market first presentation FVG redelivered', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=113.0, daily_phase_sin=0.7283709698824006, daily_phase_cos=-0.6851829903263588, session_position=0.7583892617449665, time_to_close=36.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 11, 'timestamp': '08:53:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market session high created, new reversal point', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=120.0, daily_phase_sin=0.7071067811865476, daily_phase_cos=-0.7071067811865475, session_position=0.8053691275167785, time_to_close=29.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 12, 'timestamp': '09:00:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': \"Today's Asia first presentation FVG redelivered\", 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=126.0, daily_phase_sin=0.6883545756937539, daily_phase_cos=-0.7253743710122877, session_position=0.8456375838926175, time_to_close=23.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 13, 'timestamp': '09:06:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market first presentation FVG redelivered again', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=146.0, daily_phase_sin=0.6225146366376195, daily_phase_cos=-0.7826081568524139, session_position=0.9798657718120806, time_to_close=3.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 14, 'timestamp': '09:26:00', 'price_level': 0, 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'event_type': 'session_level', 'context': 'Pre-market session low created', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.234935, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0023304318215676676, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23466.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0011705365313810204, structural_importance=0.75, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23493.5, 'high': 23521.0, 'low': 23466.25, 'close': 23493.5, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23521.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23466.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 54.75}})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23466.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=0.5, raw_json={'id': 1, 'timestamp': '12:00:00', 'open': 23521.0, 'high': 23521.0, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23466.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23466.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 23521.0}})",
    "RichNodeFeature(time_minutes=92.0, daily_phase_sin=0.788010753606722, daily_phase_cos=-0.6156614753256582, session_position=0.6174496644295302, time_to_close=57.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=0, price_delta_5m=0, price_delta_15m=0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '08:32:00', 'open': 0, 'high': 0, 'low': 0, 'close': 0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': None, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23466.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=4.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23493.5, 'high': 23521.0, 'low': 0, 'close': 0, 'timeframe': '15m', 'source_movements': 15, 'pd_array': {'type': 'swing_high', 'level': 23521.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23466.25}, 'meta': {'coverage': 15, 'period_minutes': 15, 'price_range': 23521.0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=23466.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=4.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23493.5, 'high': 23521.0, 'low': 0, 'close': 0, 'timeframe': '60m', 'source_movements': 15, 'pd_array': {'type': 'swing_high', 'level': 23521.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23466.25}, 'meta': {'coverage': 15, 'period_minutes': 60, 'price_range': 23521.0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1753657200, day_of_week=0, month_phase=0.9032258064516129, normalized_price=0.0, price_delta_1m=-1.0, price_delta_5m=-1.0, price_delta_15m=-1.0, volatility_window=0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=4, liquidity_type=0, fpfvg_gap_size=23466.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0, structural_importance=4.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23493.5, 'high': 23521.0, 'low': 0, 'close': 0, 'timeframe': '1440m', 'source_movements': 15, 'pd_array': {'type': 'swing_high', 'level': 23521.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23466.25}, 'meta': {'coverage': 15, 'period_minutes': 1440, 'price_range': 23521.0}})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 5
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 6
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 7
      },
      {
        "source": 14,
        "target": 3,
        "feature_idx": 8
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 9
      },
      {
        "source": 7,
        "target": 1,
        "feature_idx": 10
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 11
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 12
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 13
      },
      {
        "source": 15,
        "target": 17,
        "feature_idx": 14
      },
      {
        "source": 17,
        "target": 16,
        "feature_idx": 15
      }
    ],
    "scale": [
      {
        "source": 0,
        "target": 15,
        "feature_idx": 16
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 17
      },
      {
        "source": 9,
        "target": 17,
        "feature_idx": 18
      },
      {
        "source": 10,
        "target": 17,
        "feature_idx": 19
      },
      {
        "source": 11,
        "target": 17,
        "feature_idx": 20
      },
      {
        "source": 12,
        "target": 17,
        "feature_idx": 21
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 22
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 23
      },
      {
        "source": 0,
        "target": 15,
        "feature_idx": 24,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 16,
        "feature_idx": 25,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23466.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 15,
        "feature_idx": 26,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 15,
        "feature_idx": 27,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 28,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 16,
        "feature_idx": 29,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23466.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 15,
        "feature_idx": 30,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 31,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 32,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 15,
        "feature_idx": 33,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 17,
        "feature_idx": 34,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": null,
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 15,
        "feature_idx": 35,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 15,
        "feature_idx": 36,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 15,
        "feature_idx": 37,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 38,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 18,
        "feature_idx": 39,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 18,
        "feature_idx": 40,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 18,
        "feature_idx": 41,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 18,
        "feature_idx": 42,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 18,
        "feature_idx": 43,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 18,
        "feature_idx": 44,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 18,
        "feature_idx": 45,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 18,
        "feature_idx": 46,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 18,
        "feature_idx": 47,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 18,
        "feature_idx": 48,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 18,
        "feature_idx": 49,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 18,
        "feature_idx": 50,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 18,
        "feature_idx": 51,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 18,
        "feature_idx": 52,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 18,
        "feature_idx": 53,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 19,
        "feature_idx": 54,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 19,
        "feature_idx": 55,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 56,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 57,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 58,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 19,
        "feature_idx": 59,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 19,
        "feature_idx": 60,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 19,
        "feature_idx": 61,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 62,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 63,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 19,
        "feature_idx": 64,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 19,
        "feature_idx": 65,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 19,
        "feature_idx": 66,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 19,
        "feature_idx": 67,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 19,
        "feature_idx": 68,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 20,
        "feature_idx": 69,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 20,
        "feature_idx": 70,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 20,
        "feature_idx": 71,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 20,
        "feature_idx": 72,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 20,
        "feature_idx": 73,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 74,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 20,
        "feature_idx": 75,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 20,
        "feature_idx": 76,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 77,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 20,
        "feature_idx": 78,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 20,
        "feature_idx": 79,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 20,
        "feature_idx": 80,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 20,
        "feature_idx": 81,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 20,
        "feature_idx": 82,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 20,
        "feature_idx": 83,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23521.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23466.25
          },
          "liquidity_sweep": true
        }
      }
    ],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 15,
        "feature_idx": 84
      },
      {
        "source": 1,
        "target": 15,
        "feature_idx": 85
      },
      {
        "source": 2,
        "target": 15,
        "feature_idx": 86
      },
      {
        "source": 3,
        "target": 15,
        "feature_idx": 87
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 88
      },
      {
        "source": 5,
        "target": 15,
        "feature_idx": 89
      },
      {
        "source": 6,
        "target": 15,
        "feature_idx": 90
      },
      {
        "source": 7,
        "target": 15,
        "feature_idx": 91
      },
      {
        "source": 8,
        "target": 16,
        "feature_idx": 92
      },
      {
        "source": 8,
        "target": 17,
        "feature_idx": 93
      },
      {
        "source": 8,
        "target": 18,
        "feature_idx": 94
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 95
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 96
      },
      {
        "source": 9,
        "target": 16,
        "feature_idx": 97
      },
      {
        "source": 9,
        "target": 17,
        "feature_idx": 98
      },
      {
        "source": 9,
        "target": 18,
        "feature_idx": 99
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 100
      },
      {
        "source": 9,
        "target": 20,
        "feature_idx": 101
      },
      {
        "source": 10,
        "target": 16,
        "feature_idx": 102
      },
      {
        "source": 10,
        "target": 17,
        "feature_idx": 103
      },
      {
        "source": 10,
        "target": 18,
        "feature_idx": 104
      },
      {
        "source": 10,
        "target": 19,
        "feature_idx": 105
      },
      {
        "source": 10,
        "target": 20,
        "feature_idx": 106
      },
      {
        "source": 11,
        "target": 16,
        "feature_idx": 107
      },
      {
        "source": 11,
        "target": 17,
        "feature_idx": 108
      },
      {
        "source": 11,
        "target": 18,
        "feature_idx": 109
      },
      {
        "source": 11,
        "target": 19,
        "feature_idx": 110
      },
      {
        "source": 11,
        "target": 20,
        "feature_idx": 111
      },
      {
        "source": 12,
        "target": 16,
        "feature_idx": 112
      },
      {
        "source": 12,
        "target": 17,
        "feature_idx": 113
      },
      {
        "source": 12,
        "target": 18,
        "feature_idx": 114
      },
      {
        "source": 12,
        "target": 19,
        "feature_idx": 115
      },
      {
        "source": 12,
        "target": 20,
        "feature_idx": 116
      },
      {
        "source": 13,
        "target": 16,
        "feature_idx": 117
      },
      {
        "source": 13,
        "target": 17,
        "feature_idx": 118
      },
      {
        "source": 13,
        "target": 18,
        "feature_idx": 119
      },
      {
        "source": 13,
        "target": 19,
        "feature_idx": 120
      },
      {
        "source": 13,
        "target": 20,
        "feature_idx": 121
      },
      {
        "source": 14,
        "target": 16,
        "feature_idx": 122
      },
      {
        "source": 14,
        "target": 17,
        "feature_idx": 123
      },
      {
        "source": 14,
        "target": 18,
        "feature_idx": 124
      },
      {
        "source": 14,
        "target": 19,
        "feature_idx": 125
      },
      {
        "source": 14,
        "target": 20,
        "feature_idx": 126
      },
      {
        "source": 16,
        "target": 18,
        "feature_idx": 127
      },
      {
        "source": 16,
        "target": 19,
        "feature_idx": 128
      },
      {
        "source": 16,
        "target": 20,
        "feature_idx": 129
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 130
      },
      {
        "source": 17,
        "target": 19,
        "feature_idx": 131
      },
      {
        "source": 17,
        "target": 20,
        "feature_idx": 132
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 133
      },
      {
        "source": 18,
        "target": 20,
        "feature_idx": 134
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 135
      }
    ],
    "temporal_echo": [
      {
        "source": 0,
        "target": 15,
        "feature_idx": 136
      },
      {
        "source": 0,
        "target": 18,
        "feature_idx": 137
      },
      {
        "source": 0,
        "target": 19,
        "feature_idx": 138
      },
      {
        "source": 0,
        "target": 20,
        "feature_idx": 139
      },
      {
        "source": 1,
        "target": 16,
        "feature_idx": 140
      },
      {
        "source": 4,
        "target": 15,
        "feature_idx": 141
      },
      {
        "source": 4,
        "target": 18,
        "feature_idx": 142
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 143
      },
      {
        "source": 4,
        "target": 20,
        "feature_idx": 144
      },
      {
        "source": 5,
        "target": 16,
        "feature_idx": 145
      },
      {
        "source": 8,
        "target": 15,
        "feature_idx": 146
      },
      {
        "source": 8,
        "target": 18,
        "feature_idx": 147
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 148
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 149
      },
      {
        "source": 9,
        "target": 17,
        "feature_idx": 150
      },
      {
        "source": 10,
        "target": 17,
        "feature_idx": 151
      },
      {
        "source": 11,
        "target": 17,
        "feature_idx": 152
      },
      {
        "source": 12,
        "target": 17,
        "feature_idx": 153
      },
      {
        "source": 15,
        "target": 18,
        "feature_idx": 154
      },
      {
        "source": 15,
        "target": 19,
        "feature_idx": 155
      },
      {
        "source": 15,
        "target": 20,
        "feature_idx": 156
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 157
      },
      {
        "source": 18,
        "target": 20,
        "feature_idx": 158
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 159
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    