{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22
    ],
    "5m": [],
    "15m": [
      23,
      24,
      25,
      26,
      27,
      28,
      29,
      30,
      31,
      32,
      33,
      34,
      35,
      36,
      37,
      38,
      39,
      40
    ],
    "1h": [],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=0.002146347858880119, price_delta_5m=0.00662144798439629, price_delta_15m=-0.006361846340820206, volatility_window=0.04795909925668608, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23144.25, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231375, price_delta_1m=-0.01297562576742074, price_delta_5m=-0.008470668984505795, price_delta_15m=-0.016507594257109, volatility_window=0.03472736432484841, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=116, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23137.5, 'movement_type': 'session_low_immediate'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=0.0018976931616696657, price_delta_5m=0.016594875329562348, price_delta_15m=-0.0016245023790017613, volatility_window=0.01388731733257365, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=117, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price_level': 23165.0, 'movement_type': 'premarket_fpfvg_formation_premium'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23158, price_delta_1m=0.00032095992654872376, price_delta_5m=-0.003333240232034074, price_delta_15m=0.012219264359159137, volatility_window=0.02273919058934491, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=118, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price_level': 23158.0, 'movement_type': 'premarket_fpfvg_formation_discount'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231725, price_delta_1m=0.005609478311434618, price_delta_5m=-0.009610578216377159, price_delta_15m=-0.015717864716155033, volatility_window=0.0323268849284594, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:02:00', 'price_level': 23172.5, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=8.0, daily_phase_sin=0.9563047559630355, daily_phase_cos=-0.29237170472273666, session_position=0.053691275167785234, time_to_close=141.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315225, price_delta_1m=-0.00725110874107216, price_delta_5m=-0.011640287582885418, price_delta_15m=-0.004634761976576235, volatility_window=0.033826550414843684, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:08:00', 'price_level': 23152.25, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=11.0, daily_phase_sin=0.9523957996432784, daily_phase_cos=-0.30486429902801077, session_position=0.0738255033557047, time_to_close=138.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317575, price_delta_1m=0.009679748782808343, price_delta_5m=0.0025031113600380447, price_delta_15m=-0.0036267082612354224, volatility_window=0.04674011635110287, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:11:00', 'price_level': 23175.75, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=12.0, daily_phase_sin=0.9510565162951536, daily_phase_cos=-0.30901699437494734, session_position=0.08053691275167785, time_to_close=137.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315425, price_delta_1m=0.0009528499959431934, price_delta_5m=-0.015212594715403385, price_delta_15m=9.299469723492908e-05, volatility_window=0.02581666868662976, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:12:00', 'price_level': 23154.25, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=22.0, daily_phase_sin=0.9366721892483976, daily_phase_cos=-0.35020738125946754, session_position=0.1476510067114094, time_to_close=127.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23178, price_delta_1m=-0.003593193449477467, price_delta_5m=-0.0061710414212571485, price_delta_15m=-0.008675342262738384, volatility_window=0.010010577656325093, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:22:00', 'price_level': 23178.0, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=26.0, daily_phase_sin=0.9304175679820246, daily_phase_cos=-0.3665012267242972, session_position=0.174496644295302, time_to_close=123.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=-0.00907326759696557, price_delta_5m=-0.004170500653264764, price_delta_15m=0.010505664786732373, volatility_window=0.02267227514310316, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:26:00', 'price_level': 23165.0, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319675, price_delta_1m=-0.01659684936781599, price_delta_5m=0.0068236742044405045, price_delta_15m=-0.0028010471322059576, volatility_window=0.03493577751932772, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=34, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:32:00', 'price_level': 23196.75, 'movement_type': 'session_high_reversal_point'})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23181, price_delta_1m=0.006022192239193231, price_delta_5m=-0.002016353460562211, price_delta_15m=0.0042439837085856405, volatility_window=0.02726766937248986, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:33:00', 'price_level': 23181.0, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317825, price_delta_1m=0.005917528033601625, price_delta_5m=0.001613279642088126, price_delta_15m=-0.0028329365842888776, volatility_window=0.03191564920697342, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:52:00', 'price_level': 23178.25, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=66.0, daily_phase_sin=0.8526401643540923, daily_phase_cos=-0.5224985647159488, session_position=0.4429530201342282, time_to_close=83.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314825, price_delta_1m=-0.011230031009492429, price_delta_5m=0.012022381375610767, price_delta_15m=-0.000352907617589818, volatility_window=0.04484837383007237, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:06:00', 'price_level': 23148.25, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=68.0, daily_phase_sin=0.8480480961564261, daily_phase_cos=-0.5299192642332048, session_position=0.4563758389261745, time_to_close=81.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316025, price_delta_1m=-0.00012871528013403297, price_delta_5m=-0.002191900742337609, price_delta_15m=0.0031937260668988493, volatility_window=0.02059591066243697, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=36, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:08:00', 'price_level': 23160.25, 'movement_type': 'retracement_high'})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=0.004400250788788075, price_delta_5m=-0.015593951055300814, price_delta_15m=-0.011991514655235933, volatility_window=0.020779817386875284, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=119, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:12:00', 'price_level': 23144.25, 'movement_type': 'premarket_open_price_touch'})",
    "RichNodeFeature(time_minutes=73.0, daily_phase_sin=0.8362861558477594, daily_phase_cos=-0.548293229519914, session_position=0.4899328859060403, time_to_close=76.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314025, price_delta_1m=0.001241102556190784, price_delta_5m=0.016729115902812983, price_delta_15m=-0.003952406408658817, volatility_window=0.013098387425553276, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=53, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:13:00', 'price_level': 23140.25, 'movement_type': 'expansion_low_reversal_point'})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2318625, price_delta_1m=-0.011062623423124267, price_delta_5m=-0.006317597188850835, price_delta_15m=0.0052185112132206, volatility_window=0.02121757779508807, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:22:00', 'price_level': 23186.25, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=92.0, daily_phase_sin=0.788010753606722, daily_phase_cos=-0.6156614753256582, session_position=0.6174496644295302, time_to_close=57.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314675, price_delta_1m=-0.007193682116941227, price_delta_5m=0.013116064042916908, price_delta_15m=0.010034225051832765, volatility_window=0.028596151290340696, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:32:00', 'price_level': 23146.75, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=97.0, daily_phase_sin=0.7743926440821857, daily_phase_cos=-0.6327053285625159, session_position=0.6510067114093959, time_to_close=52.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231775, price_delta_1m=-0.007070257514330127, price_delta_5m=0.002083555502807517, price_delta_15m=0.001161071997565976, volatility_window=0.03369592999791853, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:37:00', 'price_level': 23177.5, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=137.0, daily_phase_sin=0.6527597524627223, daily_phase_cos=-0.7575649843840497, session_position=0.9194630872483222, time_to_close=12.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319375, price_delta_1m=-0.008933874955877772, price_delta_5m=-0.010035299576889424, price_delta_15m=0.0007276963239756781, volatility_window=0.04791036929441249, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=61, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:17:00', 'price_level': 23193.75, 'movement_type': 'expansion_high_reversal_point'})",
    "RichNodeFeature(time_minutes=145.0, daily_phase_sin=0.6259234721840592, daily_phase_cos=-0.7798844830928817, session_position=0.9731543624161074, time_to_close=4.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23167, price_delta_1m=0.004325998572198363, price_delta_5m=0.015494002706680455, price_delta_15m=0.006161891596457801, volatility_window=0.023136775850827913, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:25:00', 'price_level': 23167.0, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=0.015995212044069316, price_delta_5m=0.005237816534882966, price_delta_15m=0.0018537212262754199, volatility_window=0.0368716778675409, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23168.75, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=-93.0, daily_phase_sin=0.9896513868196702, daily_phase_cos=0.1434926219911793, session_position=-0.6241610738255033, time_to_close=242.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.005219788946762765, price_delta_5m=-0.007318510910220407, price_delta_15m=-0.012016629852141613, volatility_window=0.04070900788330542, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=115, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '05:27:00', 'event_type': 'pre_session_takeout', 'liquidity_type': 'cross_session', 'target_level': 'london_session_low', 'magnitude': 'medium', 'context': 'london_session_low_taken_before_premarket_open'})",
    "RichNodeFeature(time_minutes=-89.0, daily_phase_sin=0.992004949679715, daily_phase_cos=0.1261989691358297, session_position=-0.5973154362416108, time_to_close=238.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.008626367286936238, price_delta_5m=0.005787441654459265, price_delta_15m=0.014415420443988847, volatility_window=0.022976355421390794, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=120, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.8, structural_importance=0.8, raw_json={'timestamp': '05:31:00', 'event_type': 'pre_session_redelivery', 'liquidity_type': 'cross_session', 'target_level': 'three_day_am_session_fpfvg', 'magnitude': 'high', 'context': 'three_day_am_session_fpfvg_pre_session_redelivery'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.01193251154101764, price_delta_5m=-0.009851649926445608, price_delta_15m=0.00613113243519949, volatility_window=0.03962966244441385, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=40, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:01:00', 'event_type': 'fpfvg_formation', 'liquidity_type': 'native_session', 'target_level': 'premarket_fpfvg_premium_discount', 'magnitude': 'medium', 'context': 'premarket_native_fpfvg_formation_7_00_gap'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.017038131243845057, price_delta_5m=0.0003664090435447344, price_delta_15m=-0.00528312943860343, volatility_window=0.013175106025307746, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:01:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'previous_day_lunch_fpfvg_redelivery_premarket_formation'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.0021486052018834544, price_delta_5m=0.0015400309935117065, price_delta_15m=-0.009184163229332342, volatility_window=0.02187683611110018, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.3, structural_importance=0.3, raw_json={'timestamp': '07:02:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'midnight_opening_range_fpfvg', 'magnitude': 'low', 'context': 'midnight_opening_range_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=9.0, daily_phase_sin=0.9550199444571866, daily_phase_cos=-0.29654157497557104, session_position=0.06040268456375839, time_to_close=140.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.012915564484351182, price_delta_5m=0.008649707517227052, price_delta_15m=0.006933340173664488, volatility_window=0.04028417689751368, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:09:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'second_previous_day_lunch_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=10.0, daily_phase_sin=0.9537169507482269, daily_phase_cos=-0.30070579950427295, session_position=0.06711409395973154, time_to_close=139.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.002712333819543773, price_delta_5m=0.01407135675535945, price_delta_15m=0.015363483918397225, volatility_window=0.03853307143377771, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.3, structural_importance=0.3, raw_json={'timestamp': '07:10:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'midnight_opening_range_fpfvg', 'magnitude': 'low', 'context': 'midnight_opening_range_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=30.0, daily_phase_sin=0.9238795325112867, daily_phase_cos=-0.3826834323650897, session_position=0.20134228187919462, time_to_close=119.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.008281346080590692, price_delta_5m=-0.015370085259695633, price_delta_15m=-0.002872409079960134, volatility_window=0.0188634891561607, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:30:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.016380381276437896, price_delta_5m=0.009728806167555207, price_delta_15m=-0.001105808208716206, volatility_window=0.03590704752277652, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:33:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'second_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=51.0, daily_phase_sin=0.8849876374630419, daily_phase_cos=-0.4656145203251114, session_position=0.3422818791946309, time_to_close=98.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.00025204925974783377, price_delta_5m=-0.009961870641620999, price_delta_15m=-0.00421079622263264, volatility_window=0.02139283996638394, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:51:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'third_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=58.0, daily_phase_sin=0.8703556959398997, daily_phase_cos=-0.49242356010346694, session_position=0.38926174496644295, time_to_close=91.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.0010706659943124236, price_delta_5m=-0.010471183462725004, price_delta_15m=-0.005835737310386946, volatility_window=0.02623987381829955, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:58:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'third_previous_day_lunch_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=62.0, daily_phase_sin=0.8616291604415257, daily_phase_cos=-0.5075383629607042, session_position=0.4161073825503356, time_to_close=87.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=1.888781359341885e-05, price_delta_5m=0.010281651121781235, price_delta_15m=0.01759547927408395, volatility_window=0.041467848753788, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '08:02:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'previous_day_lunch_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.026082965559014553, price_delta_5m=-0.009001087641110711, price_delta_15m=-0.0011113325866580117, volatility_window=0.04041256291193734, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.8, structural_importance=0.8, raw_json={'timestamp': '08:12:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'three_day_am_session_fpfvg', 'magnitude': 'high', 'context': 'three_day_am_session_fpfvg_redelivery_with_open_price'})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.007180775709185869, price_delta_5m=0.00047411987903519143, price_delta_15m=-0.008804903296754894, volatility_window=0.036811501595333265, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '08:22:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'london_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=85.0, daily_phase_sin=0.8064446042674828, daily_phase_cos=-0.591309648363582, session_position=0.5704697986577181, time_to_close=64.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.02155373929949503, price_delta_5m=0.004784252167525398, price_delta_15m=0.00462020587430439, volatility_window=0.016250661760332602, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '08:25:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'fourth_previous_day_lunch_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=134.0, daily_phase_sin=0.6626200482157374, daily_phase_cos=-0.7489557207890023, session_position=0.8993288590604027, time_to_close=15.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.016060088050833147, price_delta_5m=-0.009852328969170696, price_delta_15m=-0.003530461968116576, volatility_window=0.015772760272149557, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '09:14:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'fourth_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=135.0, daily_phase_sin=0.6593458151000686, daily_phase_cos=-0.7518398074789776, session_position=0.9060402684563759, time_to_close=14.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.009980472720265487, price_delta_5m=0.003061628399902729, price_delta_15m=-0.024649948750451598, volatility_window=0.044849025339811416, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '09:15:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'second_london_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=147.0, daily_phase_sin=0.6190939493098342, daily_phase_cos=-0.7853169308807447, session_position=0.9865771812080537, time_to_close=2.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.006957179632768808, price_delta_5m=-0.00461358526789763, price_delta_15m=0.00857747453325324, volatility_window=0.025913242017040816, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '09:27:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'final_previous_day_lunch_fpfvg_rebalance'})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 1,
        "feature_idx": 0
      },
      {
        "source": 1,
        "target": 2,
        "feature_idx": 1
      },
      {
        "source": 2,
        "target": 3,
        "feature_idx": 2
      },
      {
        "source": 3,
        "target": 4,
        "feature_idx": 3
      },
      {
        "source": 4,
        "target": 5,
        "feature_idx": 4
      },
      {
        "source": 5,
        "target": 6,
        "feature_idx": 5
      },
      {
        "source": 6,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 8,
        "feature_idx": 7
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 8
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 9
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 10
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 11
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 12
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 13
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 14
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 15
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 16
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 17
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 18
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 19
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 20
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 21
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 22
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 23
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 24
      },
      {
        "source": 26,
        "target": 27,
        "feature_idx": 25
      },
      {
        "source": 27,
        "target": 28,
        "feature_idx": 26
      },
      {
        "source": 28,
        "target": 29,
        "feature_idx": 27
      },
      {
        "source": 29,
        "target": 30,
        "feature_idx": 28
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 29
      },
      {
        "source": 31,
        "target": 32,
        "feature_idx": 30
      },
      {
        "source": 32,
        "target": 33,
        "feature_idx": 31
      },
      {
        "source": 33,
        "target": 34,
        "feature_idx": 32
      },
      {
        "source": 34,
        "target": 35,
        "feature_idx": 33
      },
      {
        "source": 35,
        "target": 36,
        "feature_idx": 34
      },
      {
        "source": 36,
        "target": 37,
        "feature_idx": 35
      },
      {
        "source": 37,
        "target": 38,
        "feature_idx": 36
      },
      {
        "source": 38,
        "target": 39,
        "feature_idx": 37
      },
      {
        "source": 39,
        "target": 40,
        "feature_idx": 38
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [],
    "temporal_echo": [
      {
        "source": 0,
        "target": 25,
        "feature_idx": 39
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 40
      },
      {
        "source": 0,
        "target": 27,
        "feature_idx": 41
      },
      {
        "source": 0,
        "target": 28,
        "feature_idx": 42
      },
      {
        "source": 0,
        "target": 29,
        "feature_idx": 43
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 44
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 45
      },
      {
        "source": 1,
        "target": 27,
        "feature_idx": 46
      },
      {
        "source": 1,
        "target": 28,
        "feature_idx": 47
      },
      {
        "source": 1,
        "target": 29,
        "feature_idx": 48
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 49
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 50
      },
      {
        "source": 2,
        "target": 27,
        "feature_idx": 51
      },
      {
        "source": 2,
        "target": 28,
        "feature_idx": 52
      },
      {
        "source": 2,
        "target": 29,
        "feature_idx": 53
      },
      {
        "source": 2,
        "target": 30,
        "feature_idx": 54
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 55
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 56
      },
      {
        "source": 3,
        "target": 27,
        "feature_idx": 57
      },
      {
        "source": 3,
        "target": 28,
        "feature_idx": 58
      },
      {
        "source": 3,
        "target": 29,
        "feature_idx": 59
      },
      {
        "source": 3,
        "target": 30,
        "feature_idx": 60
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 61
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 62
      },
      {
        "source": 4,
        "target": 27,
        "feature_idx": 63
      },
      {
        "source": 4,
        "target": 28,
        "feature_idx": 64
      },
      {
        "source": 4,
        "target": 29,
        "feature_idx": 65
      },
      {
        "source": 4,
        "target": 30,
        "feature_idx": 66
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 67
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 68
      },
      {
        "source": 5,
        "target": 27,
        "feature_idx": 69
      },
      {
        "source": 5,
        "target": 28,
        "feature_idx": 70
      },
      {
        "source": 5,
        "target": 29,
        "feature_idx": 71
      },
      {
        "source": 5,
        "target": 30,
        "feature_idx": 72
      },
      {
        "source": 5,
        "target": 31,
        "feature_idx": 73
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 74
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 75
      },
      {
        "source": 6,
        "target": 27,
        "feature_idx": 76
      },
      {
        "source": 6,
        "target": 28,
        "feature_idx": 77
      },
      {
        "source": 6,
        "target": 29,
        "feature_idx": 78
      },
      {
        "source": 6,
        "target": 30,
        "feature_idx": 79
      },
      {
        "source": 6,
        "target": 31,
        "feature_idx": 80
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 81
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 82
      },
      {
        "source": 7,
        "target": 27,
        "feature_idx": 83
      },
      {
        "source": 7,
        "target": 28,
        "feature_idx": 84
      },
      {
        "source": 7,
        "target": 29,
        "feature_idx": 85
      },
      {
        "source": 7,
        "target": 30,
        "feature_idx": 86
      },
      {
        "source": 7,
        "target": 31,
        "feature_idx": 87
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 88
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 89
      },
      {
        "source": 8,
        "target": 27,
        "feature_idx": 90
      },
      {
        "source": 8,
        "target": 28,
        "feature_idx": 91
      },
      {
        "source": 8,
        "target": 29,
        "feature_idx": 92
      },
      {
        "source": 8,
        "target": 30,
        "feature_idx": 93
      },
      {
        "source": 8,
        "target": 31,
        "feature_idx": 94
      },
      {
        "source": 8,
        "target": 32,
        "feature_idx": 95
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 96
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 97
      },
      {
        "source": 9,
        "target": 27,
        "feature_idx": 98
      },
      {
        "source": 9,
        "target": 28,
        "feature_idx": 99
      },
      {
        "source": 9,
        "target": 29,
        "feature_idx": 100
      },
      {
        "source": 9,
        "target": 30,
        "feature_idx": 101
      },
      {
        "source": 9,
        "target": 31,
        "feature_idx": 102
      },
      {
        "source": 9,
        "target": 32,
        "feature_idx": 103
      },
      {
        "source": 10,
        "target": 28,
        "feature_idx": 104
      },
      {
        "source": 10,
        "target": 29,
        "feature_idx": 105
      },
      {
        "source": 10,
        "target": 30,
        "feature_idx": 106
      },
      {
        "source": 10,
        "target": 31,
        "feature_idx": 107
      },
      {
        "source": 10,
        "target": 32,
        "feature_idx": 108
      },
      {
        "source": 10,
        "target": 33,
        "feature_idx": 109
      },
      {
        "source": 11,
        "target": 28,
        "feature_idx": 110
      },
      {
        "source": 11,
        "target": 29,
        "feature_idx": 111
      },
      {
        "source": 11,
        "target": 30,
        "feature_idx": 112
      },
      {
        "source": 11,
        "target": 31,
        "feature_idx": 113
      },
      {
        "source": 11,
        "target": 32,
        "feature_idx": 114
      },
      {
        "source": 11,
        "target": 33,
        "feature_idx": 115
      },
      {
        "source": 11,
        "target": 34,
        "feature_idx": 116
      },
      {
        "source": 12,
        "target": 30,
        "feature_idx": 117
      },
      {
        "source": 12,
        "target": 31,
        "feature_idx": 118
      },
      {
        "source": 12,
        "target": 32,
        "feature_idx": 119
      },
      {
        "source": 12,
        "target": 33,
        "feature_idx": 120
      },
      {
        "source": 12,
        "target": 34,
        "feature_idx": 121
      },
      {
        "source": 12,
        "target": 35,
        "feature_idx": 122
      },
      {
        "source": 13,
        "target": 32,
        "feature_idx": 123
      },
      {
        "source": 13,
        "target": 33,
        "feature_idx": 124
      },
      {
        "source": 13,
        "target": 34,
        "feature_idx": 125
      },
      {
        "source": 13,
        "target": 35,
        "feature_idx": 126
      },
      {
        "source": 13,
        "target": 36,
        "feature_idx": 127
      },
      {
        "source": 13,
        "target": 37,
        "feature_idx": 128
      },
      {
        "source": 14,
        "target": 32,
        "feature_idx": 129
      },
      {
        "source": 14,
        "target": 33,
        "feature_idx": 130
      },
      {
        "source": 14,
        "target": 34,
        "feature_idx": 131
      },
      {
        "source": 14,
        "target": 35,
        "feature_idx": 132
      },
      {
        "source": 14,
        "target": 36,
        "feature_idx": 133
      },
      {
        "source": 14,
        "target": 37,
        "feature_idx": 134
      },
      {
        "source": 15,
        "target": 32,
        "feature_idx": 135
      },
      {
        "source": 15,
        "target": 33,
        "feature_idx": 136
      },
      {
        "source": 15,
        "target": 34,
        "feature_idx": 137
      },
      {
        "source": 15,
        "target": 35,
        "feature_idx": 138
      },
      {
        "source": 15,
        "target": 36,
        "feature_idx": 139
      },
      {
        "source": 15,
        "target": 37,
        "feature_idx": 140
      },
      {
        "source": 16,
        "target": 32,
        "feature_idx": 141
      },
      {
        "source": 16,
        "target": 33,
        "feature_idx": 142
      },
      {
        "source": 16,
        "target": 34,
        "feature_idx": 143
      },
      {
        "source": 16,
        "target": 35,
        "feature_idx": 144
      },
      {
        "source": 16,
        "target": 36,
        "feature_idx": 145
      },
      {
        "source": 16,
        "target": 37,
        "feature_idx": 146
      },
      {
        "source": 17,
        "target": 33,
        "feature_idx": 147
      },
      {
        "source": 17,
        "target": 34,
        "feature_idx": 148
      },
      {
        "source": 17,
        "target": 35,
        "feature_idx": 149
      },
      {
        "source": 17,
        "target": 36,
        "feature_idx": 150
      },
      {
        "source": 17,
        "target": 37,
        "feature_idx": 151
      },
      {
        "source": 18,
        "target": 35,
        "feature_idx": 152
      },
      {
        "source": 18,
        "target": 36,
        "feature_idx": 153
      },
      {
        "source": 18,
        "target": 37,
        "feature_idx": 154
      },
      {
        "source": 19,
        "target": 35,
        "feature_idx": 155
      },
      {
        "source": 19,
        "target": 36,
        "feature_idx": 156
      },
      {
        "source": 19,
        "target": 37,
        "feature_idx": 157
      },
      {
        "source": 20,
        "target": 38,
        "feature_idx": 158
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 159
      },
      {
        "source": 20,
        "target": 40,
        "feature_idx": 160
      },
      {
        "source": 21,
        "target": 38,
        "feature_idx": 161
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 162
      },
      {
        "source": 21,
        "target": 40,
        "feature_idx": 163
      },
      {
        "source": 22,
        "target": 38,
        "feature_idx": 164
      },
      {
        "source": 22,
        "target": 39,
        "feature_idx": 165
      },
      {
        "source": 22,
        "target": 40,
        "feature_idx": 166
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    