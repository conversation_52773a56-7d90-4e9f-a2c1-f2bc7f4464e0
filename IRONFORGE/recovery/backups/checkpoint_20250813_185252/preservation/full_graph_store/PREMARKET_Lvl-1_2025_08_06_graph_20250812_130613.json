{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22
    ],
    "5m": [],
    "15m": [
      23,
      24,
      25,
      26,
      27,
      28,
      29,
      30,
      31,
      32,
      33,
      34,
      35,
      36,
      37,
      38,
      39,
      40
    ],
    "1h": [],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=-0.002338048020907039, price_delta_5m=0.004054673432914521, price_delta_15m=0.016458697276739064, volatility_window=0.03328941605623328, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23144.25, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231375, price_delta_1m=0.0218396396654286, price_delta_5m=0.023240666788204134, price_delta_15m=0.004548618769790499, volatility_window=0.03436018178986148, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=116, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23137.5, 'movement_type': 'session_low_immediate'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=0.011201359656886473, price_delta_5m=0.00887770742221599, price_delta_15m=0.0005254971226393566, volatility_window=0.02174665017522876, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=117, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price_level': 23165.0, 'movement_type': 'premarket_fpfvg_formation_premium'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23158, price_delta_1m=0.008461803235360414, price_delta_5m=-0.002374445522264289, price_delta_15m=-0.005775381850881168, volatility_window=0.021238846263160575, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=118, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:01:00', 'price_level': 23158.0, 'movement_type': 'premarket_fpfvg_formation_discount'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231725, price_delta_1m=0.017447456184143555, price_delta_5m=-0.01464456582175993, price_delta_15m=-0.0031682243150228883, volatility_window=0.014402750403049223, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:02:00', 'price_level': 23172.5, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=8.0, daily_phase_sin=0.9563047559630355, daily_phase_cos=-0.29237170472273666, session_position=0.053691275167785234, time_to_close=141.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315225, price_delta_1m=0.009023943816387218, price_delta_5m=0.006925860742770624, price_delta_15m=-0.015212752841394553, volatility_window=0.04760187613961363, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:08:00', 'price_level': 23152.25, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=11.0, daily_phase_sin=0.9523957996432784, daily_phase_cos=-0.30486429902801077, session_position=0.0738255033557047, time_to_close=138.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317575, price_delta_1m=-0.008643106451222612, price_delta_5m=-0.0010933706782902976, price_delta_15m=-0.0006227232442576317, volatility_window=0.04454209389248019, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:11:00', 'price_level': 23175.75, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=12.0, daily_phase_sin=0.9510565162951536, daily_phase_cos=-0.30901699437494734, session_position=0.08053691275167785, time_to_close=137.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315425, price_delta_1m=0.0060680162618916254, price_delta_5m=0.0021040276050363444, price_delta_15m=-0.017481888685996955, volatility_window=0.03154233641247867, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:12:00', 'price_level': 23154.25, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=22.0, daily_phase_sin=0.9366721892483976, daily_phase_cos=-0.35020738125946754, session_position=0.1476510067114094, time_to_close=127.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23178, price_delta_1m=-0.007815740462825509, price_delta_5m=-0.01545953997628377, price_delta_15m=0.004593075044706765, volatility_window=0.0474279849065692, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:22:00', 'price_level': 23178.0, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=26.0, daily_phase_sin=0.9304175679820246, daily_phase_cos=-0.3665012267242972, session_position=0.174496644295302, time_to_close=123.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=0.0027207938555822513, price_delta_5m=-0.01833766247043649, price_delta_15m=-0.01762598153033942, volatility_window=0.0328253726651426, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:26:00', 'price_level': 23165.0, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319675, price_delta_1m=0.007750138090123056, price_delta_5m=-0.00030226282187734307, price_delta_15m=-0.004697042072900249, volatility_window=0.016491286136661162, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=34, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:32:00', 'price_level': 23196.75, 'movement_type': 'session_high_reversal_point'})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23181, price_delta_1m=-0.010460539813451398, price_delta_5m=0.0162400113598336, price_delta_15m=0.0020730366324457528, volatility_window=0.039729143330973674, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:33:00', 'price_level': 23181.0, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317825, price_delta_1m=0.01827534940650133, price_delta_5m=0.002219927659615405, price_delta_15m=-0.007940792612223354, volatility_window=0.02486739882735538, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:52:00', 'price_level': 23178.25, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=66.0, daily_phase_sin=0.8526401643540923, daily_phase_cos=-0.5224985647159488, session_position=0.4429530201342282, time_to_close=83.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314825, price_delta_1m=0.00530048395935704, price_delta_5m=-0.00405010367572544, price_delta_15m=0.01946511833302654, volatility_window=0.012957211026110471, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:06:00', 'price_level': 23148.25, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=68.0, daily_phase_sin=0.8480480961564261, daily_phase_cos=-0.5299192642332048, session_position=0.4563758389261745, time_to_close=81.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316025, price_delta_1m=0.012588039884564037, price_delta_5m=-0.010364070707869297, price_delta_15m=-0.0002290279131830522, volatility_window=0.03975341547952735, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=36, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:08:00', 'price_level': 23160.25, 'movement_type': 'retracement_high'})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=-0.0033456792470830444, price_delta_5m=0.005078484050254333, price_delta_15m=0.000805593972001214, volatility_window=0.019590084279124894, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=119, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:12:00', 'price_level': 23144.25, 'movement_type': 'premarket_open_price_touch'})",
    "RichNodeFeature(time_minutes=73.0, daily_phase_sin=0.8362861558477594, daily_phase_cos=-0.548293229519914, session_position=0.4899328859060403, time_to_close=76.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314025, price_delta_1m=0.0028088401318059394, price_delta_5m=0.019328344977873228, price_delta_15m=0.018971024322547946, volatility_window=0.026969951514479375, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=53, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:13:00', 'price_level': 23140.25, 'movement_type': 'expansion_low_reversal_point'})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2318625, price_delta_1m=0.000862183529600417, price_delta_5m=-0.005116499307927454, price_delta_15m=-0.004942948090054736, volatility_window=0.027953496426883696, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:22:00', 'price_level': 23186.25, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=92.0, daily_phase_sin=0.788010753606722, daily_phase_cos=-0.6156614753256582, session_position=0.6174496644295302, time_to_close=57.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314675, price_delta_1m=-0.012794805991967265, price_delta_5m=-0.01052855896750036, price_delta_15m=0.008331985149010585, volatility_window=0.0271558583881436, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=33, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:32:00', 'price_level': 23146.75, 'movement_type': 'retracement_low'})",
    "RichNodeFeature(time_minutes=97.0, daily_phase_sin=0.7743926440821857, daily_phase_cos=-0.6327053285625159, session_position=0.6510067114093959, time_to_close=52.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231775, price_delta_1m=-0.002138394149478502, price_delta_5m=0.000644570730616217, price_delta_15m=0.003918615522062065, volatility_window=0.027485054204929832, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=30, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:37:00', 'price_level': 23177.5, 'movement_type': 'expansion_high'})",
    "RichNodeFeature(time_minutes=137.0, daily_phase_sin=0.6527597524627223, daily_phase_cos=-0.7575649843840497, session_position=0.9194630872483222, time_to_close=12.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319375, price_delta_1m=-0.0005933697524067312, price_delta_5m=0.00025729306767231466, price_delta_15m=-0.0003577285474429286, volatility_window=0.012022764553566833, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=61, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:17:00', 'price_level': 23193.75, 'movement_type': 'expansion_high_reversal_point'})",
    "RichNodeFeature(time_minutes=145.0, daily_phase_sin=0.6259234721840592, daily_phase_cos=-0.7798844830928817, session_position=0.9731543624161074, time_to_close=4.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23167, price_delta_1m=0.009331547136867914, price_delta_5m=-0.0032337226427500087, price_delta_15m=0.009555369719238732, volatility_window=0.042301739698106924, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=22, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:25:00', 'price_level': 23167.0, 'movement_type': 'expansion_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=-0.018529209277862082, price_delta_5m=-0.013806222025287158, price_delta_15m=1.003027224630459e-05, volatility_window=0.0417982623642743, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23168.75, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=-93.0, daily_phase_sin=0.9896513868196702, daily_phase_cos=0.1434926219911793, session_position=-0.6241610738255033, time_to_close=242.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.004740090365363742, price_delta_5m=-0.013226376395556332, price_delta_15m=-0.010987830378874724, volatility_window=0.044568912932845144, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=115, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '05:27:00', 'event_type': 'pre_session_takeout', 'liquidity_type': 'cross_session', 'target_level': 'london_session_low', 'magnitude': 'medium', 'context': 'london_session_low_taken_before_premarket_open'})",
    "RichNodeFeature(time_minutes=-89.0, daily_phase_sin=0.992004949679715, daily_phase_cos=0.1261989691358297, session_position=-0.5973154362416108, time_to_close=238.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=1.5250767411545026e-05, price_delta_5m=-0.0032865618802708292, price_delta_15m=-0.014627698109623952, volatility_window=0.01977377168245943, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=120, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.8, structural_importance=0.8, raw_json={'timestamp': '05:31:00', 'event_type': 'pre_session_redelivery', 'liquidity_type': 'cross_session', 'target_level': 'three_day_am_session_fpfvg', 'magnitude': 'high', 'context': 'three_day_am_session_fpfvg_pre_session_redelivery'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.003618681619971978, price_delta_5m=-0.02121315240781668, price_delta_15m=0.001836878564525134, volatility_window=0.048607733264893295, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=40, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:01:00', 'event_type': 'fpfvg_formation', 'liquidity_type': 'native_session', 'target_level': 'premarket_fpfvg_premium_discount', 'magnitude': 'medium', 'context': 'premarket_native_fpfvg_formation_7_00_gap'})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.00837376596079454, price_delta_5m=0.003214748008341169, price_delta_15m=0.004296064707626203, volatility_window=0.04697712511272782, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:01:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'previous_day_lunch_fpfvg_redelivery_premarket_formation'})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.005542740453542171, price_delta_5m=-0.004270392460747789, price_delta_15m=0.010952856333542246, volatility_window=0.03981729592644903, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.3, structural_importance=0.3, raw_json={'timestamp': '07:02:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'midnight_opening_range_fpfvg', 'magnitude': 'low', 'context': 'midnight_opening_range_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=9.0, daily_phase_sin=0.9550199444571866, daily_phase_cos=-0.29654157497557104, session_position=0.06040268456375839, time_to_close=140.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.02264730999414972, price_delta_5m=0.005474005852415301, price_delta_15m=-0.016203395485815463, volatility_window=0.016290707441090187, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:09:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'second_previous_day_lunch_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=10.0, daily_phase_sin=0.9537169507482269, daily_phase_cos=-0.30070579950427295, session_position=0.06711409395973154, time_to_close=139.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.01334386881597027, price_delta_5m=0.018853658130454146, price_delta_15m=-0.010951039971382082, volatility_window=0.032401261280399954, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.3, structural_importance=0.3, raw_json={'timestamp': '07:10:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'midnight_opening_range_fpfvg', 'magnitude': 'low', 'context': 'midnight_opening_range_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=30.0, daily_phase_sin=0.9238795325112867, daily_phase_cos=-0.3826834323650897, session_position=0.20134228187919462, time_to_close=119.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.013965711520168953, price_delta_5m=-0.0021231135697402383, price_delta_15m=-0.007478723462210551, volatility_window=0.0135066693365712, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:30:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.0009248043005921955, price_delta_5m=-0.0028021209119723756, price_delta_15m=0.016618242932928545, volatility_window=0.029752239031312966, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:33:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'second_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=51.0, daily_phase_sin=0.8849876374630419, daily_phase_cos=-0.4656145203251114, session_position=0.3422818791946309, time_to_close=98.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.01016990779323427, price_delta_5m=0.00844785481814035, price_delta_15m=-0.01669555133253068, volatility_window=0.043675779254377, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '07:51:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'third_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=58.0, daily_phase_sin=0.8703556959398997, daily_phase_cos=-0.49242356010346694, session_position=0.38926174496644295, time_to_close=91.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.004022731700138844, price_delta_5m=-0.006038508374723183, price_delta_15m=-0.008037015935859338, volatility_window=0.0399469325245074, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '07:58:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'third_previous_day_lunch_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=62.0, daily_phase_sin=0.8616291604415257, daily_phase_cos=-0.5075383629607042, session_position=0.4161073825503356, time_to_close=87.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.0028805008524274652, price_delta_5m=0.0069414415187524945, price_delta_15m=0.005255718286995145, volatility_window=0.015939544258929968, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '08:02:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'previous_day_lunch_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.002075421638442705, price_delta_5m=0.003728180987637992, price_delta_15m=-0.00446041895943502, volatility_window=0.018577316943953044, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.8, structural_importance=0.8, raw_json={'timestamp': '08:12:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'three_day_am_session_fpfvg', 'magnitude': 'high', 'context': 'three_day_am_session_fpfvg_redelivery_with_open_price'})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.008642471929151943, price_delta_5m=-0.0011618067433839605, price_delta_15m=0.005750913545931307, volatility_window=0.03764778612252546, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '08:22:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'london_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=85.0, daily_phase_sin=0.8064446042674828, daily_phase_cos=-0.591309648363582, session_position=0.5704697986577181, time_to_close=64.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=0.004867280725758145, price_delta_5m=0.010272274165966924, price_delta_15m=0.008943561649598666, volatility_window=0.04429489505725102, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '08:25:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'fourth_previous_day_lunch_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=134.0, daily_phase_sin=0.6626200482157374, daily_phase_cos=-0.7489557207890023, session_position=0.8993288590604027, time_to_close=15.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.003798606230446283, price_delta_5m=0.0068331955891555276, price_delta_15m=-0.012239932209584697, volatility_window=0.035663234461355046, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '09:14:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'fourth_london_fpfvg_rebalance'})",
    "RichNodeFeature(time_minutes=135.0, daily_phase_sin=0.6593458151000686, daily_phase_cos=-0.7518398074789776, session_position=0.9060402684563759, time_to_close=14.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.017639006722011484, price_delta_5m=-0.01283148593353979, price_delta_15m=-0.0015371343293116187, volatility_window=0.040824873035406765, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=44, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.1, structural_importance=0.1, raw_json={'timestamp': '09:15:00', 'event_type': 'redelivery', 'liquidity_type': 'cross_session', 'target_level': 'london_fpfvg', 'magnitude': 'minimal', 'context': 'second_london_fpfvg_redelivery'})",
    "RichNodeFeature(time_minutes=147.0, daily_phase_sin=0.6190939493098342, daily_phase_cos=-0.7853169308807447, session_position=0.9865771812080537, time_to_close=2.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.0, price_delta_1m=-0.019513221289722743, price_delta_5m=-0.006839628659171724, price_delta_15m=-0.00990996331264032, volatility_window=0.04792427654989662, energy_state=0.14006, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=41, timeframe_source=2, liquidity_type=1, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.5, structural_importance=0.5, raw_json={'timestamp': '09:27:00', 'event_type': 'rebalance', 'liquidity_type': 'cross_session', 'target_level': 'previous_day_lunch_fpfvg', 'magnitude': 'medium', 'context': 'final_previous_day_lunch_fpfvg_rebalance'})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 1,
        "feature_idx": 0
      },
      {
        "source": 1,
        "target": 2,
        "feature_idx": 1
      },
      {
        "source": 2,
        "target": 3,
        "feature_idx": 2
      },
      {
        "source": 3,
        "target": 4,
        "feature_idx": 3
      },
      {
        "source": 4,
        "target": 5,
        "feature_idx": 4
      },
      {
        "source": 5,
        "target": 6,
        "feature_idx": 5
      },
      {
        "source": 6,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 8,
        "feature_idx": 7
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 8
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 9
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 10
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 11
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 12
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 13
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 14
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 15
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 16
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 17
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 18
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 19
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 20
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 21
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 22
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 23
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 24
      },
      {
        "source": 26,
        "target": 27,
        "feature_idx": 25
      },
      {
        "source": 27,
        "target": 28,
        "feature_idx": 26
      },
      {
        "source": 28,
        "target": 29,
        "feature_idx": 27
      },
      {
        "source": 29,
        "target": 30,
        "feature_idx": 28
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 29
      },
      {
        "source": 31,
        "target": 32,
        "feature_idx": 30
      },
      {
        "source": 32,
        "target": 33,
        "feature_idx": 31
      },
      {
        "source": 33,
        "target": 34,
        "feature_idx": 32
      },
      {
        "source": 34,
        "target": 35,
        "feature_idx": 33
      },
      {
        "source": 35,
        "target": 36,
        "feature_idx": 34
      },
      {
        "source": 36,
        "target": 37,
        "feature_idx": 35
      },
      {
        "source": 37,
        "target": 38,
        "feature_idx": 36
      },
      {
        "source": 38,
        "target": 39,
        "feature_idx": 37
      },
      {
        "source": 39,
        "target": 40,
        "feature_idx": 38
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [],
    "temporal_echo": [
      {
        "source": 0,
        "target": 25,
        "feature_idx": 39
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 40
      },
      {
        "source": 0,
        "target": 27,
        "feature_idx": 41
      },
      {
        "source": 0,
        "target": 28,
        "feature_idx": 42
      },
      {
        "source": 0,
        "target": 29,
        "feature_idx": 43
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 44
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 45
      },
      {
        "source": 1,
        "target": 27,
        "feature_idx": 46
      },
      {
        "source": 1,
        "target": 28,
        "feature_idx": 47
      },
      {
        "source": 1,
        "target": 29,
        "feature_idx": 48
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 49
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 50
      },
      {
        "source": 2,
        "target": 27,
        "feature_idx": 51
      },
      {
        "source": 2,
        "target": 28,
        "feature_idx": 52
      },
      {
        "source": 2,
        "target": 29,
        "feature_idx": 53
      },
      {
        "source": 2,
        "target": 30,
        "feature_idx": 54
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 55
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 56
      },
      {
        "source": 3,
        "target": 27,
        "feature_idx": 57
      },
      {
        "source": 3,
        "target": 28,
        "feature_idx": 58
      },
      {
        "source": 3,
        "target": 29,
        "feature_idx": 59
      },
      {
        "source": 3,
        "target": 30,
        "feature_idx": 60
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 61
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 62
      },
      {
        "source": 4,
        "target": 27,
        "feature_idx": 63
      },
      {
        "source": 4,
        "target": 28,
        "feature_idx": 64
      },
      {
        "source": 4,
        "target": 29,
        "feature_idx": 65
      },
      {
        "source": 4,
        "target": 30,
        "feature_idx": 66
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 67
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 68
      },
      {
        "source": 5,
        "target": 27,
        "feature_idx": 69
      },
      {
        "source": 5,
        "target": 28,
        "feature_idx": 70
      },
      {
        "source": 5,
        "target": 29,
        "feature_idx": 71
      },
      {
        "source": 5,
        "target": 30,
        "feature_idx": 72
      },
      {
        "source": 5,
        "target": 31,
        "feature_idx": 73
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 74
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 75
      },
      {
        "source": 6,
        "target": 27,
        "feature_idx": 76
      },
      {
        "source": 6,
        "target": 28,
        "feature_idx": 77
      },
      {
        "source": 6,
        "target": 29,
        "feature_idx": 78
      },
      {
        "source": 6,
        "target": 30,
        "feature_idx": 79
      },
      {
        "source": 6,
        "target": 31,
        "feature_idx": 80
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 81
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 82
      },
      {
        "source": 7,
        "target": 27,
        "feature_idx": 83
      },
      {
        "source": 7,
        "target": 28,
        "feature_idx": 84
      },
      {
        "source": 7,
        "target": 29,
        "feature_idx": 85
      },
      {
        "source": 7,
        "target": 30,
        "feature_idx": 86
      },
      {
        "source": 7,
        "target": 31,
        "feature_idx": 87
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 88
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 89
      },
      {
        "source": 8,
        "target": 27,
        "feature_idx": 90
      },
      {
        "source": 8,
        "target": 28,
        "feature_idx": 91
      },
      {
        "source": 8,
        "target": 29,
        "feature_idx": 92
      },
      {
        "source": 8,
        "target": 30,
        "feature_idx": 93
      },
      {
        "source": 8,
        "target": 31,
        "feature_idx": 94
      },
      {
        "source": 8,
        "target": 32,
        "feature_idx": 95
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 96
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 97
      },
      {
        "source": 9,
        "target": 27,
        "feature_idx": 98
      },
      {
        "source": 9,
        "target": 28,
        "feature_idx": 99
      },
      {
        "source": 9,
        "target": 29,
        "feature_idx": 100
      },
      {
        "source": 9,
        "target": 30,
        "feature_idx": 101
      },
      {
        "source": 9,
        "target": 31,
        "feature_idx": 102
      },
      {
        "source": 9,
        "target": 32,
        "feature_idx": 103
      },
      {
        "source": 10,
        "target": 28,
        "feature_idx": 104
      },
      {
        "source": 10,
        "target": 29,
        "feature_idx": 105
      },
      {
        "source": 10,
        "target": 30,
        "feature_idx": 106
      },
      {
        "source": 10,
        "target": 31,
        "feature_idx": 107
      },
      {
        "source": 10,
        "target": 32,
        "feature_idx": 108
      },
      {
        "source": 10,
        "target": 33,
        "feature_idx": 109
      },
      {
        "source": 11,
        "target": 28,
        "feature_idx": 110
      },
      {
        "source": 11,
        "target": 29,
        "feature_idx": 111
      },
      {
        "source": 11,
        "target": 30,
        "feature_idx": 112
      },
      {
        "source": 11,
        "target": 31,
        "feature_idx": 113
      },
      {
        "source": 11,
        "target": 32,
        "feature_idx": 114
      },
      {
        "source": 11,
        "target": 33,
        "feature_idx": 115
      },
      {
        "source": 11,
        "target": 34,
        "feature_idx": 116
      },
      {
        "source": 12,
        "target": 30,
        "feature_idx": 117
      },
      {
        "source": 12,
        "target": 31,
        "feature_idx": 118
      },
      {
        "source": 12,
        "target": 32,
        "feature_idx": 119
      },
      {
        "source": 12,
        "target": 33,
        "feature_idx": 120
      },
      {
        "source": 12,
        "target": 34,
        "feature_idx": 121
      },
      {
        "source": 12,
        "target": 35,
        "feature_idx": 122
      },
      {
        "source": 13,
        "target": 32,
        "feature_idx": 123
      },
      {
        "source": 13,
        "target": 33,
        "feature_idx": 124
      },
      {
        "source": 13,
        "target": 34,
        "feature_idx": 125
      },
      {
        "source": 13,
        "target": 35,
        "feature_idx": 126
      },
      {
        "source": 13,
        "target": 36,
        "feature_idx": 127
      },
      {
        "source": 13,
        "target": 37,
        "feature_idx": 128
      },
      {
        "source": 14,
        "target": 32,
        "feature_idx": 129
      },
      {
        "source": 14,
        "target": 33,
        "feature_idx": 130
      },
      {
        "source": 14,
        "target": 34,
        "feature_idx": 131
      },
      {
        "source": 14,
        "target": 35,
        "feature_idx": 132
      },
      {
        "source": 14,
        "target": 36,
        "feature_idx": 133
      },
      {
        "source": 14,
        "target": 37,
        "feature_idx": 134
      },
      {
        "source": 15,
        "target": 32,
        "feature_idx": 135
      },
      {
        "source": 15,
        "target": 33,
        "feature_idx": 136
      },
      {
        "source": 15,
        "target": 34,
        "feature_idx": 137
      },
      {
        "source": 15,
        "target": 35,
        "feature_idx": 138
      },
      {
        "source": 15,
        "target": 36,
        "feature_idx": 139
      },
      {
        "source": 15,
        "target": 37,
        "feature_idx": 140
      },
      {
        "source": 16,
        "target": 32,
        "feature_idx": 141
      },
      {
        "source": 16,
        "target": 33,
        "feature_idx": 142
      },
      {
        "source": 16,
        "target": 34,
        "feature_idx": 143
      },
      {
        "source": 16,
        "target": 35,
        "feature_idx": 144
      },
      {
        "source": 16,
        "target": 36,
        "feature_idx": 145
      },
      {
        "source": 16,
        "target": 37,
        "feature_idx": 146
      },
      {
        "source": 17,
        "target": 33,
        "feature_idx": 147
      },
      {
        "source": 17,
        "target": 34,
        "feature_idx": 148
      },
      {
        "source": 17,
        "target": 35,
        "feature_idx": 149
      },
      {
        "source": 17,
        "target": 36,
        "feature_idx": 150
      },
      {
        "source": 17,
        "target": 37,
        "feature_idx": 151
      },
      {
        "source": 18,
        "target": 35,
        "feature_idx": 152
      },
      {
        "source": 18,
        "target": 36,
        "feature_idx": 153
      },
      {
        "source": 18,
        "target": 37,
        "feature_idx": 154
      },
      {
        "source": 19,
        "target": 35,
        "feature_idx": 155
      },
      {
        "source": 19,
        "target": 36,
        "feature_idx": 156
      },
      {
        "source": 19,
        "target": 37,
        "feature_idx": 157
      },
      {
        "source": 20,
        "target": 38,
        "feature_idx": 158
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 159
      },
      {
        "source": 20,
        "target": 40,
        "feature_idx": 160
      },
      {
        "source": 21,
        "target": 38,
        "feature_idx": 161
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 162
      },
      {
        "source": 21,
        "target": 40,
        "feature_idx": 163
      },
      {
        "source": 22,
        "target": 38,
        "feature_idx": 164
      },
      {
        "source": 22,
        "target": 39,
        "feature_idx": 165
      },
      {
        "source": 22,
        "target": 40,
        "feature_idx": 166
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    