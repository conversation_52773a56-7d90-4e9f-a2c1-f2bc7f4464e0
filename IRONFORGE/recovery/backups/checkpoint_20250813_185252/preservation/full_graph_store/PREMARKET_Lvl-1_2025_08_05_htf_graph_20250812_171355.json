{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22,
      23,
      24,
      25,
      26,
      27,
      28,
      29,
      30,
      31,
      32,
      33,
      34
    ],
    "5m": [
      35,
      36,
      37,
      38,
      39,
      40,
      41
    ],
    "15m": [
      42,
      43,
      44
    ],
    "1h": [
      45
    ],
    "D": [
      46
    ],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2338475, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'price_level': 23384.75, 'open': 23384.75, 'high': 23384.75, 'low': 23384.75, 'close': 23384.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23383, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '07:02:00', 'price_level': 23383.0, 'open': 23383.0, 'high': 23383.0, 'low': 23383.0, 'close': 23383.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=5.0, daily_phase_sin=0.9600498543859287, daily_phase_cos=-0.2798290140309919, session_position=0.03355704697986577, time_to_close=144.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233825, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '07:05:00', 'price_level': 23382.5, 'open': 23382.5, 'high': 23382.5, 'low': 23382.5, 'close': 23382.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=5.0, daily_phase_sin=0.9600498543859287, daily_phase_cos=-0.2798290140309919, session_position=0.03355704697986577, time_to_close=144.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2337825, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '07:05:00', 'price_level': 23378.25, 'open': 23378.25, 'high': 23378.25, 'low': 23378.25, 'close': 23378.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=9.0, daily_phase_sin=0.9550199444571866, daily_phase_cos=-0.29654157497557104, session_position=0.06040268456375839, time_to_close=140.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233935, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 4, 'timestamp': '07:09:00', 'price_level': 23393.5, 'open': 23393.5, 'high': 23393.5, 'low': 23393.5, 'close': 23393.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=9.0, daily_phase_sin=0.9550199444571866, daily_phase_cos=-0.29654157497557104, session_position=0.06040268456375839, time_to_close=140.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23394, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 5, 'timestamp': '07:09:00', 'price_level': 23394.0, 'open': 23394.0, 'high': 23394.0, 'low': 23394.0, 'close': 23394.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=12.0, daily_phase_sin=0.9510565162951536, daily_phase_cos=-0.30901699437494734, session_position=0.08053691275167785, time_to_close=137.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2338775, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 6, 'timestamp': '07:12:00', 'price_level': 23387.75, 'open': 23387.75, 'high': 23387.75, 'low': 23387.75, 'close': 23387.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=22.0, daily_phase_sin=0.9366721892483976, daily_phase_cos=-0.35020738125946754, session_position=0.1476510067114094, time_to_close=127.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233825, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 7, 'timestamp': '07:22:00', 'price_level': 23382.5, 'open': 23382.5, 'high': 23382.5, 'low': 23382.5, 'close': 23382.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=24.0, daily_phase_sin=0.9335804264972017, daily_phase_cos=-0.35836794954530027, session_position=0.1610738255033557, time_to_close=125.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2338925, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 8, 'timestamp': '07:24:00', 'price_level': 23389.25, 'open': 23389.25, 'high': 23389.25, 'low': 23389.25, 'close': 23389.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=30.0, daily_phase_sin=0.9238795325112867, daily_phase_cos=-0.3826834323650897, session_position=0.20134228187919462, time_to_close=119.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2336, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 9, 'timestamp': '07:30:00', 'price_level': 23360.0, 'open': 23360.0, 'high': 23360.0, 'low': 23360.0, 'close': 23360.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23362, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 10, 'timestamp': '07:32:00', 'price_level': 23362.0, 'open': 23362.0, 'high': 23362.0, 'low': 23362.0, 'close': 23362.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=35.0, daily_phase_sin=0.9153114791194472, daily_phase_cos=-0.40274668985873713, session_position=0.2348993288590604, time_to_close=114.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233835, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 11, 'timestamp': '07:35:00', 'price_level': 23383.5, 'open': 23383.5, 'high': 23383.5, 'low': 23383.5, 'close': 23383.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=38.0, daily_phase_sin=0.9099612708765433, daily_phase_cos=-0.4146932426562388, session_position=0.2550335570469799, time_to_close=111.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233705, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 12, 'timestamp': '07:38:00', 'price_level': 23370.5, 'open': 23370.5, 'high': 23370.5, 'low': 23370.5, 'close': 23370.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=39.0, daily_phase_sin=0.9081431738250814, daily_phase_cos=-0.4186597375374278, session_position=0.26174496644295303, time_to_close=110.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2337575, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 13, 'timestamp': '07:39:00', 'price_level': 23375.75, 'open': 23375.75, 'high': 23375.75, 'low': 23375.75, 'close': 23375.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=41.0, daily_phase_sin=0.904455145454368, daily_phase_cos=-0.4265687399014584, session_position=0.2751677852348993, time_to_close=108.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23372, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 14, 'timestamp': '07:41:00', 'price_level': 23372.0, 'open': 23372.0, 'high': 23372.0, 'low': 23372.0, 'close': 23372.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=42.0, daily_phase_sin=0.9025852843498605, daily_phase_cos=-0.43051109680829536, session_position=0.28187919463087246, time_to_close=107.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2337325, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 15, 'timestamp': '07:42:00', 'price_level': 23373.25, 'open': 23373.25, 'high': 23373.25, 'low': 23373.25, 'close': 23373.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=46.0, daily_phase_sin=0.8949343616020251, daily_phase_cos=-0.4461978131098088, session_position=0.3087248322147651, time_to_close=103.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2339625, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 16, 'timestamp': '07:46:00', 'price_level': 23396.25, 'open': 23396.25, 'high': 23396.25, 'low': 23396.25, 'close': 23396.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=51.0, daily_phase_sin=0.8849876374630419, daily_phase_cos=-0.4656145203251114, session_position=0.3422818791946309, time_to_close=98.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233795, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 17, 'timestamp': '07:51:00', 'price_level': 23379.5, 'open': 23379.5, 'high': 23379.5, 'low': 23379.5, 'close': 23379.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2338925, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 18, 'timestamp': '07:52:00', 'price_level': 23389.25, 'open': 23389.25, 'high': 23389.25, 'low': 23389.25, 'close': 23389.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=69.0, daily_phase_sin=0.8457278217039732, daily_phase_cos=-0.5336145159156115, session_position=0.46308724832214765, time_to_close=80.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23376, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 19, 'timestamp': '08:09:00', 'price_level': 23376.0, 'open': 23376.0, 'high': 23376.0, 'low': 23376.0, 'close': 23376.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=70.0, daily_phase_sin=0.8433914458128858, daily_phase_cos=-0.5372996083468236, session_position=0.4697986577181208, time_to_close=79.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233785, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 20, 'timestamp': '08:10:00', 'price_level': 23378.5, 'open': 23378.5, 'high': 23378.5, 'low': 23378.5, 'close': 23378.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=74.0, daily_phase_sin=0.8338858220671682, daily_phase_cos=-0.5519369853120581, session_position=0.4966442953020134, time_to_close=75.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2340425, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 21, 'timestamp': '08:14:00', 'price_level': 23404.25, 'open': 23404.25, 'high': 23404.25, 'low': 23404.25, 'close': 23404.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=78.0, daily_phase_sin=0.8241261886220158, daily_phase_cos=-0.5664062369248326, session_position=0.5234899328859061, time_to_close=71.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2338, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 22, 'timestamp': '08:18:00', 'price_level': 23380.0, 'open': 23380.0, 'high': 23380.0, 'low': 23380.0, 'close': 23380.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=79.0, daily_phase_sin=0.8216469379421637, daily_phase_cos=-0.5699967625963029, session_position=0.5302013422818792, time_to_close=70.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23343, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 23, 'timestamp': '08:19:00', 'price_level': 23343.0, 'open': 23343.0, 'high': 23343.0, 'low': 23343.0, 'close': 23343.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=80.0, daily_phase_sin=0.819152044288992, daily_phase_cos=-0.5735764363510458, session_position=0.5369127516778524, time_to_close=69.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2337875, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 24, 'timestamp': '08:20:00', 'price_level': 23378.75, 'open': 23378.75, 'high': 23378.75, 'low': 23378.75, 'close': 23378.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=99.0, daily_phase_sin=0.7688418320734597, daily_phase_cos=-0.6394390019805846, session_position=0.6644295302013423, time_to_close=50.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233505, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 25, 'timestamp': '08:39:00', 'price_level': 23350.5, 'open': 23350.5, 'high': 23350.5, 'low': 23350.5, 'close': 23350.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=100.0, daily_phase_sin=0.7660444431189781, daily_phase_cos=-0.6427876096865393, session_position=0.6711409395973155, time_to_close=49.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233615, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 26, 'timestamp': '08:40:00', 'price_level': 23361.5, 'open': 23361.5, 'high': 23361.5, 'low': 23361.5, 'close': 23361.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=105.0, daily_phase_sin=0.7518398074789774, daily_phase_cos=-0.6593458151000688, session_position=0.7046979865771812, time_to_close=44.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23381, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 27, 'timestamp': '08:45:00', 'price_level': 23381.0, 'open': 23381.0, 'high': 23381.0, 'low': 23381.0, 'close': 23381.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=131.0, daily_phase_sin=0.6723668074346684, daily_phase_cos=-0.7402181274868318, session_position=0.8791946308724832, time_to_close=18.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2334375, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 28, 'timestamp': '09:11:00', 'price_level': 23343.75, 'open': 23343.75, 'high': 23343.75, 'low': 23343.75, 'close': 23343.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=138.0, daily_phase_sin=0.6494480483301838, daily_phase_cos=-0.7604059656000309, session_position=0.9261744966442953, time_to_close=11.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23369, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 29, 'timestamp': '09:18:00', 'price_level': 23369.0, 'open': 23369.0, 'high': 23369.0, 'low': 23369.0, 'close': 23369.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=141.0, daily_phase_sin=0.6394390019805848, daily_phase_cos=-0.7688418320734596, session_position=0.9463087248322147, time_to_close=8.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23341, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 30, 'timestamp': '09:21:00', 'price_level': 23341.0, 'open': 23341.0, 'high': 23341.0, 'low': 23341.0, 'close': 23341.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=144.0, daily_phase_sin=0.6293203910498377, daily_phase_cos=-0.7771459614569707, session_position=0.9664429530201343, time_to_close=5.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233425, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 31, 'timestamp': '09:24:00', 'price_level': 23342.5, 'open': 23342.5, 'high': 23342.5, 'low': 23342.5, 'close': 23342.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=145.0, daily_phase_sin=0.6259234721840592, daily_phase_cos=-0.7798844830928817, session_position=0.9731543624161074, time_to_close=4.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23352, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 32, 'timestamp': '09:25:00', 'price_level': 23352.0, 'open': 23352.0, 'high': 23352.0, 'low': 23352.0, 'close': 23352.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=148.0, daily_phase_sin=0.6156614753256584, daily_phase_cos=-0.7880107536067219, session_position=0.9932885906040269, time_to_close=1.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2333925, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 33, 'timestamp': '09:28:00', 'price_level': 23339.25, 'open': 23339.25, 'high': 23339.25, 'low': 23339.25, 'close': 23339.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2334775, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 34, 'timestamp': '09:29:00', 'price_level': 23347.75, 'open': 23347.75, 'high': 23347.75, 'low': 23347.75, 'close': 23347.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.233935, price_delta_1m=0.00037417547760827034, price_delta_5m=0.00037417547760827034, price_delta_15m=0.00037417547760827034, volatility_window=0.000651890482398957, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.000651890482398957, structural_importance=0.25, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23384.75, 'high': 23393.5, 'low': 23378.25, 'close': 23393.5, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23378.25}, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 15.25}})",
    "RichNodeFeature(time_minutes=9.0, daily_phase_sin=0.9550199444571866, daily_phase_cos=-0.29654157497557104, session_position=0.06040268456375839, time_to_close=140.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2336, price_delta_1m=-0.0014533641104556723, price_delta_5m=-0.0014533641104556723, price_delta_15m=-0.0014533641104556723, volatility_window=0.0014554794520547946, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0009631849315068493, structural_importance=0.25, raw_json={'id': 1, 'timestamp': '07:09:00', 'open': 23394.0, 'high': 23394.0, 'low': 23360.0, 'close': 23360.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23382.5}, 'liquidity_sweep': False, 'fpfvg': None, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 34.0}})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23372, price_delta_1m=0.00042804554404588647, price_delta_5m=0.00042804554404588647, price_delta_15m=0.00042804554404588647, volatility_window=0.0009199041588225227, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23383.5, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0004920417593701866, structural_importance=0.5, raw_json={'id': 2, 'timestamp': '07:32:00', 'open': 23362.0, 'high': 23383.5, 'low': 23362.0, 'close': 23372.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23383.5}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23383.5}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 21.5}})",
    "RichNodeFeature(time_minutes=42.0, daily_phase_sin=0.9025852843498605, daily_phase_cos=-0.43051109680829536, session_position=0.28187919463087246, time_to_close=107.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23376, price_delta_1m=0.0001176558672841817, price_delta_5m=0.0001176558672841817, price_delta_15m=0.0001176558672841817, volatility_window=0.0009839151266255988, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23396.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0008662731006160165, structural_importance=0.5, raw_json={'id': 3, 'timestamp': '07:42:00', 'open': 23373.25, 'high': 23396.25, 'low': 23373.25, 'close': 23376.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23396.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23396.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 23.0}})",
    "RichNodeFeature(time_minutes=70.0, daily_phase_sin=0.8433914458128858, daily_phase_cos=-0.5372996083468236, session_position=0.4697986577181208, time_to_close=79.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2337875, price_delta_1m=1.0693585987124922e-05, price_delta_5m=1.0693585987124922e-05, price_delta_15m=1.0693585987124922e-05, volatility_window=0.0026199005507137893, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23404.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0010907341068277815, structural_importance=0.5, raw_json={'id': 4, 'timestamp': '08:10:00', 'open': 23378.5, 'high': 23404.25, 'low': 23343.0, 'close': 23378.75, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23404.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23404.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 61.25}})",
    "RichNodeFeature(time_minutes=99.0, daily_phase_sin=0.7688418320734597, daily_phase_cos=-0.6394390019805846, session_position=0.6644295302013423, time_to_close=50.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23369, price_delta_1m=0.0007922742553692641, price_delta_5m=0.0007922742553692641, price_delta_15m=0.0007922742553692641, volatility_window=0.0015939920407377294, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23343.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0005135007916470538, structural_importance=0.75, raw_json={'id': 5, 'timestamp': '08:39:00', 'open': 23350.5, 'high': 23381.0, 'low': 23343.75, 'close': 23369.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23381.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23343.75}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 37.25}})",
    "RichNodeFeature(time_minutes=141.0, daily_phase_sin=0.6394390019805848, daily_phase_cos=-0.7688418320734596, session_position=0.9463087248322147, time_to_close=8.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2334775, price_delta_1m=0.0002891906944860974, price_delta_5m=0.0002891906944860974, price_delta_15m=0.0002891906944860974, volatility_window=0.0005460911651015623, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23339.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0001820303883671874, structural_importance=0.75, raw_json={'id': 6, 'timestamp': '09:21:00', 'open': 23341.0, 'high': 23352.0, 'low': 23339.25, 'close': 23347.75, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23352.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23339.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 12.75}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23372, price_delta_1m=-0.0005452271245149082, price_delta_5m=-0.0005452271245149082, price_delta_15m=-0.0005452271245149082, volatility_window=0.0014547321581379429, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23360.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.00026741399965771005, structural_importance=4.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23384.75, 'high': 23394.0, 'low': 23360.0, 'close': 23372.0, 'timeframe': '15m', 'source_movements': 15, 'pd_array': {'type': 'swing_low', 'level': 23378.25}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23360.0}, 'meta': {'coverage': 15, 'period_minutes': 15, 'price_range': 34.0}})",
    "RichNodeFeature(time_minutes=42.0, daily_phase_sin=0.9025852843498605, daily_phase_cos=-0.43051109680829536, session_position=0.28187919463087246, time_to_close=107.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.23369, price_delta_1m=-0.00018183179489373536, price_delta_5m=-0.00018183179489373536, price_delta_15m=-0.00018183179489373536, volatility_window=0.002620993624031837, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23396.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0011660747143651846, structural_importance=3.0, raw_json={'id': 1, 'timestamp': '07:42:00', 'open': 23373.25, 'high': 23404.25, 'low': 23343.0, 'close': 23369.0, 'timeframe': '15m', 'source_movements': 15, 'pd_array': {'type': 'swing_high', 'level': 23396.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23396.25}, 'meta': {'coverage': 15, 'period_minutes': 15, 'price_range': 61.25}})",
    "RichNodeFeature(time_minutes=141.0, daily_phase_sin=0.6394390019805848, daily_phase_cos=-0.7688418320734596, session_position=0.9463087248322147, time_to_close=8.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2334775, price_delta_1m=0.0002891906944860974, price_delta_5m=0.0002891906944860974, price_delta_15m=0.0002891906944860974, volatility_window=0.0005460911651015623, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23339.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0001820303883671874, structural_importance=0.75, raw_json={'id': 2, 'timestamp': '09:21:00', 'open': 23341.0, 'high': 23352.0, 'low': 23339.25, 'close': 23347.75, 'timeframe': '15m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23352.0}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23339.25}, 'meta': {'coverage': 5, 'period_minutes': 15, 'price_range': 12.75}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2334775, price_delta_1m=-0.0015822277338864003, price_delta_5m=-0.0015822277338864003, price_delta_15m=-0.0015822277338864003, volatility_window=0.0027839941750275724, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=23360.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0013063357282821686, structural_importance=7.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23384.75, 'high': 23404.25, 'low': 23339.25, 'close': 23347.75, 'timeframe': '60m', 'source_movements': 35, 'pd_array': {'type': 'swing_low', 'level': 23378.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23360.0}, 'meta': {'coverage': 35, 'period_minutes': 60, 'price_range': 65.0}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754348400, day_of_week=1, month_phase=0.16129032258064516, normalized_price=0.2334775, price_delta_1m=-0.0015822277338864003, price_delta_5m=-0.0015822277338864003, price_delta_15m=-0.0015822277338864003, volatility_window=0.0027839941750275724, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=4, liquidity_type=0, fpfvg_gap_size=23360.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0013063357282821686, structural_importance=7.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23384.75, 'high': 23404.25, 'low': 23339.25, 'close': 23347.75, 'timeframe': '1440m', 'source_movements': 35, 'pd_array': {'type': 'swing_low', 'level': 23378.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23360.0}, 'meta': {'coverage': 35, 'period_minutes': 1440, 'price_range': 65.0}})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 1,
        "feature_idx": 0
      },
      {
        "source": 1,
        "target": 2,
        "feature_idx": 1
      },
      {
        "source": 2,
        "target": 3,
        "feature_idx": 2
      },
      {
        "source": 3,
        "target": 4,
        "feature_idx": 3
      },
      {
        "source": 4,
        "target": 5,
        "feature_idx": 4
      },
      {
        "source": 5,
        "target": 6,
        "feature_idx": 5
      },
      {
        "source": 6,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 8,
        "feature_idx": 7
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 8
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 9
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 10
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 11
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 12
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 13
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 14
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 15
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 16
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 17
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 18
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 19
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 20
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 21
      },
      {
        "source": 22,
        "target": 23,
        "feature_idx": 22
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 23
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 24
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 25
      },
      {
        "source": 26,
        "target": 27,
        "feature_idx": 26
      },
      {
        "source": 27,
        "target": 28,
        "feature_idx": 27
      },
      {
        "source": 28,
        "target": 29,
        "feature_idx": 28
      },
      {
        "source": 29,
        "target": 30,
        "feature_idx": 29
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 30
      },
      {
        "source": 31,
        "target": 32,
        "feature_idx": 31
      },
      {
        "source": 32,
        "target": 33,
        "feature_idx": 32
      },
      {
        "source": 33,
        "target": 34,
        "feature_idx": 33
      },
      {
        "source": 35,
        "target": 36,
        "feature_idx": 34
      },
      {
        "source": 36,
        "target": 37,
        "feature_idx": 35
      },
      {
        "source": 37,
        "target": 38,
        "feature_idx": 36
      },
      {
        "source": 38,
        "target": 39,
        "feature_idx": 37
      },
      {
        "source": 39,
        "target": 40,
        "feature_idx": 38
      },
      {
        "source": 40,
        "target": 41,
        "feature_idx": 39
      },
      {
        "source": 42,
        "target": 43,
        "feature_idx": 40
      },
      {
        "source": 43,
        "target": 44,
        "feature_idx": 41
      }
    ],
    "scale": [
      {
        "source": 0,
        "target": 35,
        "feature_idx": 42
      },
      {
        "source": 0,
        "target": 36,
        "feature_idx": 43
      },
      {
        "source": 1,
        "target": 35,
        "feature_idx": 44
      },
      {
        "source": 1,
        "target": 36,
        "feature_idx": 45
      },
      {
        "source": 2,
        "target": 35,
        "feature_idx": 46
      },
      {
        "source": 2,
        "target": 36,
        "feature_idx": 47
      },
      {
        "source": 2,
        "target": 37,
        "feature_idx": 48
      },
      {
        "source": 3,
        "target": 35,
        "feature_idx": 49
      },
      {
        "source": 3,
        "target": 36,
        "feature_idx": 50
      },
      {
        "source": 3,
        "target": 37,
        "feature_idx": 51
      },
      {
        "source": 4,
        "target": 35,
        "feature_idx": 52
      },
      {
        "source": 4,
        "target": 36,
        "feature_idx": 53
      },
      {
        "source": 4,
        "target": 37,
        "feature_idx": 54
      },
      {
        "source": 5,
        "target": 35,
        "feature_idx": 55
      },
      {
        "source": 5,
        "target": 36,
        "feature_idx": 56
      },
      {
        "source": 5,
        "target": 37,
        "feature_idx": 57
      },
      {
        "source": 6,
        "target": 35,
        "feature_idx": 58
      },
      {
        "source": 6,
        "target": 36,
        "feature_idx": 59
      },
      {
        "source": 6,
        "target": 37,
        "feature_idx": 60
      },
      {
        "source": 7,
        "target": 35,
        "feature_idx": 61
      },
      {
        "source": 7,
        "target": 36,
        "feature_idx": 62
      },
      {
        "source": 7,
        "target": 37,
        "feature_idx": 63
      },
      {
        "source": 7,
        "target": 38,
        "feature_idx": 64
      },
      {
        "source": 8,
        "target": 35,
        "feature_idx": 65
      },
      {
        "source": 8,
        "target": 36,
        "feature_idx": 66
      },
      {
        "source": 8,
        "target": 37,
        "feature_idx": 67
      },
      {
        "source": 8,
        "target": 38,
        "feature_idx": 68
      },
      {
        "source": 9,
        "target": 36,
        "feature_idx": 69
      },
      {
        "source": 9,
        "target": 37,
        "feature_idx": 70
      },
      {
        "source": 9,
        "target": 38,
        "feature_idx": 71
      },
      {
        "source": 10,
        "target": 36,
        "feature_idx": 72
      },
      {
        "source": 10,
        "target": 37,
        "feature_idx": 73
      },
      {
        "source": 10,
        "target": 38,
        "feature_idx": 74
      },
      {
        "source": 11,
        "target": 36,
        "feature_idx": 75
      },
      {
        "source": 11,
        "target": 37,
        "feature_idx": 76
      },
      {
        "source": 11,
        "target": 38,
        "feature_idx": 77
      },
      {
        "source": 12,
        "target": 36,
        "feature_idx": 78
      },
      {
        "source": 12,
        "target": 37,
        "feature_idx": 79
      },
      {
        "source": 12,
        "target": 38,
        "feature_idx": 80
      },
      {
        "source": 13,
        "target": 37,
        "feature_idx": 81
      },
      {
        "source": 13,
        "target": 38,
        "feature_idx": 82
      },
      {
        "source": 14,
        "target": 37,
        "feature_idx": 83
      },
      {
        "source": 14,
        "target": 38,
        "feature_idx": 84
      },
      {
        "source": 14,
        "target": 39,
        "feature_idx": 85
      },
      {
        "source": 15,
        "target": 37,
        "feature_idx": 86
      },
      {
        "source": 15,
        "target": 38,
        "feature_idx": 87
      },
      {
        "source": 15,
        "target": 39,
        "feature_idx": 88
      },
      {
        "source": 16,
        "target": 37,
        "feature_idx": 89
      },
      {
        "source": 16,
        "target": 38,
        "feature_idx": 90
      },
      {
        "source": 16,
        "target": 39,
        "feature_idx": 91
      },
      {
        "source": 17,
        "target": 37,
        "feature_idx": 92
      },
      {
        "source": 17,
        "target": 38,
        "feature_idx": 93
      },
      {
        "source": 17,
        "target": 39,
        "feature_idx": 94
      },
      {
        "source": 18,
        "target": 37,
        "feature_idx": 95
      },
      {
        "source": 18,
        "target": 38,
        "feature_idx": 96
      },
      {
        "source": 18,
        "target": 39,
        "feature_idx": 97
      },
      {
        "source": 19,
        "target": 38,
        "feature_idx": 98
      },
      {
        "source": 19,
        "target": 39,
        "feature_idx": 99
      },
      {
        "source": 20,
        "target": 38,
        "feature_idx": 100
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 101
      },
      {
        "source": 20,
        "target": 40,
        "feature_idx": 102
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 103
      },
      {
        "source": 21,
        "target": 40,
        "feature_idx": 104
      },
      {
        "source": 22,
        "target": 39,
        "feature_idx": 105
      },
      {
        "source": 22,
        "target": 40,
        "feature_idx": 106
      },
      {
        "source": 23,
        "target": 39,
        "feature_idx": 107
      },
      {
        "source": 23,
        "target": 40,
        "feature_idx": 108
      },
      {
        "source": 24,
        "target": 39,
        "feature_idx": 109
      },
      {
        "source": 24,
        "target": 40,
        "feature_idx": 110
      },
      {
        "source": 25,
        "target": 39,
        "feature_idx": 111
      },
      {
        "source": 25,
        "target": 40,
        "feature_idx": 112
      },
      {
        "source": 26,
        "target": 40,
        "feature_idx": 113
      },
      {
        "source": 27,
        "target": 40,
        "feature_idx": 114
      },
      {
        "source": 28,
        "target": 41,
        "feature_idx": 115
      },
      {
        "source": 29,
        "target": 41,
        "feature_idx": 116
      },
      {
        "source": 30,
        "target": 41,
        "feature_idx": 117
      },
      {
        "source": 31,
        "target": 41,
        "feature_idx": 118
      },
      {
        "source": 32,
        "target": 41,
        "feature_idx": 119
      },
      {
        "source": 33,
        "target": 41,
        "feature_idx": 120
      },
      {
        "source": 34,
        "target": 41,
        "feature_idx": 121
      },
      {
        "source": 35,
        "target": 42,
        "feature_idx": 122
      },
      {
        "source": 36,
        "target": 42,
        "feature_idx": 123
      },
      {
        "source": 37,
        "target": 43,
        "feature_idx": 124
      },
      {
        "source": 38,
        "target": 43,
        "feature_idx": 125
      },
      {
        "source": 39,
        "target": 43,
        "feature_idx": 126
      },
      {
        "source": 41,
        "target": 44,
        "feature_idx": 127
      },
      {
        "source": 42,
        "target": 45,
        "feature_idx": 128
      },
      {
        "source": 45,
        "target": 46,
        "feature_idx": 129
      },
      {
        "source": 0,
        "target": 35,
        "feature_idx": 130,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 35,
        "feature_idx": 131,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 35,
        "feature_idx": 132,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 35,
        "feature_idx": 133,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 36,
        "feature_idx": 134,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23382.5
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 36,
        "feature_idx": 135,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23382.5
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 36,
        "feature_idx": 136,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23382.5
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 35,
        "feature_idx": 137,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 35,
        "feature_idx": 138,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 35,
        "feature_idx": 139,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 37,
        "feature_idx": 140,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23383.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23383.5
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 37,
        "feature_idx": 141,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23383.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23383.5
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 35,
        "feature_idx": 142,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 35,
        "feature_idx": 143,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 35,
        "feature_idx": 144,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 38,
        "feature_idx": 145,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23396.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23396.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 38,
        "feature_idx": 146,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23396.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23396.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 35,
        "feature_idx": 147,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 35,
        "feature_idx": 148,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 35,
        "feature_idx": 149,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 150,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23404.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23404.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 151,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23404.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23404.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 35,
        "feature_idx": 152,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 23,
        "target": 35,
        "feature_idx": 153,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 24,
        "target": 35,
        "feature_idx": 154,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 25,
        "target": 40,
        "feature_idx": 155,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23381.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23343.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 26,
        "target": 40,
        "feature_idx": 156,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23381.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23343.75
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 27,
        "target": 35,
        "feature_idx": 157,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 28,
        "target": 35,
        "feature_idx": 158,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 29,
        "target": 35,
        "feature_idx": 159,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 30,
        "target": 41,
        "feature_idx": 160,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 31,
        "target": 41,
        "feature_idx": 161,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 32,
        "target": 41,
        "feature_idx": 162,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 33,
        "target": 35,
        "feature_idx": 163,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 34,
        "target": 35,
        "feature_idx": 164,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": null,
          "liquidity_sweep": false
        }
      },
      {
        "source": 0,
        "target": 42,
        "feature_idx": 165,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 42,
        "feature_idx": 166,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 42,
        "feature_idx": 167,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 42,
        "feature_idx": 168,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 42,
        "feature_idx": 169,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 42,
        "feature_idx": 170,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 42,
        "feature_idx": 171,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 42,
        "feature_idx": 172,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 42,
        "feature_idx": 173,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 42,
        "feature_idx": 174,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 42,
        "feature_idx": 175,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 42,
        "feature_idx": 176,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 42,
        "feature_idx": 177,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 42,
        "feature_idx": 178,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 42,
        "feature_idx": 179,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 43,
        "feature_idx": 180,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23396.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23396.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 43,
        "feature_idx": 181,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23396.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23396.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 43,
        "feature_idx": 182,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23396.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23396.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 43,
        "feature_idx": 183,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23396.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23396.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 42,
        "feature_idx": 184,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 20,
        "target": 42,
        "feature_idx": 185,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 21,
        "target": 42,
        "feature_idx": 186,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 22,
        "target": 42,
        "feature_idx": 187,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 23,
        "target": 42,
        "feature_idx": 188,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 24,
        "target": 42,
        "feature_idx": 189,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 25,
        "target": 42,
        "feature_idx": 190,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 26,
        "target": 42,
        "feature_idx": 191,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 27,
        "target": 42,
        "feature_idx": 192,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 28,
        "target": 42,
        "feature_idx": 193,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 29,
        "target": 42,
        "feature_idx": 194,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 30,
        "target": 44,
        "feature_idx": 195,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 31,
        "target": 44,
        "feature_idx": 196,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 32,
        "target": 44,
        "feature_idx": 197,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 33,
        "target": 44,
        "feature_idx": 198,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 34,
        "target": 44,
        "feature_idx": 199,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23352.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23339.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 45,
        "feature_idx": 200,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 45,
        "feature_idx": 201,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 45,
        "feature_idx": 202,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 45,
        "feature_idx": 203,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 45,
        "feature_idx": 204,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 45,
        "feature_idx": 205,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 45,
        "feature_idx": 206,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 45,
        "feature_idx": 207,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 45,
        "feature_idx": 208,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 45,
        "feature_idx": 209,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 45,
        "feature_idx": 210,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 45,
        "feature_idx": 211,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 45,
        "feature_idx": 212,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 45,
        "feature_idx": 213,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 45,
        "feature_idx": 214,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 45,
        "feature_idx": 215,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 45,
        "feature_idx": 216,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 45,
        "feature_idx": 217,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 45,
        "feature_idx": 218,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 45,
        "feature_idx": 219,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 45,
        "feature_idx": 220,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 45,
        "feature_idx": 221,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 45,
        "feature_idx": 222,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 23,
        "target": 45,
        "feature_idx": 223,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 24,
        "target": 45,
        "feature_idx": 224,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 25,
        "target": 45,
        "feature_idx": 225,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 26,
        "target": 45,
        "feature_idx": 226,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 27,
        "target": 45,
        "feature_idx": 227,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 28,
        "target": 45,
        "feature_idx": 228,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 29,
        "target": 45,
        "feature_idx": 229,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 30,
        "target": 45,
        "feature_idx": 230,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 31,
        "target": 45,
        "feature_idx": 231,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 32,
        "target": 45,
        "feature_idx": 232,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 33,
        "target": 45,
        "feature_idx": 233,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 34,
        "target": 45,
        "feature_idx": 234,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 0,
        "target": 46,
        "feature_idx": 235,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 46,
        "feature_idx": 236,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 46,
        "feature_idx": 237,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 46,
        "feature_idx": 238,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 46,
        "feature_idx": 239,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 46,
        "feature_idx": 240,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 46,
        "feature_idx": 241,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 46,
        "feature_idx": 242,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 46,
        "feature_idx": 243,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 46,
        "feature_idx": 244,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 46,
        "feature_idx": 245,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 46,
        "feature_idx": 246,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 46,
        "feature_idx": 247,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 46,
        "feature_idx": 248,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 46,
        "feature_idx": 249,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 46,
        "feature_idx": 250,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 46,
        "feature_idx": 251,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 46,
        "feature_idx": 252,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 46,
        "feature_idx": 253,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 46,
        "feature_idx": 254,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 46,
        "feature_idx": 255,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 46,
        "feature_idx": 256,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 46,
        "feature_idx": 257,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 23,
        "target": 46,
        "feature_idx": 258,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 24,
        "target": 46,
        "feature_idx": 259,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 25,
        "target": 46,
        "feature_idx": 260,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 26,
        "target": 46,
        "feature_idx": 261,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 27,
        "target": 46,
        "feature_idx": 262,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 28,
        "target": 46,
        "feature_idx": 263,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 29,
        "target": 46,
        "feature_idx": 264,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 30,
        "target": 46,
        "feature_idx": 265,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 31,
        "target": 46,
        "feature_idx": 266,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 32,
        "target": 46,
        "feature_idx": 267,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 33,
        "target": 46,
        "feature_idx": 268,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 34,
        "target": 46,
        "feature_idx": 269,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 35,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23378.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23360.0
          },
          "liquidity_sweep": false
        }
      }
    ],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 35,
        "feature_idx": 270
      },
      {
        "source": 0,
        "target": 36,
        "feature_idx": 271
      },
      {
        "source": 0,
        "target": 37,
        "feature_idx": 272
      },
      {
        "source": 0,
        "target": 38,
        "feature_idx": 273
      },
      {
        "source": 0,
        "target": 39,
        "feature_idx": 274
      },
      {
        "source": 0,
        "target": 40,
        "feature_idx": 275
      },
      {
        "source": 0,
        "target": 41,
        "feature_idx": 276
      },
      {
        "source": 0,
        "target": 42,
        "feature_idx": 277
      },
      {
        "source": 0,
        "target": 43,
        "feature_idx": 278
      },
      {
        "source": 0,
        "target": 44,
        "feature_idx": 279
      },
      {
        "source": 0,
        "target": 45,
        "feature_idx": 280
      },
      {
        "source": 0,
        "target": 46,
        "feature_idx": 281
      },
      {
        "source": 1,
        "target": 35,
        "feature_idx": 282
      },
      {
        "source": 1,
        "target": 36,
        "feature_idx": 283
      },
      {
        "source": 1,
        "target": 37,
        "feature_idx": 284
      },
      {
        "source": 1,
        "target": 38,
        "feature_idx": 285
      },
      {
        "source": 1,
        "target": 39,
        "feature_idx": 286
      },
      {
        "source": 1,
        "target": 40,
        "feature_idx": 287
      },
      {
        "source": 1,
        "target": 41,
        "feature_idx": 288
      },
      {
        "source": 1,
        "target": 42,
        "feature_idx": 289
      },
      {
        "source": 1,
        "target": 43,
        "feature_idx": 290
      },
      {
        "source": 1,
        "target": 44,
        "feature_idx": 291
      },
      {
        "source": 1,
        "target": 45,
        "feature_idx": 292
      },
      {
        "source": 1,
        "target": 46,
        "feature_idx": 293
      },
      {
        "source": 2,
        "target": 35,
        "feature_idx": 294
      },
      {
        "source": 2,
        "target": 36,
        "feature_idx": 295
      },
      {
        "source": 2,
        "target": 37,
        "feature_idx": 296
      },
      {
        "source": 2,
        "target": 38,
        "feature_idx": 297
      },
      {
        "source": 2,
        "target": 39,
        "feature_idx": 298
      },
      {
        "source": 2,
        "target": 40,
        "feature_idx": 299
      },
      {
        "source": 2,
        "target": 41,
        "feature_idx": 300
      },
      {
        "source": 2,
        "target": 42,
        "feature_idx": 301
      },
      {
        "source": 2,
        "target": 43,
        "feature_idx": 302
      },
      {
        "source": 2,
        "target": 44,
        "feature_idx": 303
      },
      {
        "source": 2,
        "target": 45,
        "feature_idx": 304
      },
      {
        "source": 2,
        "target": 46,
        "feature_idx": 305
      },
      {
        "source": 3,
        "target": 35,
        "feature_idx": 306
      },
      {
        "source": 3,
        "target": 36,
        "feature_idx": 307
      },
      {
        "source": 3,
        "target": 37,
        "feature_idx": 308
      },
      {
        "source": 3,
        "target": 38,
        "feature_idx": 309
      },
      {
        "source": 3,
        "target": 39,
        "feature_idx": 310
      },
      {
        "source": 3,
        "target": 40,
        "feature_idx": 311
      },
      {
        "source": 3,
        "target": 41,
        "feature_idx": 312
      },
      {
        "source": 3,
        "target": 42,
        "feature_idx": 313
      },
      {
        "source": 3,
        "target": 43,
        "feature_idx": 314
      },
      {
        "source": 3,
        "target": 44,
        "feature_idx": 315
      },
      {
        "source": 3,
        "target": 45,
        "feature_idx": 316
      },
      {
        "source": 3,
        "target": 46,
        "feature_idx": 317
      },
      {
        "source": 4,
        "target": 35,
        "feature_idx": 318
      },
      {
        "source": 4,
        "target": 36,
        "feature_idx": 319
      },
      {
        "source": 4,
        "target": 37,
        "feature_idx": 320
      },
      {
        "source": 4,
        "target": 38,
        "feature_idx": 321
      },
      {
        "source": 4,
        "target": 39,
        "feature_idx": 322
      },
      {
        "source": 4,
        "target": 40,
        "feature_idx": 323
      },
      {
        "source": 4,
        "target": 41,
        "feature_idx": 324
      },
      {
        "source": 4,
        "target": 42,
        "feature_idx": 325
      },
      {
        "source": 4,
        "target": 43,
        "feature_idx": 326
      },
      {
        "source": 4,
        "target": 44,
        "feature_idx": 327
      },
      {
        "source": 4,
        "target": 45,
        "feature_idx": 328
      },
      {
        "source": 4,
        "target": 46,
        "feature_idx": 329
      },
      {
        "source": 5,
        "target": 35,
        "feature_idx": 330
      },
      {
        "source": 5,
        "target": 36,
        "feature_idx": 331
      },
      {
        "source": 5,
        "target": 37,
        "feature_idx": 332
      },
      {
        "source": 5,
        "target": 38,
        "feature_idx": 333
      },
      {
        "source": 5,
        "target": 39,
        "feature_idx": 334
      },
      {
        "source": 5,
        "target": 40,
        "feature_idx": 335
      },
      {
        "source": 5,
        "target": 41,
        "feature_idx": 336
      },
      {
        "source": 5,
        "target": 42,
        "feature_idx": 337
      },
      {
        "source": 5,
        "target": 43,
        "feature_idx": 338
      },
      {
        "source": 5,
        "target": 44,
        "feature_idx": 339
      },
      {
        "source": 5,
        "target": 45,
        "feature_idx": 340
      },
      {
        "source": 5,
        "target": 46,
        "feature_idx": 341
      },
      {
        "source": 6,
        "target": 35,
        "feature_idx": 342
      },
      {
        "source": 6,
        "target": 36,
        "feature_idx": 343
      },
      {
        "source": 6,
        "target": 37,
        "feature_idx": 344
      },
      {
        "source": 6,
        "target": 38,
        "feature_idx": 345
      },
      {
        "source": 6,
        "target": 39,
        "feature_idx": 346
      },
      {
        "source": 6,
        "target": 40,
        "feature_idx": 347
      },
      {
        "source": 6,
        "target": 41,
        "feature_idx": 348
      },
      {
        "source": 6,
        "target": 42,
        "feature_idx": 349
      },
      {
        "source": 6,
        "target": 43,
        "feature_idx": 350
      },
      {
        "source": 6,
        "target": 44,
        "feature_idx": 351
      },
      {
        "source": 6,
        "target": 45,
        "feature_idx": 352
      },
      {
        "source": 6,
        "target": 46,
        "feature_idx": 353
      },
      {
        "source": 7,
        "target": 35,
        "feature_idx": 354
      },
      {
        "source": 7,
        "target": 36,
        "feature_idx": 355
      },
      {
        "source": 7,
        "target": 37,
        "feature_idx": 356
      },
      {
        "source": 7,
        "target": 38,
        "feature_idx": 357
      },
      {
        "source": 7,
        "target": 39,
        "feature_idx": 358
      },
      {
        "source": 7,
        "target": 40,
        "feature_idx": 359
      },
      {
        "source": 7,
        "target": 41,
        "feature_idx": 360
      },
      {
        "source": 7,
        "target": 42,
        "feature_idx": 361
      },
      {
        "source": 7,
        "target": 43,
        "feature_idx": 362
      },
      {
        "source": 7,
        "target": 44,
        "feature_idx": 363
      },
      {
        "source": 7,
        "target": 45,
        "feature_idx": 364
      },
      {
        "source": 7,
        "target": 46,
        "feature_idx": 365
      },
      {
        "source": 8,
        "target": 35,
        "feature_idx": 366
      },
      {
        "source": 8,
        "target": 36,
        "feature_idx": 367
      },
      {
        "source": 8,
        "target": 37,
        "feature_idx": 368
      },
      {
        "source": 8,
        "target": 38,
        "feature_idx": 369
      },
      {
        "source": 8,
        "target": 39,
        "feature_idx": 370
      },
      {
        "source": 8,
        "target": 40,
        "feature_idx": 371
      },
      {
        "source": 8,
        "target": 41,
        "feature_idx": 372
      },
      {
        "source": 8,
        "target": 42,
        "feature_idx": 373
      },
      {
        "source": 8,
        "target": 43,
        "feature_idx": 374
      },
      {
        "source": 8,
        "target": 44,
        "feature_idx": 375
      },
      {
        "source": 8,
        "target": 45,
        "feature_idx": 376
      },
      {
        "source": 8,
        "target": 46,
        "feature_idx": 377
      },
      {
        "source": 9,
        "target": 35,
        "feature_idx": 378
      },
      {
        "source": 9,
        "target": 36,
        "feature_idx": 379
      },
      {
        "source": 9,
        "target": 37,
        "feature_idx": 380
      },
      {
        "source": 9,
        "target": 38,
        "feature_idx": 381
      },
      {
        "source": 9,
        "target": 39,
        "feature_idx": 382
      },
      {
        "source": 9,
        "target": 40,
        "feature_idx": 383
      },
      {
        "source": 9,
        "target": 41,
        "feature_idx": 384
      },
      {
        "source": 9,
        "target": 42,
        "feature_idx": 385
      },
      {
        "source": 9,
        "target": 43,
        "feature_idx": 386
      },
      {
        "source": 9,
        "target": 44,
        "feature_idx": 387
      },
      {
        "source": 9,
        "target": 45,
        "feature_idx": 388
      },
      {
        "source": 9,
        "target": 46,
        "feature_idx": 389
      },
      {
        "source": 10,
        "target": 35,
        "feature_idx": 390
      },
      {
        "source": 10,
        "target": 36,
        "feature_idx": 391
      },
      {
        "source": 10,
        "target": 37,
        "feature_idx": 392
      },
      {
        "source": 10,
        "target": 38,
        "feature_idx": 393
      },
      {
        "source": 10,
        "target": 39,
        "feature_idx": 394
      },
      {
        "source": 10,
        "target": 40,
        "feature_idx": 395
      },
      {
        "source": 10,
        "target": 41,
        "feature_idx": 396
      },
      {
        "source": 10,
        "target": 42,
        "feature_idx": 397
      },
      {
        "source": 10,
        "target": 43,
        "feature_idx": 398
      },
      {
        "source": 10,
        "target": 44,
        "feature_idx": 399
      },
      {
        "source": 10,
        "target": 45,
        "feature_idx": 400
      },
      {
        "source": 10,
        "target": 46,
        "feature_idx": 401
      },
      {
        "source": 11,
        "target": 35,
        "feature_idx": 402
      },
      {
        "source": 11,
        "target": 36,
        "feature_idx": 403
      },
      {
        "source": 11,
        "target": 37,
        "feature_idx": 404
      },
      {
        "source": 11,
        "target": 38,
        "feature_idx": 405
      },
      {
        "source": 11,
        "target": 39,
        "feature_idx": 406
      },
      {
        "source": 11,
        "target": 40,
        "feature_idx": 407
      },
      {
        "source": 11,
        "target": 41,
        "feature_idx": 408
      },
      {
        "source": 11,
        "target": 42,
        "feature_idx": 409
      },
      {
        "source": 11,
        "target": 43,
        "feature_idx": 410
      },
      {
        "source": 11,
        "target": 44,
        "feature_idx": 411
      },
      {
        "source": 11,
        "target": 45,
        "feature_idx": 412
      },
      {
        "source": 11,
        "target": 46,
        "feature_idx": 413
      },
      {
        "source": 12,
        "target": 35,
        "feature_idx": 414
      },
      {
        "source": 12,
        "target": 36,
        "feature_idx": 415
      },
      {
        "source": 12,
        "target": 37,
        "feature_idx": 416
      },
      {
        "source": 12,
        "target": 38,
        "feature_idx": 417
      },
      {
        "source": 12,
        "target": 39,
        "feature_idx": 418
      },
      {
        "source": 12,
        "target": 40,
        "feature_idx": 419
      },
      {
        "source": 12,
        "target": 41,
        "feature_idx": 420
      },
      {
        "source": 12,
        "target": 42,
        "feature_idx": 421
      },
      {
        "source": 12,
        "target": 43,
        "feature_idx": 422
      },
      {
        "source": 12,
        "target": 44,
        "feature_idx": 423
      },
      {
        "source": 12,
        "target": 45,
        "feature_idx": 424
      },
      {
        "source": 12,
        "target": 46,
        "feature_idx": 425
      },
      {
        "source": 13,
        "target": 35,
        "feature_idx": 426
      },
      {
        "source": 13,
        "target": 36,
        "feature_idx": 427
      },
      {
        "source": 13,
        "target": 37,
        "feature_idx": 428
      },
      {
        "source": 13,
        "target": 38,
        "feature_idx": 429
      },
      {
        "source": 13,
        "target": 39,
        "feature_idx": 430
      },
      {
        "source": 13,
        "target": 40,
        "feature_idx": 431
      },
      {
        "source": 13,
        "target": 41,
        "feature_idx": 432
      },
      {
        "source": 13,
        "target": 42,
        "feature_idx": 433
      },
      {
        "source": 13,
        "target": 43,
        "feature_idx": 434
      },
      {
        "source": 13,
        "target": 44,
        "feature_idx": 435
      },
      {
        "source": 13,
        "target": 45,
        "feature_idx": 436
      },
      {
        "source": 13,
        "target": 46,
        "feature_idx": 437
      },
      {
        "source": 14,
        "target": 35,
        "feature_idx": 438
      },
      {
        "source": 14,
        "target": 36,
        "feature_idx": 439
      },
      {
        "source": 14,
        "target": 37,
        "feature_idx": 440
      },
      {
        "source": 14,
        "target": 38,
        "feature_idx": 441
      },
      {
        "source": 14,
        "target": 39,
        "feature_idx": 442
      },
      {
        "source": 14,
        "target": 40,
        "feature_idx": 443
      },
      {
        "source": 14,
        "target": 41,
        "feature_idx": 444
      },
      {
        "source": 14,
        "target": 42,
        "feature_idx": 445
      },
      {
        "source": 14,
        "target": 43,
        "feature_idx": 446
      },
      {
        "source": 14,
        "target": 44,
        "feature_idx": 447
      },
      {
        "source": 14,
        "target": 45,
        "feature_idx": 448
      },
      {
        "source": 14,
        "target": 46,
        "feature_idx": 449
      },
      {
        "source": 15,
        "target": 35,
        "feature_idx": 450
      },
      {
        "source": 15,
        "target": 36,
        "feature_idx": 451
      },
      {
        "source": 15,
        "target": 37,
        "feature_idx": 452
      },
      {
        "source": 15,
        "target": 38,
        "feature_idx": 453
      },
      {
        "source": 15,
        "target": 39,
        "feature_idx": 454
      },
      {
        "source": 15,
        "target": 40,
        "feature_idx": 455
      },
      {
        "source": 15,
        "target": 41,
        "feature_idx": 456
      },
      {
        "source": 15,
        "target": 42,
        "feature_idx": 457
      },
      {
        "source": 15,
        "target": 43,
        "feature_idx": 458
      },
      {
        "source": 15,
        "target": 44,
        "feature_idx": 459
      },
      {
        "source": 15,
        "target": 45,
        "feature_idx": 460
      },
      {
        "source": 15,
        "target": 46,
        "feature_idx": 461
      },
      {
        "source": 16,
        "target": 35,
        "feature_idx": 462
      },
      {
        "source": 16,
        "target": 36,
        "feature_idx": 463
      },
      {
        "source": 16,
        "target": 37,
        "feature_idx": 464
      },
      {
        "source": 16,
        "target": 38,
        "feature_idx": 465
      },
      {
        "source": 16,
        "target": 39,
        "feature_idx": 466
      },
      {
        "source": 16,
        "target": 40,
        "feature_idx": 467
      },
      {
        "source": 16,
        "target": 41,
        "feature_idx": 468
      },
      {
        "source": 16,
        "target": 42,
        "feature_idx": 469
      },
      {
        "source": 16,
        "target": 43,
        "feature_idx": 470
      },
      {
        "source": 16,
        "target": 44,
        "feature_idx": 471
      },
      {
        "source": 16,
        "target": 45,
        "feature_idx": 472
      },
      {
        "source": 16,
        "target": 46,
        "feature_idx": 473
      },
      {
        "source": 17,
        "target": 35,
        "feature_idx": 474
      },
      {
        "source": 17,
        "target": 36,
        "feature_idx": 475
      },
      {
        "source": 17,
        "target": 37,
        "feature_idx": 476
      },
      {
        "source": 17,
        "target": 38,
        "feature_idx": 477
      },
      {
        "source": 17,
        "target": 39,
        "feature_idx": 478
      },
      {
        "source": 17,
        "target": 40,
        "feature_idx": 479
      },
      {
        "source": 17,
        "target": 41,
        "feature_idx": 480
      },
      {
        "source": 17,
        "target": 42,
        "feature_idx": 481
      },
      {
        "source": 17,
        "target": 43,
        "feature_idx": 482
      },
      {
        "source": 17,
        "target": 44,
        "feature_idx": 483
      },
      {
        "source": 17,
        "target": 45,
        "feature_idx": 484
      },
      {
        "source": 17,
        "target": 46,
        "feature_idx": 485
      },
      {
        "source": 18,
        "target": 35,
        "feature_idx": 486
      },
      {
        "source": 18,
        "target": 36,
        "feature_idx": 487
      },
      {
        "source": 18,
        "target": 37,
        "feature_idx": 488
      },
      {
        "source": 18,
        "target": 38,
        "feature_idx": 489
      },
      {
        "source": 18,
        "target": 39,
        "feature_idx": 490
      },
      {
        "source": 18,
        "target": 40,
        "feature_idx": 491
      },
      {
        "source": 18,
        "target": 41,
        "feature_idx": 492
      },
      {
        "source": 18,
        "target": 42,
        "feature_idx": 493
      },
      {
        "source": 18,
        "target": 43,
        "feature_idx": 494
      },
      {
        "source": 18,
        "target": 44,
        "feature_idx": 495
      },
      {
        "source": 18,
        "target": 45,
        "feature_idx": 496
      },
      {
        "source": 18,
        "target": 46,
        "feature_idx": 497
      },
      {
        "source": 19,
        "target": 35,
        "feature_idx": 498
      },
      {
        "source": 19,
        "target": 36,
        "feature_idx": 499
      },
      {
        "source": 19,
        "target": 37,
        "feature_idx": 500
      },
      {
        "source": 19,
        "target": 38,
        "feature_idx": 501
      },
      {
        "source": 19,
        "target": 39,
        "feature_idx": 502
      },
      {
        "source": 19,
        "target": 40,
        "feature_idx": 503
      },
      {
        "source": 19,
        "target": 41,
        "feature_idx": 504
      },
      {
        "source": 19,
        "target": 42,
        "feature_idx": 505
      },
      {
        "source": 19,
        "target": 43,
        "feature_idx": 506
      },
      {
        "source": 19,
        "target": 44,
        "feature_idx": 507
      },
      {
        "source": 19,
        "target": 45,
        "feature_idx": 508
      },
      {
        "source": 19,
        "target": 46,
        "feature_idx": 509
      },
      {
        "source": 20,
        "target": 35,
        "feature_idx": 510
      },
      {
        "source": 20,
        "target": 36,
        "feature_idx": 511
      },
      {
        "source": 20,
        "target": 37,
        "feature_idx": 512
      },
      {
        "source": 20,
        "target": 38,
        "feature_idx": 513
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 514
      },
      {
        "source": 20,
        "target": 40,
        "feature_idx": 515
      },
      {
        "source": 20,
        "target": 41,
        "feature_idx": 516
      },
      {
        "source": 20,
        "target": 42,
        "feature_idx": 517
      },
      {
        "source": 20,
        "target": 43,
        "feature_idx": 518
      },
      {
        "source": 20,
        "target": 44,
        "feature_idx": 519
      },
      {
        "source": 20,
        "target": 45,
        "feature_idx": 520
      },
      {
        "source": 20,
        "target": 46,
        "feature_idx": 521
      },
      {
        "source": 21,
        "target": 35,
        "feature_idx": 522
      },
      {
        "source": 21,
        "target": 36,
        "feature_idx": 523
      },
      {
        "source": 21,
        "target": 37,
        "feature_idx": 524
      },
      {
        "source": 21,
        "target": 38,
        "feature_idx": 525
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 526
      },
      {
        "source": 21,
        "target": 40,
        "feature_idx": 527
      },
      {
        "source": 21,
        "target": 41,
        "feature_idx": 528
      },
      {
        "source": 21,
        "target": 42,
        "feature_idx": 529
      },
      {
        "source": 21,
        "target": 43,
        "feature_idx": 530
      },
      {
        "source": 21,
        "target": 44,
        "feature_idx": 531
      },
      {
        "source": 21,
        "target": 45,
        "feature_idx": 532
      },
      {
        "source": 21,
        "target": 46,
        "feature_idx": 533
      },
      {
        "source": 22,
        "target": 35,
        "feature_idx": 534
      },
      {
        "source": 22,
        "target": 36,
        "feature_idx": 535
      },
      {
        "source": 22,
        "target": 37,
        "feature_idx": 536
      },
      {
        "source": 22,
        "target": 38,
        "feature_idx": 537
      },
      {
        "source": 22,
        "target": 39,
        "feature_idx": 538
      },
      {
        "source": 22,
        "target": 40,
        "feature_idx": 539
      },
      {
        "source": 22,
        "target": 41,
        "feature_idx": 540
      },
      {
        "source": 22,
        "target": 42,
        "feature_idx": 541
      },
      {
        "source": 22,
        "target": 43,
        "feature_idx": 542
      },
      {
        "source": 22,
        "target": 44,
        "feature_idx": 543
      },
      {
        "source": 22,
        "target": 45,
        "feature_idx": 544
      },
      {
        "source": 22,
        "target": 46,
        "feature_idx": 545
      },
      {
        "source": 23,
        "target": 35,
        "feature_idx": 546
      },
      {
        "source": 23,
        "target": 36,
        "feature_idx": 547
      },
      {
        "source": 23,
        "target": 37,
        "feature_idx": 548
      },
      {
        "source": 23,
        "target": 38,
        "feature_idx": 549
      },
      {
        "source": 23,
        "target": 39,
        "feature_idx": 550
      },
      {
        "source": 23,
        "target": 40,
        "feature_idx": 551
      },
      {
        "source": 23,
        "target": 41,
        "feature_idx": 552
      },
      {
        "source": 23,
        "target": 42,
        "feature_idx": 553
      },
      {
        "source": 23,
        "target": 43,
        "feature_idx": 554
      },
      {
        "source": 23,
        "target": 44,
        "feature_idx": 555
      },
      {
        "source": 23,
        "target": 45,
        "feature_idx": 556
      },
      {
        "source": 23,
        "target": 46,
        "feature_idx": 557
      },
      {
        "source": 24,
        "target": 35,
        "feature_idx": 558
      },
      {
        "source": 24,
        "target": 36,
        "feature_idx": 559
      },
      {
        "source": 24,
        "target": 37,
        "feature_idx": 560
      },
      {
        "source": 24,
        "target": 38,
        "feature_idx": 561
      },
      {
        "source": 24,
        "target": 39,
        "feature_idx": 562
      },
      {
        "source": 24,
        "target": 40,
        "feature_idx": 563
      },
      {
        "source": 24,
        "target": 41,
        "feature_idx": 564
      },
      {
        "source": 24,
        "target": 42,
        "feature_idx": 565
      },
      {
        "source": 24,
        "target": 43,
        "feature_idx": 566
      },
      {
        "source": 24,
        "target": 44,
        "feature_idx": 567
      },
      {
        "source": 24,
        "target": 45,
        "feature_idx": 568
      },
      {
        "source": 24,
        "target": 46,
        "feature_idx": 569
      },
      {
        "source": 25,
        "target": 35,
        "feature_idx": 570
      },
      {
        "source": 25,
        "target": 36,
        "feature_idx": 571
      },
      {
        "source": 25,
        "target": 37,
        "feature_idx": 572
      },
      {
        "source": 25,
        "target": 38,
        "feature_idx": 573
      },
      {
        "source": 25,
        "target": 39,
        "feature_idx": 574
      },
      {
        "source": 25,
        "target": 40,
        "feature_idx": 575
      },
      {
        "source": 25,
        "target": 41,
        "feature_idx": 576
      },
      {
        "source": 25,
        "target": 42,
        "feature_idx": 577
      },
      {
        "source": 25,
        "target": 43,
        "feature_idx": 578
      },
      {
        "source": 25,
        "target": 44,
        "feature_idx": 579
      },
      {
        "source": 25,
        "target": 45,
        "feature_idx": 580
      },
      {
        "source": 25,
        "target": 46,
        "feature_idx": 581
      },
      {
        "source": 26,
        "target": 35,
        "feature_idx": 582
      },
      {
        "source": 26,
        "target": 36,
        "feature_idx": 583
      },
      {
        "source": 26,
        "target": 37,
        "feature_idx": 584
      },
      {
        "source": 26,
        "target": 38,
        "feature_idx": 585
      },
      {
        "source": 26,
        "target": 39,
        "feature_idx": 586
      },
      {
        "source": 26,
        "target": 40,
        "feature_idx": 587
      },
      {
        "source": 26,
        "target": 41,
        "feature_idx": 588
      },
      {
        "source": 26,
        "target": 42,
        "feature_idx": 589
      },
      {
        "source": 26,
        "target": 43,
        "feature_idx": 590
      },
      {
        "source": 26,
        "target": 44,
        "feature_idx": 591
      },
      {
        "source": 26,
        "target": 45,
        "feature_idx": 592
      },
      {
        "source": 26,
        "target": 46,
        "feature_idx": 593
      },
      {
        "source": 27,
        "target": 35,
        "feature_idx": 594
      },
      {
        "source": 27,
        "target": 36,
        "feature_idx": 595
      },
      {
        "source": 27,
        "target": 37,
        "feature_idx": 596
      },
      {
        "source": 27,
        "target": 38,
        "feature_idx": 597
      },
      {
        "source": 27,
        "target": 39,
        "feature_idx": 598
      },
      {
        "source": 27,
        "target": 40,
        "feature_idx": 599
      },
      {
        "source": 27,
        "target": 41,
        "feature_idx": 600
      },
      {
        "source": 27,
        "target": 42,
        "feature_idx": 601
      },
      {
        "source": 27,
        "target": 43,
        "feature_idx": 602
      },
      {
        "source": 27,
        "target": 44,
        "feature_idx": 603
      },
      {
        "source": 27,
        "target": 45,
        "feature_idx": 604
      },
      {
        "source": 27,
        "target": 46,
        "feature_idx": 605
      },
      {
        "source": 28,
        "target": 35,
        "feature_idx": 606
      },
      {
        "source": 28,
        "target": 36,
        "feature_idx": 607
      },
      {
        "source": 28,
        "target": 37,
        "feature_idx": 608
      },
      {
        "source": 28,
        "target": 38,
        "feature_idx": 609
      },
      {
        "source": 28,
        "target": 39,
        "feature_idx": 610
      },
      {
        "source": 28,
        "target": 40,
        "feature_idx": 611
      },
      {
        "source": 28,
        "target": 41,
        "feature_idx": 612
      },
      {
        "source": 28,
        "target": 42,
        "feature_idx": 613
      },
      {
        "source": 28,
        "target": 43,
        "feature_idx": 614
      },
      {
        "source": 28,
        "target": 44,
        "feature_idx": 615
      },
      {
        "source": 28,
        "target": 45,
        "feature_idx": 616
      },
      {
        "source": 28,
        "target": 46,
        "feature_idx": 617
      },
      {
        "source": 29,
        "target": 35,
        "feature_idx": 618
      },
      {
        "source": 29,
        "target": 36,
        "feature_idx": 619
      },
      {
        "source": 29,
        "target": 37,
        "feature_idx": 620
      },
      {
        "source": 29,
        "target": 38,
        "feature_idx": 621
      },
      {
        "source": 29,
        "target": 39,
        "feature_idx": 622
      },
      {
        "source": 29,
        "target": 40,
        "feature_idx": 623
      },
      {
        "source": 29,
        "target": 41,
        "feature_idx": 624
      },
      {
        "source": 29,
        "target": 42,
        "feature_idx": 625
      },
      {
        "source": 29,
        "target": 43,
        "feature_idx": 626
      },
      {
        "source": 29,
        "target": 44,
        "feature_idx": 627
      },
      {
        "source": 29,
        "target": 45,
        "feature_idx": 628
      },
      {
        "source": 29,
        "target": 46,
        "feature_idx": 629
      },
      {
        "source": 30,
        "target": 35,
        "feature_idx": 630
      },
      {
        "source": 30,
        "target": 36,
        "feature_idx": 631
      },
      {
        "source": 30,
        "target": 37,
        "feature_idx": 632
      },
      {
        "source": 30,
        "target": 38,
        "feature_idx": 633
      },
      {
        "source": 30,
        "target": 39,
        "feature_idx": 634
      },
      {
        "source": 30,
        "target": 40,
        "feature_idx": 635
      },
      {
        "source": 30,
        "target": 41,
        "feature_idx": 636
      },
      {
        "source": 30,
        "target": 42,
        "feature_idx": 637
      },
      {
        "source": 30,
        "target": 43,
        "feature_idx": 638
      },
      {
        "source": 30,
        "target": 44,
        "feature_idx": 639
      },
      {
        "source": 30,
        "target": 45,
        "feature_idx": 640
      },
      {
        "source": 30,
        "target": 46,
        "feature_idx": 641
      },
      {
        "source": 31,
        "target": 35,
        "feature_idx": 642
      },
      {
        "source": 31,
        "target": 36,
        "feature_idx": 643
      },
      {
        "source": 31,
        "target": 37,
        "feature_idx": 644
      },
      {
        "source": 31,
        "target": 38,
        "feature_idx": 645
      },
      {
        "source": 31,
        "target": 39,
        "feature_idx": 646
      },
      {
        "source": 31,
        "target": 40,
        "feature_idx": 647
      },
      {
        "source": 31,
        "target": 41,
        "feature_idx": 648
      },
      {
        "source": 31,
        "target": 42,
        "feature_idx": 649
      },
      {
        "source": 31,
        "target": 43,
        "feature_idx": 650
      },
      {
        "source": 31,
        "target": 44,
        "feature_idx": 651
      },
      {
        "source": 31,
        "target": 45,
        "feature_idx": 652
      },
      {
        "source": 31,
        "target": 46,
        "feature_idx": 653
      },
      {
        "source": 32,
        "target": 35,
        "feature_idx": 654
      },
      {
        "source": 32,
        "target": 36,
        "feature_idx": 655
      },
      {
        "source": 32,
        "target": 37,
        "feature_idx": 656
      },
      {
        "source": 32,
        "target": 38,
        "feature_idx": 657
      },
      {
        "source": 32,
        "target": 39,
        "feature_idx": 658
      },
      {
        "source": 32,
        "target": 40,
        "feature_idx": 659
      },
      {
        "source": 32,
        "target": 41,
        "feature_idx": 660
      },
      {
        "source": 32,
        "target": 42,
        "feature_idx": 661
      },
      {
        "source": 32,
        "target": 43,
        "feature_idx": 662
      },
      {
        "source": 32,
        "target": 44,
        "feature_idx": 663
      },
      {
        "source": 32,
        "target": 45,
        "feature_idx": 664
      },
      {
        "source": 32,
        "target": 46,
        "feature_idx": 665
      },
      {
        "source": 33,
        "target": 35,
        "feature_idx": 666
      },
      {
        "source": 33,
        "target": 36,
        "feature_idx": 667
      },
      {
        "source": 33,
        "target": 37,
        "feature_idx": 668
      },
      {
        "source": 33,
        "target": 38,
        "feature_idx": 669
      },
      {
        "source": 33,
        "target": 39,
        "feature_idx": 670
      },
      {
        "source": 33,
        "target": 40,
        "feature_idx": 671
      },
      {
        "source": 33,
        "target": 41,
        "feature_idx": 672
      },
      {
        "source": 33,
        "target": 42,
        "feature_idx": 673
      },
      {
        "source": 33,
        "target": 43,
        "feature_idx": 674
      },
      {
        "source": 33,
        "target": 44,
        "feature_idx": 675
      },
      {
        "source": 33,
        "target": 45,
        "feature_idx": 676
      },
      {
        "source": 33,
        "target": 46,
        "feature_idx": 677
      },
      {
        "source": 34,
        "target": 35,
        "feature_idx": 678
      },
      {
        "source": 34,
        "target": 36,
        "feature_idx": 679
      },
      {
        "source": 34,
        "target": 37,
        "feature_idx": 680
      },
      {
        "source": 34,
        "target": 38,
        "feature_idx": 681
      },
      {
        "source": 34,
        "target": 39,
        "feature_idx": 682
      },
      {
        "source": 34,
        "target": 40,
        "feature_idx": 683
      },
      {
        "source": 34,
        "target": 41,
        "feature_idx": 684
      },
      {
        "source": 34,
        "target": 42,
        "feature_idx": 685
      },
      {
        "source": 34,
        "target": 43,
        "feature_idx": 686
      },
      {
        "source": 34,
        "target": 44,
        "feature_idx": 687
      },
      {
        "source": 34,
        "target": 45,
        "feature_idx": 688
      },
      {
        "source": 34,
        "target": 46,
        "feature_idx": 689
      },
      {
        "source": 35,
        "target": 42,
        "feature_idx": 690
      },
      {
        "source": 35,
        "target": 43,
        "feature_idx": 691
      },
      {
        "source": 35,
        "target": 44,
        "feature_idx": 692
      },
      {
        "source": 35,
        "target": 45,
        "feature_idx": 693
      },
      {
        "source": 35,
        "target": 46,
        "feature_idx": 694
      },
      {
        "source": 36,
        "target": 42,
        "feature_idx": 695
      },
      {
        "source": 36,
        "target": 43,
        "feature_idx": 696
      },
      {
        "source": 36,
        "target": 44,
        "feature_idx": 697
      },
      {
        "source": 36,
        "target": 45,
        "feature_idx": 698
      },
      {
        "source": 36,
        "target": 46,
        "feature_idx": 699
      },
      {
        "source": 37,
        "target": 42,
        "feature_idx": 700
      },
      {
        "source": 37,
        "target": 43,
        "feature_idx": 701
      },
      {
        "source": 37,
        "target": 44,
        "feature_idx": 702
      },
      {
        "source": 37,
        "target": 45,
        "feature_idx": 703
      },
      {
        "source": 37,
        "target": 46,
        "feature_idx": 704
      },
      {
        "source": 38,
        "target": 42,
        "feature_idx": 705
      },
      {
        "source": 38,
        "target": 43,
        "feature_idx": 706
      },
      {
        "source": 38,
        "target": 44,
        "feature_idx": 707
      },
      {
        "source": 38,
        "target": 45,
        "feature_idx": 708
      },
      {
        "source": 38,
        "target": 46,
        "feature_idx": 709
      },
      {
        "source": 39,
        "target": 42,
        "feature_idx": 710
      },
      {
        "source": 39,
        "target": 43,
        "feature_idx": 711
      },
      {
        "source": 39,
        "target": 44,
        "feature_idx": 712
      },
      {
        "source": 39,
        "target": 45,
        "feature_idx": 713
      },
      {
        "source": 39,
        "target": 46,
        "feature_idx": 714
      },
      {
        "source": 40,
        "target": 42,
        "feature_idx": 715
      },
      {
        "source": 40,
        "target": 43,
        "feature_idx": 716
      },
      {
        "source": 40,
        "target": 44,
        "feature_idx": 717
      },
      {
        "source": 40,
        "target": 45,
        "feature_idx": 718
      },
      {
        "source": 40,
        "target": 46,
        "feature_idx": 719
      },
      {
        "source": 41,
        "target": 42,
        "feature_idx": 720
      },
      {
        "source": 41,
        "target": 43,
        "feature_idx": 721
      },
      {
        "source": 41,
        "target": 44,
        "feature_idx": 722
      },
      {
        "source": 41,
        "target": 45,
        "feature_idx": 723
      },
      {
        "source": 41,
        "target": 46,
        "feature_idx": 724
      },
      {
        "source": 42,
        "target": 45,
        "feature_idx": 725
      },
      {
        "source": 42,
        "target": 46,
        "feature_idx": 726
      },
      {
        "source": 43,
        "target": 45,
        "feature_idx": 727
      },
      {
        "source": 43,
        "target": 46,
        "feature_idx": 728
      },
      {
        "source": 44,
        "target": 45,
        "feature_idx": 729
      },
      {
        "source": 44,
        "target": 46,
        "feature_idx": 730
      },
      {
        "source": 45,
        "target": 46,
        "feature_idx": 731
      }
    ],
    "temporal_echo": [
      {
        "source": 0,
        "target": 35,
        "feature_idx": 732
      },
      {
        "source": 0,
        "target": 36,
        "feature_idx": 733
      },
      {
        "source": 0,
        "target": 42,
        "feature_idx": 734
      },
      {
        "source": 0,
        "target": 45,
        "feature_idx": 735
      },
      {
        "source": 0,
        "target": 46,
        "feature_idx": 736
      },
      {
        "source": 1,
        "target": 35,
        "feature_idx": 737
      },
      {
        "source": 1,
        "target": 36,
        "feature_idx": 738
      },
      {
        "source": 1,
        "target": 42,
        "feature_idx": 739
      },
      {
        "source": 1,
        "target": 45,
        "feature_idx": 740
      },
      {
        "source": 1,
        "target": 46,
        "feature_idx": 741
      },
      {
        "source": 2,
        "target": 35,
        "feature_idx": 742
      },
      {
        "source": 2,
        "target": 36,
        "feature_idx": 743
      },
      {
        "source": 2,
        "target": 37,
        "feature_idx": 744
      },
      {
        "source": 2,
        "target": 42,
        "feature_idx": 745
      },
      {
        "source": 2,
        "target": 45,
        "feature_idx": 746
      },
      {
        "source": 2,
        "target": 46,
        "feature_idx": 747
      },
      {
        "source": 3,
        "target": 35,
        "feature_idx": 748
      },
      {
        "source": 3,
        "target": 36,
        "feature_idx": 749
      },
      {
        "source": 3,
        "target": 37,
        "feature_idx": 750
      },
      {
        "source": 3,
        "target": 42,
        "feature_idx": 751
      },
      {
        "source": 3,
        "target": 45,
        "feature_idx": 752
      },
      {
        "source": 3,
        "target": 46,
        "feature_idx": 753
      },
      {
        "source": 4,
        "target": 35,
        "feature_idx": 754
      },
      {
        "source": 4,
        "target": 36,
        "feature_idx": 755
      },
      {
        "source": 4,
        "target": 37,
        "feature_idx": 756
      },
      {
        "source": 4,
        "target": 42,
        "feature_idx": 757
      },
      {
        "source": 4,
        "target": 45,
        "feature_idx": 758
      },
      {
        "source": 4,
        "target": 46,
        "feature_idx": 759
      },
      {
        "source": 5,
        "target": 35,
        "feature_idx": 760
      },
      {
        "source": 5,
        "target": 36,
        "feature_idx": 761
      },
      {
        "source": 5,
        "target": 37,
        "feature_idx": 762
      },
      {
        "source": 5,
        "target": 42,
        "feature_idx": 763
      },
      {
        "source": 5,
        "target": 45,
        "feature_idx": 764
      },
      {
        "source": 5,
        "target": 46,
        "feature_idx": 765
      },
      {
        "source": 6,
        "target": 35,
        "feature_idx": 766
      },
      {
        "source": 6,
        "target": 36,
        "feature_idx": 767
      },
      {
        "source": 6,
        "target": 37,
        "feature_idx": 768
      },
      {
        "source": 6,
        "target": 42,
        "feature_idx": 769
      },
      {
        "source": 6,
        "target": 45,
        "feature_idx": 770
      },
      {
        "source": 6,
        "target": 46,
        "feature_idx": 771
      },
      {
        "source": 7,
        "target": 35,
        "feature_idx": 772
      },
      {
        "source": 7,
        "target": 36,
        "feature_idx": 773
      },
      {
        "source": 7,
        "target": 37,
        "feature_idx": 774
      },
      {
        "source": 7,
        "target": 38,
        "feature_idx": 775
      },
      {
        "source": 7,
        "target": 42,
        "feature_idx": 776
      },
      {
        "source": 7,
        "target": 43,
        "feature_idx": 777
      },
      {
        "source": 7,
        "target": 45,
        "feature_idx": 778
      },
      {
        "source": 7,
        "target": 46,
        "feature_idx": 779
      },
      {
        "source": 8,
        "target": 35,
        "feature_idx": 780
      },
      {
        "source": 8,
        "target": 36,
        "feature_idx": 781
      },
      {
        "source": 8,
        "target": 37,
        "feature_idx": 782
      },
      {
        "source": 8,
        "target": 38,
        "feature_idx": 783
      },
      {
        "source": 8,
        "target": 42,
        "feature_idx": 784
      },
      {
        "source": 8,
        "target": 43,
        "feature_idx": 785
      },
      {
        "source": 8,
        "target": 45,
        "feature_idx": 786
      },
      {
        "source": 8,
        "target": 46,
        "feature_idx": 787
      },
      {
        "source": 9,
        "target": 36,
        "feature_idx": 788
      },
      {
        "source": 9,
        "target": 37,
        "feature_idx": 789
      },
      {
        "source": 9,
        "target": 38,
        "feature_idx": 790
      },
      {
        "source": 9,
        "target": 43,
        "feature_idx": 791
      },
      {
        "source": 10,
        "target": 36,
        "feature_idx": 792
      },
      {
        "source": 10,
        "target": 37,
        "feature_idx": 793
      },
      {
        "source": 10,
        "target": 38,
        "feature_idx": 794
      },
      {
        "source": 10,
        "target": 43,
        "feature_idx": 795
      },
      {
        "source": 11,
        "target": 36,
        "feature_idx": 796
      },
      {
        "source": 11,
        "target": 37,
        "feature_idx": 797
      },
      {
        "source": 11,
        "target": 38,
        "feature_idx": 798
      },
      {
        "source": 11,
        "target": 43,
        "feature_idx": 799
      },
      {
        "source": 12,
        "target": 36,
        "feature_idx": 800
      },
      {
        "source": 12,
        "target": 37,
        "feature_idx": 801
      },
      {
        "source": 12,
        "target": 38,
        "feature_idx": 802
      },
      {
        "source": 12,
        "target": 43,
        "feature_idx": 803
      },
      {
        "source": 13,
        "target": 37,
        "feature_idx": 804
      },
      {
        "source": 13,
        "target": 38,
        "feature_idx": 805
      },
      {
        "source": 13,
        "target": 43,
        "feature_idx": 806
      },
      {
        "source": 14,
        "target": 37,
        "feature_idx": 807
      },
      {
        "source": 14,
        "target": 38,
        "feature_idx": 808
      },
      {
        "source": 14,
        "target": 39,
        "feature_idx": 809
      },
      {
        "source": 14,
        "target": 43,
        "feature_idx": 810
      },
      {
        "source": 15,
        "target": 37,
        "feature_idx": 811
      },
      {
        "source": 15,
        "target": 38,
        "feature_idx": 812
      },
      {
        "source": 15,
        "target": 39,
        "feature_idx": 813
      },
      {
        "source": 15,
        "target": 43,
        "feature_idx": 814
      },
      {
        "source": 16,
        "target": 37,
        "feature_idx": 815
      },
      {
        "source": 16,
        "target": 38,
        "feature_idx": 816
      },
      {
        "source": 16,
        "target": 39,
        "feature_idx": 817
      },
      {
        "source": 16,
        "target": 43,
        "feature_idx": 818
      },
      {
        "source": 17,
        "target": 37,
        "feature_idx": 819
      },
      {
        "source": 17,
        "target": 38,
        "feature_idx": 820
      },
      {
        "source": 17,
        "target": 39,
        "feature_idx": 821
      },
      {
        "source": 17,
        "target": 43,
        "feature_idx": 822
      },
      {
        "source": 18,
        "target": 37,
        "feature_idx": 823
      },
      {
        "source": 18,
        "target": 38,
        "feature_idx": 824
      },
      {
        "source": 18,
        "target": 39,
        "feature_idx": 825
      },
      {
        "source": 18,
        "target": 43,
        "feature_idx": 826
      },
      {
        "source": 19,
        "target": 38,
        "feature_idx": 827
      },
      {
        "source": 19,
        "target": 39,
        "feature_idx": 828
      },
      {
        "source": 19,
        "target": 43,
        "feature_idx": 829
      },
      {
        "source": 20,
        "target": 38,
        "feature_idx": 830
      },
      {
        "source": 20,
        "target": 39,
        "feature_idx": 831
      },
      {
        "source": 20,
        "target": 40,
        "feature_idx": 832
      },
      {
        "source": 20,
        "target": 43,
        "feature_idx": 833
      },
      {
        "source": 21,
        "target": 39,
        "feature_idx": 834
      },
      {
        "source": 21,
        "target": 40,
        "feature_idx": 835
      },
      {
        "source": 22,
        "target": 39,
        "feature_idx": 836
      },
      {
        "source": 22,
        "target": 40,
        "feature_idx": 837
      },
      {
        "source": 23,
        "target": 39,
        "feature_idx": 838
      },
      {
        "source": 23,
        "target": 40,
        "feature_idx": 839
      },
      {
        "source": 24,
        "target": 39,
        "feature_idx": 840
      },
      {
        "source": 24,
        "target": 40,
        "feature_idx": 841
      },
      {
        "source": 25,
        "target": 39,
        "feature_idx": 842
      },
      {
        "source": 25,
        "target": 40,
        "feature_idx": 843
      },
      {
        "source": 26,
        "target": 40,
        "feature_idx": 844
      },
      {
        "source": 27,
        "target": 40,
        "feature_idx": 845
      },
      {
        "source": 28,
        "target": 41,
        "feature_idx": 846
      },
      {
        "source": 28,
        "target": 44,
        "feature_idx": 847
      },
      {
        "source": 29,
        "target": 41,
        "feature_idx": 848
      },
      {
        "source": 29,
        "target": 44,
        "feature_idx": 849
      },
      {
        "source": 30,
        "target": 41,
        "feature_idx": 850
      },
      {
        "source": 30,
        "target": 44,
        "feature_idx": 851
      },
      {
        "source": 31,
        "target": 41,
        "feature_idx": 852
      },
      {
        "source": 31,
        "target": 44,
        "feature_idx": 853
      },
      {
        "source": 32,
        "target": 41,
        "feature_idx": 854
      },
      {
        "source": 32,
        "target": 44,
        "feature_idx": 855
      },
      {
        "source": 33,
        "target": 41,
        "feature_idx": 856
      },
      {
        "source": 33,
        "target": 44,
        "feature_idx": 857
      },
      {
        "source": 34,
        "target": 41,
        "feature_idx": 858
      },
      {
        "source": 34,
        "target": 44,
        "feature_idx": 859
      },
      {
        "source": 35,
        "target": 42,
        "feature_idx": 860
      },
      {
        "source": 35,
        "target": 45,
        "feature_idx": 861
      },
      {
        "source": 35,
        "target": 46,
        "feature_idx": 862
      },
      {
        "source": 36,
        "target": 42,
        "feature_idx": 863
      },
      {
        "source": 36,
        "target": 45,
        "feature_idx": 864
      },
      {
        "source": 36,
        "target": 46,
        "feature_idx": 865
      },
      {
        "source": 37,
        "target": 43,
        "feature_idx": 866
      },
      {
        "source": 38,
        "target": 43,
        "feature_idx": 867
      },
      {
        "source": 39,
        "target": 43,
        "feature_idx": 868
      },
      {
        "source": 41,
        "target": 44,
        "feature_idx": 869
      },
      {
        "source": 42,
        "target": 45,
        "feature_idx": 870
      },
      {
        "source": 42,
        "target": 46,
        "feature_idx": 871
      },
      {
        "source": 45,
        "target": 46,
        "feature_idx": 872
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    