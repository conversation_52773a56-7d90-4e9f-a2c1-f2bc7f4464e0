{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18
    ],
    "5m": [
      19,
      20,
      21,
      22
    ],
    "15m": [
      23,
      24
    ],
    "1h": [
      25
    ],
    "D": [
      26
    ],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2361775, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'price_level': 23617.75, 'open': 23617.75, 'high': 23617.75, 'low': 23617.75, 'close': 23617.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2361375, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '07:00:00', 'price_level': 23613.75, 'open': 23613.75, 'high': 23613.75, 'low': 23613.75, 'close': 23613.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23639, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '07:00:00', 'price_level': 23639.0, 'open': 23639.0, 'high': 23639.0, 'low': 23639.0, 'close': 23639.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=3.0, daily_phase_sin=0.9624552364536473, daily_phase_cos=-0.2714404498650742, session_position=0.020134228187919462, time_to_close=146.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2361175, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '07:03:00', 'price_level': 23611.75, 'open': 23611.75, 'high': 23611.75, 'low': 23611.75, 'close': 23611.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=14.0, daily_phase_sin=0.9483236552061993, daily_phase_cos=-0.317304656405092, session_position=0.09395973154362416, time_to_close=135.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23606, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 4, 'timestamp': '07:14:00', 'price_level': 23606.0, 'open': 23606.0, 'high': 23606.0, 'low': 23606.0, 'close': 23606.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=20.0, daily_phase_sin=0.9396926207859084, daily_phase_cos=-0.3420201433256687, session_position=0.1342281879194631, time_to_close=129.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2362625, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 5, 'timestamp': '07:20:00', 'price_level': 23626.25, 'open': 23626.25, 'high': 23626.25, 'low': 23626.25, 'close': 23626.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=24.0, daily_phase_sin=0.9335804264972017, daily_phase_cos=-0.35836794954530027, session_position=0.1610738255033557, time_to_close=125.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2360775, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 6, 'timestamp': '07:24:00', 'price_level': 23607.75, 'open': 23607.75, 'high': 23607.75, 'low': 23607.75, 'close': 23607.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23608, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 7, 'timestamp': '07:32:00', 'price_level': 23608.0, 'open': 23608.0, 'high': 23608.0, 'low': 23608.0, 'close': 23608.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=40.0, daily_phase_sin=0.90630778703665, daily_phase_cos=-0.42261826174069933, session_position=0.2684563758389262, time_to_close=109.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.235835, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 8, 'timestamp': '07:40:00', 'price_level': 23583.5, 'open': 23583.5, 'high': 23583.5, 'low': 23583.5, 'close': 23583.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=42.0, daily_phase_sin=0.9025852843498605, daily_phase_cos=-0.43051109680829536, session_position=0.28187919463087246, time_to_close=107.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23605, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 9, 'timestamp': '07:42:00', 'price_level': 23605.0, 'open': 23605.0, 'high': 23605.0, 'low': 23605.0, 'close': 23605.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=50.0, daily_phase_sin=0.8870108331782218, daily_phase_cos=-0.4617486132350338, session_position=0.33557046979865773, time_to_close=99.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2358825, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 10, 'timestamp': '07:50:00', 'price_level': 23588.25, 'open': 23588.25, 'high': 23588.25, 'low': 23588.25, 'close': 23588.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=53.0, daily_phase_sin=0.8808907382053855, daily_phase_cos=-0.4733196671848433, session_position=0.35570469798657717, time_to_close=96.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2358, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 11, 'timestamp': '07:53:00', 'price_level': 23580.0, 'open': 23580.0, 'high': 23580.0, 'low': 23580.0, 'close': 23580.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=62.0, daily_phase_sin=0.8616291604415257, daily_phase_cos=-0.5075383629607042, session_position=0.4161073825503356, time_to_close=87.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.235805, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 12, 'timestamp': '08:02:00', 'price_level': 23580.5, 'open': 23580.5, 'high': 23580.5, 'low': 23580.5, 'close': 23580.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=65.0, daily_phase_sin=0.8549118706729468, daily_phase_cos=-0.5187732581605212, session_position=0.436241610738255, time_to_close=84.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2357575, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 13, 'timestamp': '08:05:00', 'price_level': 23575.75, 'open': 23575.75, 'high': 23575.75, 'low': 23575.75, 'close': 23575.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=68.0, daily_phase_sin=0.8480480961564261, daily_phase_cos=-0.5299192642332048, session_position=0.4563758389261745, time_to_close=81.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23595, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 14, 'timestamp': '08:08:00', 'price_level': 23595.0, 'open': 23595.0, 'high': 23595.0, 'low': 23595.0, 'close': 23595.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23583, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 15, 'timestamp': '08:12:00', 'price_level': 23583.0, 'open': 23583.0, 'high': 23583.0, 'low': 23583.0, 'close': 23583.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=76.0, daily_phase_sin=0.8290375725550418, daily_phase_cos=-0.5591929034707467, session_position=0.5100671140939598, time_to_close=73.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2360325, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 16, 'timestamp': '08:16:00', 'price_level': 23603.25, 'open': 23603.25, 'high': 23603.25, 'low': 23603.25, 'close': 23603.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=129.0, daily_phase_sin=0.678800745532942, daily_phase_cos=-0.7343225094356853, session_position=0.8657718120805369, time_to_close=20.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.2361775, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 17, 'timestamp': '09:09:00', 'price_level': 23617.75, 'open': 23617.75, 'high': 23617.75, 'low': 23617.75, 'close': 23617.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.236015, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 18, 'timestamp': '09:29:00', 'price_level': 23601.5, 'open': 23601.5, 'high': 23601.5, 'low': 23601.5, 'close': 23601.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23606, price_delta_1m=-0.0004975071715129511, price_delta_5m=-0.0004975071715129511, price_delta_15m=-0.0004975071715129511, volatility_window=0.0013979496738117428, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0003283063627891214, structural_importance=0.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23617.75, 'high': 23639.0, 'low': 23606.0, 'close': 23606.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23613.75}, 'liquidity_sweep': True, 'fpfvg': None, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 33.0}})",
    "RichNodeFeature(time_minutes=20.0, daily_phase_sin=0.9396926207859084, daily_phase_cos=-0.3420201433256687, session_position=0.1342281879194631, time_to_close=129.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23605, price_delta_1m=-0.0008994233109359293, price_delta_5m=-0.0008994233109359293, price_delta_15m=-0.0008994233109359293, volatility_window=0.0018110569794535056, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23607.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.00011650074136835416, structural_importance=0.5, raw_json={'id': 1, 'timestamp': '07:20:00', 'open': 23626.25, 'high': 23626.25, 'low': 23583.5, 'close': 23605.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23607.75}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23607.75}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 42.75}})",
    "RichNodeFeature(time_minutes=50.0, daily_phase_sin=0.8870108331782218, daily_phase_cos=-0.4617486132350338, session_position=0.33557046979865773, time_to_close=99.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23595, price_delta_1m=0.0002861594225938762, price_delta_5m=0.0002861594225938762, price_delta_15m=0.0002861594225938762, volatility_window=0.0008158508158508159, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23580.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0006357279084551812, structural_importance=0.5, raw_json={'id': 2, 'timestamp': '07:50:00', 'open': 23588.25, 'high': 23595.0, 'low': 23575.75, 'close': 23595.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23580.0}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23580.0}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 19.25}})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.236015, price_delta_1m=0.000784463384641479, price_delta_5m=0.000784463384641479, price_delta_15m=0.000784463384641479, volatility_window=0.0014723640446581785, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.4, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0006885155604516662, structural_importance=0.32000000000000006, raw_json={'id': 3, 'timestamp': '08:12:00', 'open': 23583.0, 'high': 23617.75, 'low': 23583.0, 'close': 23601.5, 'timeframe': '5m', 'source_movements': 4, 'pd_array': {'type': 'swing_high', 'level': 23617.75}, 'liquidity_sweep': True, 'fpfvg': None, 'meta': {'coverage': 4, 'period_minutes': 5, 'price_range': 34.75}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.23595, price_delta_1m=-0.0009632585661208202, price_delta_5m=-0.0009632585661208202, price_delta_15m=-0.0009632585661208202, volatility_window=0.002680652680652681, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23626.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0007946598855689765, structural_importance=4.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23617.75, 'high': 23639.0, 'low': 23575.75, 'close': 23595.0, 'timeframe': '15m', 'source_movements': 15, 'pd_array': {'type': 'swing_low', 'level': 23613.75}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23626.25}, 'meta': {'coverage': 15, 'period_minutes': 15, 'price_range': 63.25}})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.236015, price_delta_1m=0.000784463384641479, price_delta_5m=0.000784463384641479, price_delta_15m=0.000784463384641479, volatility_window=0.0014723640446581785, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.4, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0006885155604516662, structural_importance=0.32000000000000006, raw_json={'id': 1, 'timestamp': '08:12:00', 'open': 23583.0, 'high': 23617.75, 'low': 23583.0, 'close': 23601.5, 'timeframe': '15m', 'source_movements': 4, 'pd_array': {'type': 'swing_high', 'level': 23617.75}, 'liquidity_sweep': True, 'fpfvg': None, 'meta': {'coverage': 4, 'period_minutes': 15, 'price_range': 34.75}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.236015, price_delta_1m=-0.000688041832943443, price_delta_5m=-0.000688041832943443, price_delta_15m=-0.000688041832943443, volatility_window=0.002679914412219562, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=23626.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0005190348071097176, structural_importance=5.699999999999999, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23617.75, 'high': 23639.0, 'low': 23575.75, 'close': 23601.5, 'timeframe': '60m', 'source_movements': 19, 'pd_array': {'type': 'swing_low', 'level': 23613.75}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23626.25}, 'meta': {'coverage': 19, 'period_minutes': 60, 'price_range': 63.25}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.2857142857142857, absolute_timestamp=1754521200, day_of_week=3, month_phase=0.22580645161290322, normalized_price=0.236015, price_delta_1m=-0.000688041832943443, price_delta_5m=-0.000688041832943443, price_delta_15m=-0.000688041832943443, volatility_window=0.002679914412219562, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=4, liquidity_type=0, fpfvg_gap_size=23626.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0005190348071097176, structural_importance=5.699999999999999, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23617.75, 'high': 23639.0, 'low': 23575.75, 'close': 23601.5, 'timeframe': '1440m', 'source_movements': 19, 'pd_array': {'type': 'swing_low', 'level': 23613.75}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23626.25}, 'meta': {'coverage': 19, 'period_minutes': 1440, 'price_range': 63.25}})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 1,
        "feature_idx": 0
      },
      {
        "source": 1,
        "target": 2,
        "feature_idx": 1
      },
      {
        "source": 2,
        "target": 3,
        "feature_idx": 2
      },
      {
        "source": 3,
        "target": 4,
        "feature_idx": 3
      },
      {
        "source": 4,
        "target": 5,
        "feature_idx": 4
      },
      {
        "source": 5,
        "target": 6,
        "feature_idx": 5
      },
      {
        "source": 6,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 8,
        "feature_idx": 7
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 8
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 9
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 10
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 11
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 12
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 13
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 14
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 15
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 16
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 17
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 18
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 19
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 20
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 21
      }
    ],
    "scale": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 22
      },
      {
        "source": 0,
        "target": 20,
        "feature_idx": 23
      },
      {
        "source": 1,
        "target": 19,
        "feature_idx": 24
      },
      {
        "source": 1,
        "target": 20,
        "feature_idx": 25
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 26
      },
      {
        "source": 2,
        "target": 20,
        "feature_idx": 27
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 28
      },
      {
        "source": 3,
        "target": 20,
        "feature_idx": 29
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 30
      },
      {
        "source": 4,
        "target": 20,
        "feature_idx": 31
      },
      {
        "source": 5,
        "target": 19,
        "feature_idx": 32
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 33
      },
      {
        "source": 6,
        "target": 19,
        "feature_idx": 34
      },
      {
        "source": 6,
        "target": 20,
        "feature_idx": 35
      },
      {
        "source": 6,
        "target": 21,
        "feature_idx": 36
      },
      {
        "source": 7,
        "target": 20,
        "feature_idx": 37
      },
      {
        "source": 7,
        "target": 21,
        "feature_idx": 38
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 39
      },
      {
        "source": 8,
        "target": 21,
        "feature_idx": 40
      },
      {
        "source": 9,
        "target": 20,
        "feature_idx": 41
      },
      {
        "source": 9,
        "target": 21,
        "feature_idx": 42
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 43
      },
      {
        "source": 10,
        "target": 22,
        "feature_idx": 44
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 45
      },
      {
        "source": 11,
        "target": 22,
        "feature_idx": 46
      },
      {
        "source": 12,
        "target": 21,
        "feature_idx": 47
      },
      {
        "source": 12,
        "target": 22,
        "feature_idx": 48
      },
      {
        "source": 13,
        "target": 21,
        "feature_idx": 49
      },
      {
        "source": 13,
        "target": 22,
        "feature_idx": 50
      },
      {
        "source": 14,
        "target": 21,
        "feature_idx": 51
      },
      {
        "source": 14,
        "target": 22,
        "feature_idx": 52
      },
      {
        "source": 15,
        "target": 21,
        "feature_idx": 53
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 54
      },
      {
        "source": 16,
        "target": 21,
        "feature_idx": 55
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 56
      },
      {
        "source": 19,
        "target": 23,
        "feature_idx": 57
      },
      {
        "source": 20,
        "target": 23,
        "feature_idx": 58
      },
      {
        "source": 21,
        "target": 24,
        "feature_idx": 59
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 60
      },
      {
        "source": 23,
        "target": 25,
        "feature_idx": 61
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 62
      },
      {
        "source": 0,
        "target": 19,
        "feature_idx": 63,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 19,
        "feature_idx": 64,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 65,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 66,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 67,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 68,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23607.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23607.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 20,
        "feature_idx": 69,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23607.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23607.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 19,
        "feature_idx": 70,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 71,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 72,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 73,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23580.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23580.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 74,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23580.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23580.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 19,
        "feature_idx": 75,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 19,
        "feature_idx": 76,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 19,
        "feature_idx": 77,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 78,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23617.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 79,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23617.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 19,
        "feature_idx": 80,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 81,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 82,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 83,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 84,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 85,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 86,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 87,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 88,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 23,
        "feature_idx": 89,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 90,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 91,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 23,
        "feature_idx": 92,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 23,
        "feature_idx": 93,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 94,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 95,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 96,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 97,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23617.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 98,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 4,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23617.75
          },
          "fpfvg": null,
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 23,
        "feature_idx": 99,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 23,
        "feature_idx": 100,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 25,
        "feature_idx": 101,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 102,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 103,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 104,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 105,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 106,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 107,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 108,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 109,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 110,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 111,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 112,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 113,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 25,
        "feature_idx": 114,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 25,
        "feature_idx": 115,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 25,
        "feature_idx": 116,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 25,
        "feature_idx": 117,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 25,
        "feature_idx": 118,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 25,
        "feature_idx": 119,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 120,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 121,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 122,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 123,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 124,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 125,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 126,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 127,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 128,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 129,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 10,
        "target": 26,
        "feature_idx": 130,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 11,
        "target": 26,
        "feature_idx": 131,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 132,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 133,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 134,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 135,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 136,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 137,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 138,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 19,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23613.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23626.25
          },
          "liquidity_sweep": true
        }
      }
    ],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 139
      },
      {
        "source": 0,
        "target": 20,
        "feature_idx": 140
      },
      {
        "source": 0,
        "target": 21,
        "feature_idx": 141
      },
      {
        "source": 0,
        "target": 22,
        "feature_idx": 142
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 143
      },
      {
        "source": 0,
        "target": 24,
        "feature_idx": 144
      },
      {
        "source": 0,
        "target": 25,
        "feature_idx": 145
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 146
      },
      {
        "source": 1,
        "target": 19,
        "feature_idx": 147
      },
      {
        "source": 1,
        "target": 20,
        "feature_idx": 148
      },
      {
        "source": 1,
        "target": 21,
        "feature_idx": 149
      },
      {
        "source": 1,
        "target": 22,
        "feature_idx": 150
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 151
      },
      {
        "source": 1,
        "target": 24,
        "feature_idx": 152
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 153
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 154
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 155
      },
      {
        "source": 2,
        "target": 20,
        "feature_idx": 156
      },
      {
        "source": 2,
        "target": 21,
        "feature_idx": 157
      },
      {
        "source": 2,
        "target": 22,
        "feature_idx": 158
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 159
      },
      {
        "source": 2,
        "target": 24,
        "feature_idx": 160
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 161
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 162
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 163
      },
      {
        "source": 3,
        "target": 20,
        "feature_idx": 164
      },
      {
        "source": 3,
        "target": 21,
        "feature_idx": 165
      },
      {
        "source": 3,
        "target": 22,
        "feature_idx": 166
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 167
      },
      {
        "source": 3,
        "target": 24,
        "feature_idx": 168
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 169
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 170
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 171
      },
      {
        "source": 4,
        "target": 20,
        "feature_idx": 172
      },
      {
        "source": 4,
        "target": 21,
        "feature_idx": 173
      },
      {
        "source": 4,
        "target": 22,
        "feature_idx": 174
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 175
      },
      {
        "source": 4,
        "target": 24,
        "feature_idx": 176
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 177
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 178
      },
      {
        "source": 5,
        "target": 19,
        "feature_idx": 179
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 180
      },
      {
        "source": 5,
        "target": 21,
        "feature_idx": 181
      },
      {
        "source": 5,
        "target": 22,
        "feature_idx": 182
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 183
      },
      {
        "source": 5,
        "target": 24,
        "feature_idx": 184
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 185
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 186
      },
      {
        "source": 6,
        "target": 19,
        "feature_idx": 187
      },
      {
        "source": 6,
        "target": 20,
        "feature_idx": 188
      },
      {
        "source": 6,
        "target": 21,
        "feature_idx": 189
      },
      {
        "source": 6,
        "target": 22,
        "feature_idx": 190
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 191
      },
      {
        "source": 6,
        "target": 24,
        "feature_idx": 192
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 193
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 194
      },
      {
        "source": 7,
        "target": 19,
        "feature_idx": 195
      },
      {
        "source": 7,
        "target": 20,
        "feature_idx": 196
      },
      {
        "source": 7,
        "target": 21,
        "feature_idx": 197
      },
      {
        "source": 7,
        "target": 22,
        "feature_idx": 198
      },
      {
        "source": 7,
        "target": 23,
        "feature_idx": 199
      },
      {
        "source": 7,
        "target": 24,
        "feature_idx": 200
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 201
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 202
      },
      {
        "source": 8,
        "target": 19,
        "feature_idx": 203
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 204
      },
      {
        "source": 8,
        "target": 21,
        "feature_idx": 205
      },
      {
        "source": 8,
        "target": 22,
        "feature_idx": 206
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 207
      },
      {
        "source": 8,
        "target": 24,
        "feature_idx": 208
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 209
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 210
      },
      {
        "source": 9,
        "target": 19,
        "feature_idx": 211
      },
      {
        "source": 9,
        "target": 20,
        "feature_idx": 212
      },
      {
        "source": 9,
        "target": 21,
        "feature_idx": 213
      },
      {
        "source": 9,
        "target": 22,
        "feature_idx": 214
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 215
      },
      {
        "source": 9,
        "target": 24,
        "feature_idx": 216
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 217
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 218
      },
      {
        "source": 10,
        "target": 19,
        "feature_idx": 219
      },
      {
        "source": 10,
        "target": 20,
        "feature_idx": 220
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 221
      },
      {
        "source": 10,
        "target": 22,
        "feature_idx": 222
      },
      {
        "source": 10,
        "target": 23,
        "feature_idx": 223
      },
      {
        "source": 10,
        "target": 24,
        "feature_idx": 224
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 225
      },
      {
        "source": 10,
        "target": 26,
        "feature_idx": 226
      },
      {
        "source": 11,
        "target": 19,
        "feature_idx": 227
      },
      {
        "source": 11,
        "target": 20,
        "feature_idx": 228
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 229
      },
      {
        "source": 11,
        "target": 22,
        "feature_idx": 230
      },
      {
        "source": 11,
        "target": 23,
        "feature_idx": 231
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 232
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 233
      },
      {
        "source": 11,
        "target": 26,
        "feature_idx": 234
      },
      {
        "source": 12,
        "target": 19,
        "feature_idx": 235
      },
      {
        "source": 12,
        "target": 20,
        "feature_idx": 236
      },
      {
        "source": 12,
        "target": 21,
        "feature_idx": 237
      },
      {
        "source": 12,
        "target": 22,
        "feature_idx": 238
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 239
      },
      {
        "source": 12,
        "target": 24,
        "feature_idx": 240
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 241
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 242
      },
      {
        "source": 13,
        "target": 19,
        "feature_idx": 243
      },
      {
        "source": 13,
        "target": 20,
        "feature_idx": 244
      },
      {
        "source": 13,
        "target": 21,
        "feature_idx": 245
      },
      {
        "source": 13,
        "target": 22,
        "feature_idx": 246
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 247
      },
      {
        "source": 13,
        "target": 24,
        "feature_idx": 248
      },
      {
        "source": 13,
        "target": 25,
        "feature_idx": 249
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 250
      },
      {
        "source": 14,
        "target": 19,
        "feature_idx": 251
      },
      {
        "source": 14,
        "target": 20,
        "feature_idx": 252
      },
      {
        "source": 14,
        "target": 21,
        "feature_idx": 253
      },
      {
        "source": 14,
        "target": 22,
        "feature_idx": 254
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 255
      },
      {
        "source": 14,
        "target": 24,
        "feature_idx": 256
      },
      {
        "source": 14,
        "target": 25,
        "feature_idx": 257
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 258
      },
      {
        "source": 15,
        "target": 19,
        "feature_idx": 259
      },
      {
        "source": 15,
        "target": 20,
        "feature_idx": 260
      },
      {
        "source": 15,
        "target": 21,
        "feature_idx": 261
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 262
      },
      {
        "source": 15,
        "target": 23,
        "feature_idx": 263
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 264
      },
      {
        "source": 15,
        "target": 25,
        "feature_idx": 265
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 266
      },
      {
        "source": 16,
        "target": 19,
        "feature_idx": 267
      },
      {
        "source": 16,
        "target": 20,
        "feature_idx": 268
      },
      {
        "source": 16,
        "target": 21,
        "feature_idx": 269
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 270
      },
      {
        "source": 16,
        "target": 23,
        "feature_idx": 271
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 272
      },
      {
        "source": 16,
        "target": 25,
        "feature_idx": 273
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 274
      },
      {
        "source": 17,
        "target": 19,
        "feature_idx": 275
      },
      {
        "source": 17,
        "target": 20,
        "feature_idx": 276
      },
      {
        "source": 17,
        "target": 21,
        "feature_idx": 277
      },
      {
        "source": 17,
        "target": 22,
        "feature_idx": 278
      },
      {
        "source": 17,
        "target": 23,
        "feature_idx": 279
      },
      {
        "source": 17,
        "target": 24,
        "feature_idx": 280
      },
      {
        "source": 17,
        "target": 25,
        "feature_idx": 281
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 282
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 283
      },
      {
        "source": 18,
        "target": 20,
        "feature_idx": 284
      },
      {
        "source": 18,
        "target": 21,
        "feature_idx": 285
      },
      {
        "source": 18,
        "target": 22,
        "feature_idx": 286
      },
      {
        "source": 18,
        "target": 23,
        "feature_idx": 287
      },
      {
        "source": 18,
        "target": 24,
        "feature_idx": 288
      },
      {
        "source": 18,
        "target": 25,
        "feature_idx": 289
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 290
      },
      {
        "source": 19,
        "target": 23,
        "feature_idx": 291
      },
      {
        "source": 19,
        "target": 24,
        "feature_idx": 292
      },
      {
        "source": 19,
        "target": 25,
        "feature_idx": 293
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 294
      },
      {
        "source": 20,
        "target": 23,
        "feature_idx": 295
      },
      {
        "source": 20,
        "target": 24,
        "feature_idx": 296
      },
      {
        "source": 20,
        "target": 25,
        "feature_idx": 297
      },
      {
        "source": 20,
        "target": 26,
        "feature_idx": 298
      },
      {
        "source": 21,
        "target": 23,
        "feature_idx": 299
      },
      {
        "source": 21,
        "target": 24,
        "feature_idx": 300
      },
      {
        "source": 21,
        "target": 25,
        "feature_idx": 301
      },
      {
        "source": 21,
        "target": 26,
        "feature_idx": 302
      },
      {
        "source": 22,
        "target": 23,
        "feature_idx": 303
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 304
      },
      {
        "source": 22,
        "target": 25,
        "feature_idx": 305
      },
      {
        "source": 22,
        "target": 26,
        "feature_idx": 306
      },
      {
        "source": 23,
        "target": 25,
        "feature_idx": 307
      },
      {
        "source": 23,
        "target": 26,
        "feature_idx": 308
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 309
      },
      {
        "source": 24,
        "target": 26,
        "feature_idx": 310
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 311
      }
    ],
    "temporal_echo": [
      {
        "source": 0,
        "target": 19,
        "feature_idx": 312
      },
      {
        "source": 0,
        "target": 20,
        "feature_idx": 313
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 314
      },
      {
        "source": 0,
        "target": 25,
        "feature_idx": 315
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 316
      },
      {
        "source": 1,
        "target": 19,
        "feature_idx": 317
      },
      {
        "source": 1,
        "target": 20,
        "feature_idx": 318
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 319
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 320
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 321
      },
      {
        "source": 2,
        "target": 19,
        "feature_idx": 322
      },
      {
        "source": 2,
        "target": 20,
        "feature_idx": 323
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 324
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 325
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 326
      },
      {
        "source": 3,
        "target": 19,
        "feature_idx": 327
      },
      {
        "source": 3,
        "target": 20,
        "feature_idx": 328
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 329
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 330
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 331
      },
      {
        "source": 4,
        "target": 19,
        "feature_idx": 332
      },
      {
        "source": 4,
        "target": 20,
        "feature_idx": 333
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 334
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 335
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 336
      },
      {
        "source": 5,
        "target": 19,
        "feature_idx": 337
      },
      {
        "source": 5,
        "target": 20,
        "feature_idx": 338
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 339
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 340
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 341
      },
      {
        "source": 6,
        "target": 19,
        "feature_idx": 342
      },
      {
        "source": 6,
        "target": 20,
        "feature_idx": 343
      },
      {
        "source": 6,
        "target": 21,
        "feature_idx": 344
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 345
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 346
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 347
      },
      {
        "source": 7,
        "target": 20,
        "feature_idx": 348
      },
      {
        "source": 7,
        "target": 21,
        "feature_idx": 349
      },
      {
        "source": 8,
        "target": 20,
        "feature_idx": 350
      },
      {
        "source": 8,
        "target": 21,
        "feature_idx": 351
      },
      {
        "source": 9,
        "target": 20,
        "feature_idx": 352
      },
      {
        "source": 9,
        "target": 21,
        "feature_idx": 353
      },
      {
        "source": 10,
        "target": 21,
        "feature_idx": 354
      },
      {
        "source": 10,
        "target": 22,
        "feature_idx": 355
      },
      {
        "source": 10,
        "target": 24,
        "feature_idx": 356
      },
      {
        "source": 11,
        "target": 21,
        "feature_idx": 357
      },
      {
        "source": 11,
        "target": 22,
        "feature_idx": 358
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 359
      },
      {
        "source": 12,
        "target": 21,
        "feature_idx": 360
      },
      {
        "source": 12,
        "target": 22,
        "feature_idx": 361
      },
      {
        "source": 12,
        "target": 24,
        "feature_idx": 362
      },
      {
        "source": 13,
        "target": 21,
        "feature_idx": 363
      },
      {
        "source": 13,
        "target": 22,
        "feature_idx": 364
      },
      {
        "source": 13,
        "target": 24,
        "feature_idx": 365
      },
      {
        "source": 14,
        "target": 21,
        "feature_idx": 366
      },
      {
        "source": 14,
        "target": 22,
        "feature_idx": 367
      },
      {
        "source": 14,
        "target": 24,
        "feature_idx": 368
      },
      {
        "source": 15,
        "target": 21,
        "feature_idx": 369
      },
      {
        "source": 15,
        "target": 22,
        "feature_idx": 370
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 371
      },
      {
        "source": 16,
        "target": 21,
        "feature_idx": 372
      },
      {
        "source": 16,
        "target": 22,
        "feature_idx": 373
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 374
      },
      {
        "source": 19,
        "target": 23,
        "feature_idx": 375
      },
      {
        "source": 19,
        "target": 25,
        "feature_idx": 376
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 377
      },
      {
        "source": 20,
        "target": 23,
        "feature_idx": 378
      },
      {
        "source": 20,
        "target": 25,
        "feature_idx": 379
      },
      {
        "source": 20,
        "target": 26,
        "feature_idx": 380
      },
      {
        "source": 21,
        "target": 24,
        "feature_idx": 381
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 382
      },
      {
        "source": 23,
        "target": 25,
        "feature_idx": 383
      },
      {
        "source": 23,
        "target": 26,
        "feature_idx": 384
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 385
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    