{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21
    ],
    "5m": [],
    "15m": [],
    "1h": [
      22,
      23,
      24,
      25,
      26,
      27,
      28
    ],
    "D": [],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23508, price_delta_1m=0.013180449123543624, price_delta_5m=0.009346458469593392, price_delta_15m=0.008320639489990794, volatility_window=0.04808778767411442, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23508.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23522, price_delta_1m=-0.007630463497220402, price_delta_5m=-0.006480316756332445, price_delta_15m=-0.007911938620678753, volatility_window=0.011089019936018669, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23522.0, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.234705, price_delta_1m=-0.000292214010444129, price_delta_5m=-0.014275097936042093, price_delta_15m=-0.005130030333875527, volatility_window=0.019659735860312538, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23470.5, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2348675, price_delta_1m=-0.006182223941751919, price_delta_5m=-0.002314437045836618, price_delta_15m=0.020254598393358397, volatility_window=0.029494777106456857, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23486.75, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23508, price_delta_1m=0.020351775864416912, price_delta_5m=-0.01981312918483078, price_delta_15m=-0.007076679529948269, volatility_window=0.02962677299166671, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:00:00', 'price_level': 23508.0, 'movement_type': 'open'})",
    "RichNodeFeature(time_minutes=300.0, daily_phase_sin=1.2246467991473532e-16, daily_phase_cos=-1.0, session_position=2.0134228187919465, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23522, price_delta_1m=0.007004368015812385, price_delta_5m=0.016511053346180776, price_delta_15m=0.0001221506676251276, volatility_window=0.029874837831681704, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:00:00', 'price_level': 23522.0, 'movement_type': 'session_high'})",
    "RichNodeFeature(time_minutes=330.0, daily_phase_sin=-0.13052619222005132, daily_phase_cos=-0.9914448613738105, session_position=2.214765100671141, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.234705, price_delta_1m=-0.020670234079658064, price_delta_5m=0.006829950031697837, price_delta_15m=0.008382176966019807, volatility_window=0.037961003588736976, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=2, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '12:30:00', 'price_level': 23470.5, 'movement_type': 'session_low'})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2348675, price_delta_1m=-0.00842039609670993, price_delta_5m=0.0028524196671376894, price_delta_15m=-0.011253379645865882, volatility_window=0.04847920777638952, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=3, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:29:00', 'price_level': 23486.75, 'movement_type': 'close'})",
    "RichNodeFeature(time_minutes=10.0, daily_phase_sin=0.9537169507482269, daily_phase_cos=-0.30070579950427295, session_position=0.06711409395973154, time_to_close=139.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2350675, price_delta_1m=-0.023266094150588883, price_delta_5m=0.017246726369196996, price_delta_15m=0.005737199122879871, volatility_window=0.014523863414071188, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:10:00', 'price': 23506.75, 'action': 'touch', 'context': 'Pre-market FPFVG premium high created'})",
    "RichNodeFeature(time_minutes=13.0, daily_phase_sin=0.9496991262018769, daily_phase_cos=-0.31316380648374964, session_position=0.087248322147651, time_to_close=136.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2350975, price_delta_1m=-0.016467917788562134, price_delta_5m=0.0037353538903677035, price_delta_15m=0.0009169937683825419, volatility_window=0.03825523781286315, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:13:00', 'price': 23509.75, 'action': 'break', 'context': 'Expansion lower initiated'})",
    "RichNodeFeature(time_minutes=14.0, daily_phase_sin=0.9483236552061993, daily_phase_cos=-0.317304656405092, session_position=0.09395973154362416, time_to_close=135.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2349975, price_delta_1m=-0.019873567341109243, price_delta_5m=-0.0018595560356647184, price_delta_15m=0.01778169662604035, volatility_window=0.022084314588813207, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:14:00', 'price': 23499.75, 'action': 'delivery', 'context': 'Three-day pre-market FPFVG redelivered'})",
    "RichNodeFeature(time_minutes=25.0, daily_phase_sin=0.9320078692827986, daily_phase_cos=-0.3624380382837014, session_position=0.16778523489932887, time_to_close=124.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23501, price_delta_1m=0.006767999106509562, price_delta_5m=-0.007470713388224623, price_delta_15m=0.005420278917535845, volatility_window=0.04986111955014203, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:25:00', 'price': 23501.0, 'action': 'break', 'context': 'Expansion higher initiated'})",
    "RichNodeFeature(time_minutes=27.0, daily_phase_sin=0.9288095528719242, daily_phase_cos=-0.3705574375098361, session_position=0.18120805369127516, time_to_close=122.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.235165, price_delta_1m=-0.0009680149518107497, price_delta_5m=-0.020998647043493253, price_delta_15m=0.00885680835931667, volatility_window=0.04504859406773981, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:27:00', 'price': 23516.5, 'action': 'delivery', 'context': 'Three-day Asia FPFVG redelivered'})",
    "RichNodeFeature(time_minutes=43.0, daily_phase_sin=0.9006982393225879, daily_phase_cos=-0.43444525740441703, session_position=0.28859060402684567, time_to_close=106.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2351675, price_delta_1m=0.01091311159290758, price_delta_5m=-0.011895649004351722, price_delta_15m=-0.0017342477840090569, volatility_window=0.031132058212480135, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:43:00', 'price': 23516.75, 'action': 'break', 'context': 'Retracement lower initiated'})",
    "RichNodeFeature(time_minutes=44.0, daily_phase_sin=0.8987940462991669, daily_phase_cos=-0.4383711467890775, session_position=0.2953020134228188, time_to_close=105.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23504, price_delta_1m=-0.021465695564648862, price_delta_5m=-0.01961567127911489, price_delta_15m=-0.013685640086429154, volatility_window=0.019332398475431847, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:44:00', 'price': 23504.0, 'action': 'delivery', 'context': \"Clustered FPFVGs redelivered: today's pre-market and previous day's PM\"})",
    "RichNodeFeature(time_minutes=46.0, daily_phase_sin=0.8949343616020251, daily_phase_cos=-0.4461978131098088, session_position=0.3087248322147651, time_to_close=103.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23522, price_delta_1m=0.004939621182974989, price_delta_5m=-0.022688214703497543, price_delta_15m=0.008833519518143682, volatility_window=0.02615120799136609, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:46:00', 'price': 23522.0, 'action': 'touch', 'context': 'Pre-market session high created'})",
    "RichNodeFeature(time_minutes=51.0, daily_phase_sin=0.8849876374630419, daily_phase_cos=-0.4656145203251114, session_position=0.3422818791946309, time_to_close=98.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2352175, price_delta_1m=0.0023554457891269403, price_delta_5m=0.005005651624139197, price_delta_15m=0.010747801845345498, volatility_window=0.04893587896735253, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:51:00', 'price': 23521.75, 'action': 'touch', 'context': 'Reversal point at pre-market high, expansion lower initiated'})",
    "RichNodeFeature(time_minutes=62.0, daily_phase_sin=0.8616291604415257, daily_phase_cos=-0.5075383629607042, session_position=0.4161073825503356, time_to_close=87.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.234975, price_delta_1m=0.011297460739178356, price_delta_5m=-0.005802632413714172, price_delta_15m=0.006627178882281374, volatility_window=0.029069632676869052, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:02:00', 'price': 23497.5, 'action': 'delivery', 'context': \"Previous day's lunch FPFVG redelivered\"})",
    "RichNodeFeature(time_minutes=70.0, daily_phase_sin=0.8433914458128858, daily_phase_cos=-0.5372996083468236, session_position=0.4697986577181208, time_to_close=79.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.234715, price_delta_1m=0.0034708573841610393, price_delta_5m=0.006712629257193808, price_delta_15m=0.020484903242234283, volatility_window=0.03555697597794433, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:10:00', 'price': 23471.5, 'action': 'delivery', 'context': \"Multiple redeliveries: today's Asia FPFVG, three-day lunch and PM rebalances\"})",
    "RichNodeFeature(time_minutes=96.0, daily_phase_sin=0.7771459614569711, daily_phase_cos=-0.6293203910498373, session_position=0.6442953020134228, time_to_close=53.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.23474, price_delta_1m=-0.001355125890515388, price_delta_5m=0.00288733395746538, price_delta_15m=-0.02401877798974394, volatility_window=0.012112827391975825, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:36:00', 'price': 23474.0, 'action': 'break', 'context': 'Expansion higher initiated'})",
    "RichNodeFeature(time_minutes=117.0, daily_phase_sin=0.7163019434246545, daily_phase_cos=-0.69779045984168, session_position=0.785234899328859, time_to_close=32.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.2350375, price_delta_1m=-0.01657129721414726, price_delta_5m=-0.001783121496023591, price_delta_15m=-0.002349312294071218, volatility_window=0.020569655139922764, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:57:00', 'price': 23503.75, 'action': 'delivery', 'context': \"Previous day's PM session FPFVG redelivered during consolidation\"})",
    "RichNodeFeature(time_minutes=125.0, daily_phase_sin=0.6915130557822694, daily_phase_cos=-0.7223639620597555, session_position=0.8389261744966443, time_to_close=24.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.234705, price_delta_1m=0.0007734639776194879, price_delta_5m=0.015204826581851707, price_delta_15m=-0.0067467814787201645, volatility_window=0.03246604005861575, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=4, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '09:05:00', 'price': 23470.5, 'action': 'touch', 'context': \"Pre-market session low created, today's Asia FPFVG redelivered\"})",
    "RichNodeFeature(time_minutes=13.0, daily_phase_sin=0.9496991262018769, daily_phase_cos=-0.31316380648374964, session_position=0.087248322147651, time_to_close=136.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=0.005916398117213113, price_delta_5m=-0.0024569156524576756, price_delta_15m=0.009531338097953713, volatility_window=0.010070196753388334, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:13:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})",
    "RichNodeFeature(time_minutes=25.0, daily_phase_sin=0.9320078692827986, daily_phase_cos=-0.3624380382837014, session_position=0.16778523489932887, time_to_close=124.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=-0.031961259209004104, price_delta_5m=0.0046701686484088316, price_delta_15m=0.01252043978294832, volatility_window=0.02477168262542206, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:25:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})",
    "RichNodeFeature(time_minutes=44.0, daily_phase_sin=0.8987940462991669, daily_phase_cos=-0.4383711467890775, session_position=0.2953020134228188, time_to_close=105.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=-0.004653792460051579, price_delta_5m=0.004376071832203585, price_delta_15m=0.009422310845593503, volatility_window=0.022743534102697374, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:44:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})",
    "RichNodeFeature(time_minutes=51.0, daily_phase_sin=0.8849876374630419, daily_phase_cos=-0.4656145203251114, session_position=0.3422818791946309, time_to_close=98.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=-6.701802379513343e-05, price_delta_5m=0.0031272776853939, price_delta_15m=-0.0008377726206879458, volatility_window=0.041690195575996965, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '07:51:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})",
    "RichNodeFeature(time_minutes=67.0, daily_phase_sin=0.8503522249955631, daily_phase_cos=-0.5262139236518693, session_position=0.44966442953020136, time_to_close=82.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=-3.205203769838538e-05, price_delta_5m=-0.02258794017060011, price_delta_15m=-0.002391624055719706, volatility_window=0.04689027174548984, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:07:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})",
    "RichNodeFeature(time_minutes=96.0, daily_phase_sin=0.7771459614569711, daily_phase_cos=-0.6293203910498373, session_position=0.6442953020134228, time_to_close=53.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=-0.013881430838637424, price_delta_5m=0.005606932434810404, price_delta_15m=0.005487083541335358, volatility_window=0.0116052803534153, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:36:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})",
    "RichNodeFeature(time_minutes=117.0, daily_phase_sin=0.7163019434246545, daily_phase_cos=-0.69779045984168, session_position=0.785234899328859, time_to_close=32.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1753830000, day_of_week=2, month_phase=0.967741935483871, normalized_price=0.0, price_delta_1m=-0.0034838176491333336, price_delta_5m=-0.0005477757330261022, price_delta_15m=-0.0013967212583825956, volatility_window=0.01796062571952565, energy_state=0.0745, contamination_coefficient=0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=5, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=0.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'timestamp': '08:57:00', 'event_type': 'heuristic_cascade_initiation', 'confidence': 3, 'trigger_source': 'Phase Transition'})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 4,
        "feature_idx": 0
      },
      {
        "source": 4,
        "target": 8,
        "feature_idx": 1
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 2
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 3
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 4
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 5
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 6
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 7
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 8
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 9
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 10
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 11
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 12
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 13
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 14
      },
      {
        "source": 21,
        "target": 3,
        "feature_idx": 15
      },
      {
        "source": 3,
        "target": 7,
        "feature_idx": 16
      },
      {
        "source": 7,
        "target": 1,
        "feature_idx": 17
      },
      {
        "source": 1,
        "target": 5,
        "feature_idx": 18
      },
      {
        "source": 5,
        "target": 2,
        "feature_idx": 19
      },
      {
        "source": 2,
        "target": 6,
        "feature_idx": 20
      },
      {
        "source": 22,
        "target": 23,
        "feature_idx": 21
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 22
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 23
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 24
      },
      {
        "source": 26,
        "target": 27,
        "feature_idx": 25
      },
      {
        "source": 27,
        "target": 28,
        "feature_idx": 26
      }
    ],
    "scale": [],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [],
    "temporal_echo": [
      {
        "source": 0,
        "target": 22,
        "feature_idx": 27
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 28
      },
      {
        "source": 4,
        "target": 22,
        "feature_idx": 29
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 30
      },
      {
        "source": 8,
        "target": 22,
        "feature_idx": 31
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 32
      },
      {
        "source": 9,
        "target": 22,
        "feature_idx": 33
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 34
      },
      {
        "source": 10,
        "target": 22,
        "feature_idx": 35
      },
      {
        "source": 10,
        "target": 23,
        "feature_idx": 36
      },
      {
        "source": 11,
        "target": 22,
        "feature_idx": 37
      },
      {
        "source": 11,
        "target": 23,
        "feature_idx": 38
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 39
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 40
      },
      {
        "source": 12,
        "target": 22,
        "feature_idx": 41
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 42
      },
      {
        "source": 12,
        "target": 24,
        "feature_idx": 43
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 44
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 45
      },
      {
        "source": 13,
        "target": 24,
        "feature_idx": 46
      },
      {
        "source": 13,
        "target": 25,
        "feature_idx": 47
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 48
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 49
      },
      {
        "source": 14,
        "target": 24,
        "feature_idx": 50
      },
      {
        "source": 14,
        "target": 25,
        "feature_idx": 51
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 52
      },
      {
        "source": 15,
        "target": 23,
        "feature_idx": 53
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 54
      },
      {
        "source": 15,
        "target": 25,
        "feature_idx": 55
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 56
      },
      {
        "source": 16,
        "target": 23,
        "feature_idx": 57
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 58
      },
      {
        "source": 16,
        "target": 25,
        "feature_idx": 59
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 60
      },
      {
        "source": 17,
        "target": 24,
        "feature_idx": 61
      },
      {
        "source": 17,
        "target": 25,
        "feature_idx": 62
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 63
      },
      {
        "source": 18,
        "target": 24,
        "feature_idx": 64
      },
      {
        "source": 18,
        "target": 25,
        "feature_idx": 65
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 66
      },
      {
        "source": 18,
        "target": 27,
        "feature_idx": 67
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 68
      },
      {
        "source": 19,
        "target": 27,
        "feature_idx": 69
      },
      {
        "source": 19,
        "target": 28,
        "feature_idx": 70
      },
      {
        "source": 20,
        "target": 27,
        "feature_idx": 71
      },
      {
        "source": 20,
        "target": 28,
        "feature_idx": 72
      },
      {
        "source": 21,
        "target": 27,
        "feature_idx": 73
      },
      {
        "source": 21,
        "target": 28,
        "feature_idx": 74
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    