{
  "nodes": {
    "1m": [
      0,
      1,
      2,
      3,
      4,
      5,
      6,
      7,
      8,
      9,
      10,
      11,
      12,
      13,
      14,
      15,
      16,
      17,
      18,
      19,
      20,
      21,
      22
    ],
    "5m": [
      23,
      24,
      25,
      26,
      27
    ],
    "15m": [
      28,
      29
    ],
    "1h": [
      30
    ],
    "D": [
      31
    ],
    "W": []
  },
  "rich_node_features": [
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'price_level': 23144.25, 'open': 23144.25, 'high': 23144.25, 'low': 23144.25, 'close': 23144.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231375, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 1, 'timestamp': '07:00:00', 'price_level': 23137.5, 'open': 23137.5, 'high': 23137.5, 'low': 23137.5, 'close': 23137.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 2, 'timestamp': '07:01:00', 'price_level': 23165.0, 'open': 23165.0, 'high': 23165.0, 'low': 23165.0, 'close': 23165.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=1.0, daily_phase_sin=0.964787323828813, daily_phase_cos=-0.2630312144579746, session_position=0.006711409395973154, time_to_close=148.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23158, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 3, 'timestamp': '07:01:00', 'price_level': 23158.0, 'open': 23158.0, 'high': 23158.0, 'low': 23158.0, 'close': 23158.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=2.0, daily_phase_sin=0.963630453208623, daily_phase_cos=-0.26723837607825685, session_position=0.013422818791946308, time_to_close=147.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231725, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 4, 'timestamp': '07:02:00', 'price_level': 23172.5, 'open': 23172.5, 'high': 23172.5, 'low': 23172.5, 'close': 23172.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=8.0, daily_phase_sin=0.9563047559630355, daily_phase_cos=-0.29237170472273666, session_position=0.053691275167785234, time_to_close=141.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315225, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 5, 'timestamp': '07:08:00', 'price_level': 23152.25, 'open': 23152.25, 'high': 23152.25, 'low': 23152.25, 'close': 23152.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=11.0, daily_phase_sin=0.9523957996432784, daily_phase_cos=-0.30486429902801077, session_position=0.0738255033557047, time_to_close=138.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317575, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 6, 'timestamp': '07:11:00', 'price_level': 23175.75, 'open': 23175.75, 'high': 23175.75, 'low': 23175.75, 'close': 23175.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=12.0, daily_phase_sin=0.9510565162951536, daily_phase_cos=-0.30901699437494734, session_position=0.08053691275167785, time_to_close=137.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2315425, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 7, 'timestamp': '07:12:00', 'price_level': 23154.25, 'open': 23154.25, 'high': 23154.25, 'low': 23154.25, 'close': 23154.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=22.0, daily_phase_sin=0.9366721892483976, daily_phase_cos=-0.35020738125946754, session_position=0.1476510067114094, time_to_close=127.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23178, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 8, 'timestamp': '07:22:00', 'price_level': 23178.0, 'open': 23178.0, 'high': 23178.0, 'low': 23178.0, 'close': 23178.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=26.0, daily_phase_sin=0.9304175679820246, daily_phase_cos=-0.3665012267242972, session_position=0.174496644295302, time_to_close=123.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 9, 'timestamp': '07:26:00', 'price_level': 23165.0, 'open': 23165.0, 'high': 23165.0, 'low': 23165.0, 'close': 23165.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319675, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 10, 'timestamp': '07:32:00', 'price_level': 23196.75, 'open': 23196.75, 'high': 23196.75, 'low': 23196.75, 'close': 23196.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=33.0, daily_phase_sin=0.9187912101488983, daily_phase_cos=-0.39474385638426723, session_position=0.2214765100671141, time_to_close=116.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23181, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 11, 'timestamp': '07:33:00', 'price_level': 23181.0, 'open': 23181.0, 'high': 23181.0, 'low': 23181.0, 'close': 23181.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=52.0, daily_phase_sin=0.8829475928589271, daily_phase_cos=-0.46947156278589053, session_position=0.348993288590604, time_to_close=97.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2317825, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 12, 'timestamp': '07:52:00', 'price_level': 23178.25, 'open': 23178.25, 'high': 23178.25, 'low': 23178.25, 'close': 23178.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=66.0, daily_phase_sin=0.8526401643540923, daily_phase_cos=-0.5224985647159488, session_position=0.4429530201342282, time_to_close=83.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314825, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 13, 'timestamp': '08:06:00', 'price_level': 23148.25, 'open': 23148.25, 'high': 23148.25, 'low': 23148.25, 'close': 23148.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=68.0, daily_phase_sin=0.8480480961564261, daily_phase_cos=-0.5299192642332048, session_position=0.4563758389261745, time_to_close=81.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316025, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 14, 'timestamp': '08:08:00', 'price_level': 23160.25, 'open': 23160.25, 'high': 23160.25, 'low': 23160.25, 'close': 23160.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314425, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 15, 'timestamp': '08:12:00', 'price_level': 23144.25, 'open': 23144.25, 'high': 23144.25, 'low': 23144.25, 'close': 23144.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=73.0, daily_phase_sin=0.8362861558477594, daily_phase_cos=-0.548293229519914, session_position=0.4899328859060403, time_to_close=76.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314025, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 16, 'timestamp': '08:13:00', 'price_level': 23140.25, 'open': 23140.25, 'high': 23140.25, 'low': 23140.25, 'close': 23140.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=82.0, daily_phase_sin=0.8141155183563192, daily_phase_cos=-0.5807029557109398, session_position=0.5503355704697986, time_to_close=67.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2318625, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 17, 'timestamp': '08:22:00', 'price_level': 23186.25, 'open': 23186.25, 'high': 23186.25, 'low': 23186.25, 'close': 23186.25, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=92.0, daily_phase_sin=0.788010753606722, daily_phase_cos=-0.6156614753256582, session_position=0.6174496644295302, time_to_close=57.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2314675, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 18, 'timestamp': '08:32:00', 'price_level': 23146.75, 'open': 23146.75, 'high': 23146.75, 'low': 23146.75, 'close': 23146.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=97.0, daily_phase_sin=0.7743926440821857, daily_phase_cos=-0.6327053285625159, session_position=0.6510067114093959, time_to_close=52.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231775, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 19, 'timestamp': '08:37:00', 'price_level': 23177.5, 'open': 23177.5, 'high': 23177.5, 'low': 23177.5, 'close': 23177.5, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=137.0, daily_phase_sin=0.6527597524627223, daily_phase_cos=-0.7575649843840497, session_position=0.9194630872483222, time_to_close=12.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2319375, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 20, 'timestamp': '09:17:00', 'price_level': 23193.75, 'open': 23193.75, 'high': 23193.75, 'low': 23193.75, 'close': 23193.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=145.0, daily_phase_sin=0.6259234721840592, daily_phase_cos=-0.7798844830928817, session_position=0.9731543624161074, time_to_close=4.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23167, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 21, 'timestamp': '09:25:00', 'price_level': 23167.0, 'open': 23167.0, 'high': 23167.0, 'low': 23167.0, 'close': 23167.0, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=149.0, daily_phase_sin=0.6122172800344493, daily_phase_cos=-0.7906895737438434, session_position=1.0, time_to_close=0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=0.0, price_delta_5m=0.0, price_delta_15m=0.0, volatility_window=0.0, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.1, event_type_id=0, timeframe_source=0, liquidity_type=0, fpfvg_gap_size=0.0, fpfvg_interaction_count=0, first_presentation_flag=1.0, pd_array_strength=0.0, structural_importance=0.0, raw_json={'id': 22, 'timestamp': '09:29:00', 'price_level': 23168.75, 'open': 23168.75, 'high': 23168.75, 'low': 23168.75, 'close': 23168.75, 'event_type': 'session_level', 'context': '', 'contamination_risk': False, 'meta': {'coverage': 1, 'source': '1m_movement'}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231725, price_delta_1m=0.0012206055499746158, price_delta_5m=0.0012206055499746158, price_delta_15m=0.0012206055499746158, volatility_window=0.001510411047577948, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23165.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.001510411047577948, structural_importance=0.5, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23144.25, 'high': 23172.5, 'low': 23137.5, 'close': 23172.5, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23137.5}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23165.0}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 35.0}})",
    "RichNodeFeature(time_minutes=8.0, daily_phase_sin=0.9563047559630355, daily_phase_cos=-0.29237170472273666, session_position=0.053691275167785234, time_to_close=141.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.23165, price_delta_1m=0.0005507024155319677, price_delta_5m=0.0005507024155319677, price_delta_15m=0.0005507024155319677, volatility_window=0.0011115907619253184, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23175.75, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.00046406216274552127, structural_importance=0.5, raw_json={'id': 1, 'timestamp': '07:08:00', 'open': 23152.25, 'high': 23178.0, 'low': 23152.25, 'close': 23165.0, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_high', 'level': 23175.75}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23175.75}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 25.75}})",
    "RichNodeFeature(time_minutes=32.0, daily_phase_sin=0.9205048534524404, daily_phase_cos=-0.3907311284892736, session_position=0.21476510067114093, time_to_close=117.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316025, price_delta_1m=-0.0015734962871954046, price_delta_5m=-0.0015734962871954046, price_delta_15m=-0.0015734962871954046, volatility_window=0.002094105201800499, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23148.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0005181291220949688, structural_importance=0.5, raw_json={'id': 2, 'timestamp': '07:32:00', 'open': 23196.75, 'high': 23196.75, 'low': 23148.25, 'close': 23160.25, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23148.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23148.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 48.5}})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.231775, price_delta_1m=0.0014366419305010963, price_delta_5m=0.0014366419305010963, price_delta_15m=0.0014366419305010963, volatility_window=0.0019846834214216373, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.5, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23186.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.001607162118433826, structural_importance=0.5, raw_json={'id': 3, 'timestamp': '08:12:00', 'open': 23144.25, 'high': 23186.25, 'low': 23140.25, 'close': 23177.5, 'timeframe': '5m', 'source_movements': 5, 'pd_array': {'type': 'swing_low', 'level': 23140.25}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23186.25}, 'meta': {'coverage': 5, 'period_minutes': 5, 'price_range': 46.0}})",
    "RichNodeFeature(time_minutes=137.0, daily_phase_sin=0.6527597524627223, daily_phase_cos=-0.7575649843840497, session_position=0.9194630872483222, time_to_close=12.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=-0.0010778765831312314, price_delta_5m=-0.0010778765831312314, price_delta_15m=-0.0010778765831312314, volatility_window=0.0011545724305368223, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.3, event_type_id=1, timeframe_source=1, liquidity_type=0, fpfvg_gap_size=23167.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=7.553277582951174e-05, structural_importance=0.18, raw_json={'id': 4, 'timestamp': '09:17:00', 'open': 23193.75, 'high': 23193.75, 'low': 23167.0, 'close': 23168.75, 'timeframe': '5m', 'source_movements': 3, 'pd_array': {'type': 'swing_low', 'level': 23167.0}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23167.0}, 'meta': {'coverage': 3, 'period_minutes': 5, 'price_range': 26.75}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316025, price_delta_1m=0.0006913164176847381, price_delta_5m=0.0006913164176847381, price_delta_15m=0.0006913164176847381, volatility_window=0.002558262540343908, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23165.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.0009822864606383783, structural_importance=3.0, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23144.25, 'high': 23196.75, 'low': 23137.5, 'close': 23160.25, 'timeframe': '15m', 'source_movements': 15, 'pd_array': {'type': 'swing_low', 'level': 23137.5}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23165.0}, 'meta': {'coverage': 15, 'period_minutes': 15, 'price_range': 59.25}})",
    "RichNodeFeature(time_minutes=72.0, daily_phase_sin=0.838670567945424, daily_phase_cos=-0.5446390350150271, session_position=0.48322147651006714, time_to_close=77.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=0.0010585782645797551, price_delta_5m=0.0010585782645797551, price_delta_15m=0.0010585782645797551, volatility_window=0.0023091448610736446, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=0.8, event_type_id=1, timeframe_source=2, liquidity_type=0, fpfvg_gap_size=23186.25, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.001230105206366334, structural_importance=1.9200000000000004, raw_json={'id': 1, 'timestamp': '08:12:00', 'open': 23144.25, 'high': 23193.75, 'low': 23140.25, 'close': 23168.75, 'timeframe': '15m', 'source_movements': 8, 'pd_array': {'type': 'swing_low', 'level': 23140.25}, 'liquidity_sweep': True, 'fpfvg': {'gap': True, 'level': 23186.25}, 'meta': {'coverage': 8, 'period_minutes': 15, 'price_range': 53.5}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=0.0010585782645797551, price_delta_5m=0.0010585782645797551, price_delta_15m=0.0010585782645797551, volatility_window=0.002557323981656326, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=3, liquidity_type=0, fpfvg_gap_size=23165.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.001348799568384138, structural_importance=4.6, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23144.25, 'high': 23196.75, 'low': 23137.5, 'close': 23168.75, 'timeframe': '60m', 'source_movements': 23, 'pd_array': {'type': 'swing_low', 'level': 23137.5}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23165.0}, 'meta': {'coverage': 23, 'period_minutes': 60, 'price_range': 59.25}})",
    "RichNodeFeature(time_minutes=0.0, daily_phase_sin=0.9659258262890683, daily_phase_cos=-0.25881904510252085, session_position=0.0, time_to_close=149.0, weekend_proximity=0.42857142857142855, absolute_timestamp=1754434800, day_of_week=2, month_phase=0.1935483870967742, normalized_price=0.2316875, price_delta_1m=0.0010585782645797551, price_delta_5m=0.0010585782645797551, price_delta_15m=0.0010585782645797551, volatility_window=0.002557323981656326, energy_state=0.5, contamination_coefficient=0.0, fisher_regime=0, session_character=0, cross_tf_confluence=1.0, event_type_id=1, timeframe_source=4, liquidity_type=0, fpfvg_gap_size=23165.0, fpfvg_interaction_count=1, first_presentation_flag=0.0, pd_array_strength=0.001348799568384138, structural_importance=4.6, raw_json={'id': 0, 'timestamp': '07:00:00', 'open': 23144.25, 'high': 23196.75, 'low': 23137.5, 'close': 23168.75, 'timeframe': '1440m', 'source_movements': 23, 'pd_array': {'type': 'swing_low', 'level': 23137.5}, 'liquidity_sweep': False, 'fpfvg': {'gap': True, 'level': 23165.0}, 'meta': {'coverage': 23, 'period_minutes': 1440, 'price_range': 59.25}})"
  ],
  "edges": {
    "temporal": [
      {
        "source": 0,
        "target": 1,
        "feature_idx": 0
      },
      {
        "source": 1,
        "target": 2,
        "feature_idx": 1
      },
      {
        "source": 2,
        "target": 3,
        "feature_idx": 2
      },
      {
        "source": 3,
        "target": 4,
        "feature_idx": 3
      },
      {
        "source": 4,
        "target": 5,
        "feature_idx": 4
      },
      {
        "source": 5,
        "target": 6,
        "feature_idx": 5
      },
      {
        "source": 6,
        "target": 7,
        "feature_idx": 6
      },
      {
        "source": 7,
        "target": 8,
        "feature_idx": 7
      },
      {
        "source": 8,
        "target": 9,
        "feature_idx": 8
      },
      {
        "source": 9,
        "target": 10,
        "feature_idx": 9
      },
      {
        "source": 10,
        "target": 11,
        "feature_idx": 10
      },
      {
        "source": 11,
        "target": 12,
        "feature_idx": 11
      },
      {
        "source": 12,
        "target": 13,
        "feature_idx": 12
      },
      {
        "source": 13,
        "target": 14,
        "feature_idx": 13
      },
      {
        "source": 14,
        "target": 15,
        "feature_idx": 14
      },
      {
        "source": 15,
        "target": 16,
        "feature_idx": 15
      },
      {
        "source": 16,
        "target": 17,
        "feature_idx": 16
      },
      {
        "source": 17,
        "target": 18,
        "feature_idx": 17
      },
      {
        "source": 18,
        "target": 19,
        "feature_idx": 18
      },
      {
        "source": 19,
        "target": 20,
        "feature_idx": 19
      },
      {
        "source": 20,
        "target": 21,
        "feature_idx": 20
      },
      {
        "source": 21,
        "target": 22,
        "feature_idx": 21
      },
      {
        "source": 23,
        "target": 24,
        "feature_idx": 22
      },
      {
        "source": 24,
        "target": 25,
        "feature_idx": 23
      },
      {
        "source": 25,
        "target": 26,
        "feature_idx": 24
      },
      {
        "source": 26,
        "target": 27,
        "feature_idx": 25
      },
      {
        "source": 28,
        "target": 29,
        "feature_idx": 26
      }
    ],
    "scale": [
      {
        "source": 0,
        "target": 23,
        "feature_idx": 27
      },
      {
        "source": 0,
        "target": 24,
        "feature_idx": 28
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 29
      },
      {
        "source": 1,
        "target": 24,
        "feature_idx": 30
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 31
      },
      {
        "source": 2,
        "target": 24,
        "feature_idx": 32
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 33
      },
      {
        "source": 3,
        "target": 24,
        "feature_idx": 34
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 35
      },
      {
        "source": 4,
        "target": 24,
        "feature_idx": 36
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 37
      },
      {
        "source": 5,
        "target": 24,
        "feature_idx": 38
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 39
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 40
      },
      {
        "source": 6,
        "target": 24,
        "feature_idx": 41
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 42
      },
      {
        "source": 7,
        "target": 23,
        "feature_idx": 43
      },
      {
        "source": 7,
        "target": 24,
        "feature_idx": 44
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 45
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 46
      },
      {
        "source": 8,
        "target": 24,
        "feature_idx": 47
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 48
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 49
      },
      {
        "source": 9,
        "target": 24,
        "feature_idx": 50
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 51
      },
      {
        "source": 10,
        "target": 24,
        "feature_idx": 52
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 53
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 54
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 55
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 56
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 57
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 58
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 59
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 60
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 61
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 62
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 63
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 64
      },
      {
        "source": 20,
        "target": 27,
        "feature_idx": 65
      },
      {
        "source": 21,
        "target": 27,
        "feature_idx": 66
      },
      {
        "source": 22,
        "target": 27,
        "feature_idx": 67
      },
      {
        "source": 23,
        "target": 28,
        "feature_idx": 68
      },
      {
        "source": 24,
        "target": 28,
        "feature_idx": 69
      },
      {
        "source": 26,
        "target": 29,
        "feature_idx": 70
      },
      {
        "source": 28,
        "target": 30,
        "feature_idx": 71
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 72
      },
      {
        "source": 0,
        "target": 23,
        "feature_idx": 73,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 74,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 75,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 76,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 77,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 24,
        "feature_idx": 78,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23175.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23175.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 24,
        "feature_idx": 79,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23175.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23175.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 24,
        "feature_idx": 80,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_high",
            "level": 23175.75
          },
          "fpfvg": {
            "gap": true,
            "level": 23175.75
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 81,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 82,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 83,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23148.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23148.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 84,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23148.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23148.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 85,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 86,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 87,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 88,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23140.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23186.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 89,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23140.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23186.25
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 23,
        "feature_idx": 90,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 23,
        "feature_idx": 91,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 23,
        "feature_idx": 92,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 27,
        "feature_idx": 93,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 3,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23167.0
          },
          "fpfvg": {
            "gap": true,
            "level": 23167.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 23,
        "feature_idx": 94,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 23,
        "feature_idx": 95,
        "tf_source": "1m",
        "tf_target": "5m",
        "coverage": 5,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 0,
        "target": 28,
        "feature_idx": 96,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 28,
        "feature_idx": 97,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 28,
        "feature_idx": 98,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 28,
        "feature_idx": 99,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 28,
        "feature_idx": 100,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 28,
        "feature_idx": 101,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 28,
        "feature_idx": 102,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 28,
        "feature_idx": 103,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 28,
        "feature_idx": 104,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 28,
        "feature_idx": 105,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 28,
        "feature_idx": 106,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 28,
        "feature_idx": 107,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 28,
        "feature_idx": 108,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 28,
        "feature_idx": 109,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 28,
        "feature_idx": 110,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 29,
        "feature_idx": 111,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 8,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23140.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23186.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 16,
        "target": 29,
        "feature_idx": 112,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 8,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23140.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23186.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 17,
        "target": 29,
        "feature_idx": 113,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 8,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23140.25
          },
          "fpfvg": {
            "gap": true,
            "level": 23186.25
          },
          "liquidity_sweep": true
        }
      },
      {
        "source": 18,
        "target": 28,
        "feature_idx": 114,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 28,
        "feature_idx": 115,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 28,
        "feature_idx": 116,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 28,
        "feature_idx": 117,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 28,
        "feature_idx": 118,
        "tf_source": "1m",
        "tf_target": "15m",
        "coverage": 15,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 0,
        "target": 30,
        "feature_idx": 119,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 30,
        "feature_idx": 120,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 30,
        "feature_idx": 121,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 30,
        "feature_idx": 122,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 30,
        "feature_idx": 123,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 30,
        "feature_idx": 124,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 30,
        "feature_idx": 125,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 30,
        "feature_idx": 126,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 30,
        "feature_idx": 127,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 30,
        "feature_idx": 128,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 30,
        "feature_idx": 129,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 30,
        "feature_idx": 130,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 30,
        "feature_idx": 131,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 30,
        "feature_idx": 132,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 30,
        "feature_idx": 133,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 30,
        "feature_idx": 134,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 30,
        "feature_idx": 135,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 30,
        "feature_idx": 136,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 30,
        "feature_idx": 137,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 30,
        "feature_idx": 138,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 30,
        "feature_idx": 139,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 30,
        "feature_idx": 140,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 30,
        "feature_idx": 141,
        "tf_source": "1m",
        "tf_target": "1h",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 0,
        "target": 31,
        "feature_idx": 142,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 1,
        "target": 31,
        "feature_idx": 143,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 2,
        "target": 31,
        "feature_idx": 144,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 3,
        "target": 31,
        "feature_idx": 145,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 4,
        "target": 31,
        "feature_idx": 146,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 5,
        "target": 31,
        "feature_idx": 147,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 6,
        "target": 31,
        "feature_idx": 148,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 7,
        "target": 31,
        "feature_idx": 149,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 8,
        "target": 31,
        "feature_idx": 150,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 9,
        "target": 31,
        "feature_idx": 151,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 10,
        "target": 31,
        "feature_idx": 152,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 11,
        "target": 31,
        "feature_idx": 153,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 12,
        "target": 31,
        "feature_idx": 154,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 13,
        "target": 31,
        "feature_idx": 155,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 14,
        "target": 31,
        "feature_idx": 156,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 15,
        "target": 31,
        "feature_idx": 157,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 16,
        "target": 31,
        "feature_idx": 158,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 17,
        "target": 31,
        "feature_idx": 159,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 18,
        "target": 31,
        "feature_idx": 160,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 19,
        "target": 31,
        "feature_idx": 161,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 20,
        "target": 31,
        "feature_idx": 162,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 21,
        "target": 31,
        "feature_idx": 163,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      },
      {
        "source": 22,
        "target": 31,
        "feature_idx": 164,
        "tf_source": "1m",
        "tf_target": "D",
        "coverage": 23,
        "parent_metadata": {
          "pd_array": {
            "type": "swing_low",
            "level": 23137.5
          },
          "fpfvg": {
            "gap": true,
            "level": 23165.0
          },
          "liquidity_sweep": false
        }
      }
    ],
    "cascade": [],
    "pd_array": [],
    "cross_tf_confluence": [
      {
        "source": 0,
        "target": 23,
        "feature_idx": 165
      },
      {
        "source": 0,
        "target": 24,
        "feature_idx": 166
      },
      {
        "source": 0,
        "target": 25,
        "feature_idx": 167
      },
      {
        "source": 0,
        "target": 26,
        "feature_idx": 168
      },
      {
        "source": 0,
        "target": 27,
        "feature_idx": 169
      },
      {
        "source": 0,
        "target": 28,
        "feature_idx": 170
      },
      {
        "source": 0,
        "target": 29,
        "feature_idx": 171
      },
      {
        "source": 0,
        "target": 30,
        "feature_idx": 172
      },
      {
        "source": 0,
        "target": 31,
        "feature_idx": 173
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 174
      },
      {
        "source": 1,
        "target": 24,
        "feature_idx": 175
      },
      {
        "source": 1,
        "target": 25,
        "feature_idx": 176
      },
      {
        "source": 1,
        "target": 26,
        "feature_idx": 177
      },
      {
        "source": 1,
        "target": 27,
        "feature_idx": 178
      },
      {
        "source": 1,
        "target": 28,
        "feature_idx": 179
      },
      {
        "source": 1,
        "target": 29,
        "feature_idx": 180
      },
      {
        "source": 1,
        "target": 30,
        "feature_idx": 181
      },
      {
        "source": 1,
        "target": 31,
        "feature_idx": 182
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 183
      },
      {
        "source": 2,
        "target": 24,
        "feature_idx": 184
      },
      {
        "source": 2,
        "target": 25,
        "feature_idx": 185
      },
      {
        "source": 2,
        "target": 26,
        "feature_idx": 186
      },
      {
        "source": 2,
        "target": 27,
        "feature_idx": 187
      },
      {
        "source": 2,
        "target": 28,
        "feature_idx": 188
      },
      {
        "source": 2,
        "target": 29,
        "feature_idx": 189
      },
      {
        "source": 2,
        "target": 30,
        "feature_idx": 190
      },
      {
        "source": 2,
        "target": 31,
        "feature_idx": 191
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 192
      },
      {
        "source": 3,
        "target": 24,
        "feature_idx": 193
      },
      {
        "source": 3,
        "target": 25,
        "feature_idx": 194
      },
      {
        "source": 3,
        "target": 26,
        "feature_idx": 195
      },
      {
        "source": 3,
        "target": 27,
        "feature_idx": 196
      },
      {
        "source": 3,
        "target": 28,
        "feature_idx": 197
      },
      {
        "source": 3,
        "target": 29,
        "feature_idx": 198
      },
      {
        "source": 3,
        "target": 30,
        "feature_idx": 199
      },
      {
        "source": 3,
        "target": 31,
        "feature_idx": 200
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 201
      },
      {
        "source": 4,
        "target": 24,
        "feature_idx": 202
      },
      {
        "source": 4,
        "target": 25,
        "feature_idx": 203
      },
      {
        "source": 4,
        "target": 26,
        "feature_idx": 204
      },
      {
        "source": 4,
        "target": 27,
        "feature_idx": 205
      },
      {
        "source": 4,
        "target": 28,
        "feature_idx": 206
      },
      {
        "source": 4,
        "target": 29,
        "feature_idx": 207
      },
      {
        "source": 4,
        "target": 30,
        "feature_idx": 208
      },
      {
        "source": 4,
        "target": 31,
        "feature_idx": 209
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 210
      },
      {
        "source": 5,
        "target": 24,
        "feature_idx": 211
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 212
      },
      {
        "source": 5,
        "target": 26,
        "feature_idx": 213
      },
      {
        "source": 5,
        "target": 27,
        "feature_idx": 214
      },
      {
        "source": 5,
        "target": 28,
        "feature_idx": 215
      },
      {
        "source": 5,
        "target": 29,
        "feature_idx": 216
      },
      {
        "source": 5,
        "target": 30,
        "feature_idx": 217
      },
      {
        "source": 5,
        "target": 31,
        "feature_idx": 218
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 219
      },
      {
        "source": 6,
        "target": 24,
        "feature_idx": 220
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 221
      },
      {
        "source": 6,
        "target": 26,
        "feature_idx": 222
      },
      {
        "source": 6,
        "target": 27,
        "feature_idx": 223
      },
      {
        "source": 6,
        "target": 28,
        "feature_idx": 224
      },
      {
        "source": 6,
        "target": 29,
        "feature_idx": 225
      },
      {
        "source": 6,
        "target": 30,
        "feature_idx": 226
      },
      {
        "source": 6,
        "target": 31,
        "feature_idx": 227
      },
      {
        "source": 7,
        "target": 23,
        "feature_idx": 228
      },
      {
        "source": 7,
        "target": 24,
        "feature_idx": 229
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 230
      },
      {
        "source": 7,
        "target": 26,
        "feature_idx": 231
      },
      {
        "source": 7,
        "target": 27,
        "feature_idx": 232
      },
      {
        "source": 7,
        "target": 28,
        "feature_idx": 233
      },
      {
        "source": 7,
        "target": 29,
        "feature_idx": 234
      },
      {
        "source": 7,
        "target": 30,
        "feature_idx": 235
      },
      {
        "source": 7,
        "target": 31,
        "feature_idx": 236
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 237
      },
      {
        "source": 8,
        "target": 24,
        "feature_idx": 238
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 239
      },
      {
        "source": 8,
        "target": 26,
        "feature_idx": 240
      },
      {
        "source": 8,
        "target": 27,
        "feature_idx": 241
      },
      {
        "source": 8,
        "target": 28,
        "feature_idx": 242
      },
      {
        "source": 8,
        "target": 29,
        "feature_idx": 243
      },
      {
        "source": 8,
        "target": 30,
        "feature_idx": 244
      },
      {
        "source": 8,
        "target": 31,
        "feature_idx": 245
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 246
      },
      {
        "source": 9,
        "target": 24,
        "feature_idx": 247
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 248
      },
      {
        "source": 9,
        "target": 26,
        "feature_idx": 249
      },
      {
        "source": 9,
        "target": 27,
        "feature_idx": 250
      },
      {
        "source": 9,
        "target": 28,
        "feature_idx": 251
      },
      {
        "source": 9,
        "target": 29,
        "feature_idx": 252
      },
      {
        "source": 9,
        "target": 30,
        "feature_idx": 253
      },
      {
        "source": 9,
        "target": 31,
        "feature_idx": 254
      },
      {
        "source": 10,
        "target": 23,
        "feature_idx": 255
      },
      {
        "source": 10,
        "target": 24,
        "feature_idx": 256
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 257
      },
      {
        "source": 10,
        "target": 26,
        "feature_idx": 258
      },
      {
        "source": 10,
        "target": 27,
        "feature_idx": 259
      },
      {
        "source": 10,
        "target": 28,
        "feature_idx": 260
      },
      {
        "source": 10,
        "target": 29,
        "feature_idx": 261
      },
      {
        "source": 10,
        "target": 30,
        "feature_idx": 262
      },
      {
        "source": 10,
        "target": 31,
        "feature_idx": 263
      },
      {
        "source": 11,
        "target": 23,
        "feature_idx": 264
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 265
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 266
      },
      {
        "source": 11,
        "target": 26,
        "feature_idx": 267
      },
      {
        "source": 11,
        "target": 27,
        "feature_idx": 268
      },
      {
        "source": 11,
        "target": 28,
        "feature_idx": 269
      },
      {
        "source": 11,
        "target": 29,
        "feature_idx": 270
      },
      {
        "source": 11,
        "target": 30,
        "feature_idx": 271
      },
      {
        "source": 11,
        "target": 31,
        "feature_idx": 272
      },
      {
        "source": 12,
        "target": 23,
        "feature_idx": 273
      },
      {
        "source": 12,
        "target": 24,
        "feature_idx": 274
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 275
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 276
      },
      {
        "source": 12,
        "target": 27,
        "feature_idx": 277
      },
      {
        "source": 12,
        "target": 28,
        "feature_idx": 278
      },
      {
        "source": 12,
        "target": 29,
        "feature_idx": 279
      },
      {
        "source": 12,
        "target": 30,
        "feature_idx": 280
      },
      {
        "source": 12,
        "target": 31,
        "feature_idx": 281
      },
      {
        "source": 13,
        "target": 23,
        "feature_idx": 282
      },
      {
        "source": 13,
        "target": 24,
        "feature_idx": 283
      },
      {
        "source": 13,
        "target": 25,
        "feature_idx": 284
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 285
      },
      {
        "source": 13,
        "target": 27,
        "feature_idx": 286
      },
      {
        "source": 13,
        "target": 28,
        "feature_idx": 287
      },
      {
        "source": 13,
        "target": 29,
        "feature_idx": 288
      },
      {
        "source": 13,
        "target": 30,
        "feature_idx": 289
      },
      {
        "source": 13,
        "target": 31,
        "feature_idx": 290
      },
      {
        "source": 14,
        "target": 23,
        "feature_idx": 291
      },
      {
        "source": 14,
        "target": 24,
        "feature_idx": 292
      },
      {
        "source": 14,
        "target": 25,
        "feature_idx": 293
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 294
      },
      {
        "source": 14,
        "target": 27,
        "feature_idx": 295
      },
      {
        "source": 14,
        "target": 28,
        "feature_idx": 296
      },
      {
        "source": 14,
        "target": 29,
        "feature_idx": 297
      },
      {
        "source": 14,
        "target": 30,
        "feature_idx": 298
      },
      {
        "source": 14,
        "target": 31,
        "feature_idx": 299
      },
      {
        "source": 15,
        "target": 23,
        "feature_idx": 300
      },
      {
        "source": 15,
        "target": 24,
        "feature_idx": 301
      },
      {
        "source": 15,
        "target": 25,
        "feature_idx": 302
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 303
      },
      {
        "source": 15,
        "target": 27,
        "feature_idx": 304
      },
      {
        "source": 15,
        "target": 28,
        "feature_idx": 305
      },
      {
        "source": 15,
        "target": 29,
        "feature_idx": 306
      },
      {
        "source": 15,
        "target": 30,
        "feature_idx": 307
      },
      {
        "source": 15,
        "target": 31,
        "feature_idx": 308
      },
      {
        "source": 16,
        "target": 23,
        "feature_idx": 309
      },
      {
        "source": 16,
        "target": 24,
        "feature_idx": 310
      },
      {
        "source": 16,
        "target": 25,
        "feature_idx": 311
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 312
      },
      {
        "source": 16,
        "target": 27,
        "feature_idx": 313
      },
      {
        "source": 16,
        "target": 28,
        "feature_idx": 314
      },
      {
        "source": 16,
        "target": 29,
        "feature_idx": 315
      },
      {
        "source": 16,
        "target": 30,
        "feature_idx": 316
      },
      {
        "source": 16,
        "target": 31,
        "feature_idx": 317
      },
      {
        "source": 17,
        "target": 23,
        "feature_idx": 318
      },
      {
        "source": 17,
        "target": 24,
        "feature_idx": 319
      },
      {
        "source": 17,
        "target": 25,
        "feature_idx": 320
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 321
      },
      {
        "source": 17,
        "target": 27,
        "feature_idx": 322
      },
      {
        "source": 17,
        "target": 28,
        "feature_idx": 323
      },
      {
        "source": 17,
        "target": 29,
        "feature_idx": 324
      },
      {
        "source": 17,
        "target": 30,
        "feature_idx": 325
      },
      {
        "source": 17,
        "target": 31,
        "feature_idx": 326
      },
      {
        "source": 18,
        "target": 23,
        "feature_idx": 327
      },
      {
        "source": 18,
        "target": 24,
        "feature_idx": 328
      },
      {
        "source": 18,
        "target": 25,
        "feature_idx": 329
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 330
      },
      {
        "source": 18,
        "target": 27,
        "feature_idx": 331
      },
      {
        "source": 18,
        "target": 28,
        "feature_idx": 332
      },
      {
        "source": 18,
        "target": 29,
        "feature_idx": 333
      },
      {
        "source": 18,
        "target": 30,
        "feature_idx": 334
      },
      {
        "source": 18,
        "target": 31,
        "feature_idx": 335
      },
      {
        "source": 19,
        "target": 23,
        "feature_idx": 336
      },
      {
        "source": 19,
        "target": 24,
        "feature_idx": 337
      },
      {
        "source": 19,
        "target": 25,
        "feature_idx": 338
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 339
      },
      {
        "source": 19,
        "target": 27,
        "feature_idx": 340
      },
      {
        "source": 19,
        "target": 28,
        "feature_idx": 341
      },
      {
        "source": 19,
        "target": 29,
        "feature_idx": 342
      },
      {
        "source": 19,
        "target": 30,
        "feature_idx": 343
      },
      {
        "source": 19,
        "target": 31,
        "feature_idx": 344
      },
      {
        "source": 20,
        "target": 23,
        "feature_idx": 345
      },
      {
        "source": 20,
        "target": 24,
        "feature_idx": 346
      },
      {
        "source": 20,
        "target": 25,
        "feature_idx": 347
      },
      {
        "source": 20,
        "target": 26,
        "feature_idx": 348
      },
      {
        "source": 20,
        "target": 27,
        "feature_idx": 349
      },
      {
        "source": 20,
        "target": 28,
        "feature_idx": 350
      },
      {
        "source": 20,
        "target": 29,
        "feature_idx": 351
      },
      {
        "source": 20,
        "target": 30,
        "feature_idx": 352
      },
      {
        "source": 20,
        "target": 31,
        "feature_idx": 353
      },
      {
        "source": 21,
        "target": 23,
        "feature_idx": 354
      },
      {
        "source": 21,
        "target": 24,
        "feature_idx": 355
      },
      {
        "source": 21,
        "target": 25,
        "feature_idx": 356
      },
      {
        "source": 21,
        "target": 26,
        "feature_idx": 357
      },
      {
        "source": 21,
        "target": 27,
        "feature_idx": 358
      },
      {
        "source": 21,
        "target": 28,
        "feature_idx": 359
      },
      {
        "source": 21,
        "target": 29,
        "feature_idx": 360
      },
      {
        "source": 21,
        "target": 30,
        "feature_idx": 361
      },
      {
        "source": 21,
        "target": 31,
        "feature_idx": 362
      },
      {
        "source": 22,
        "target": 23,
        "feature_idx": 363
      },
      {
        "source": 22,
        "target": 24,
        "feature_idx": 364
      },
      {
        "source": 22,
        "target": 25,
        "feature_idx": 365
      },
      {
        "source": 22,
        "target": 26,
        "feature_idx": 366
      },
      {
        "source": 22,
        "target": 27,
        "feature_idx": 367
      },
      {
        "source": 22,
        "target": 28,
        "feature_idx": 368
      },
      {
        "source": 22,
        "target": 29,
        "feature_idx": 369
      },
      {
        "source": 22,
        "target": 30,
        "feature_idx": 370
      },
      {
        "source": 22,
        "target": 31,
        "feature_idx": 371
      },
      {
        "source": 23,
        "target": 28,
        "feature_idx": 372
      },
      {
        "source": 23,
        "target": 29,
        "feature_idx": 373
      },
      {
        "source": 23,
        "target": 30,
        "feature_idx": 374
      },
      {
        "source": 23,
        "target": 31,
        "feature_idx": 375
      },
      {
        "source": 24,
        "target": 28,
        "feature_idx": 376
      },
      {
        "source": 24,
        "target": 29,
        "feature_idx": 377
      },
      {
        "source": 24,
        "target": 30,
        "feature_idx": 378
      },
      {
        "source": 24,
        "target": 31,
        "feature_idx": 379
      },
      {
        "source": 25,
        "target": 28,
        "feature_idx": 380
      },
      {
        "source": 25,
        "target": 29,
        "feature_idx": 381
      },
      {
        "source": 25,
        "target": 30,
        "feature_idx": 382
      },
      {
        "source": 25,
        "target": 31,
        "feature_idx": 383
      },
      {
        "source": 26,
        "target": 28,
        "feature_idx": 384
      },
      {
        "source": 26,
        "target": 29,
        "feature_idx": 385
      },
      {
        "source": 26,
        "target": 30,
        "feature_idx": 386
      },
      {
        "source": 26,
        "target": 31,
        "feature_idx": 387
      },
      {
        "source": 27,
        "target": 28,
        "feature_idx": 388
      },
      {
        "source": 27,
        "target": 29,
        "feature_idx": 389
      },
      {
        "source": 27,
        "target": 30,
        "feature_idx": 390
      },
      {
        "source": 27,
        "target": 31,
        "feature_idx": 391
      },
      {
        "source": 28,
        "target": 30,
        "feature_idx": 392
      },
      {
        "source": 28,
        "target": 31,
        "feature_idx": 393
      },
      {
        "source": 29,
        "target": 30,
        "feature_idx": 394
      },
      {
        "source": 29,
        "target": 31,
        "feature_idx": 395
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 396
      }
    ],
    "temporal_echo": [
      {
        "source": 0,
        "target": 23,
        "feature_idx": 397
      },
      {
        "source": 0,
        "target": 24,
        "feature_idx": 398
      },
      {
        "source": 0,
        "target": 28,
        "feature_idx": 399
      },
      {
        "source": 0,
        "target": 30,
        "feature_idx": 400
      },
      {
        "source": 0,
        "target": 31,
        "feature_idx": 401
      },
      {
        "source": 1,
        "target": 23,
        "feature_idx": 402
      },
      {
        "source": 1,
        "target": 24,
        "feature_idx": 403
      },
      {
        "source": 1,
        "target": 28,
        "feature_idx": 404
      },
      {
        "source": 1,
        "target": 30,
        "feature_idx": 405
      },
      {
        "source": 1,
        "target": 31,
        "feature_idx": 406
      },
      {
        "source": 2,
        "target": 23,
        "feature_idx": 407
      },
      {
        "source": 2,
        "target": 24,
        "feature_idx": 408
      },
      {
        "source": 2,
        "target": 28,
        "feature_idx": 409
      },
      {
        "source": 2,
        "target": 30,
        "feature_idx": 410
      },
      {
        "source": 2,
        "target": 31,
        "feature_idx": 411
      },
      {
        "source": 3,
        "target": 23,
        "feature_idx": 412
      },
      {
        "source": 3,
        "target": 24,
        "feature_idx": 413
      },
      {
        "source": 3,
        "target": 28,
        "feature_idx": 414
      },
      {
        "source": 3,
        "target": 30,
        "feature_idx": 415
      },
      {
        "source": 3,
        "target": 31,
        "feature_idx": 416
      },
      {
        "source": 4,
        "target": 23,
        "feature_idx": 417
      },
      {
        "source": 4,
        "target": 24,
        "feature_idx": 418
      },
      {
        "source": 4,
        "target": 28,
        "feature_idx": 419
      },
      {
        "source": 4,
        "target": 30,
        "feature_idx": 420
      },
      {
        "source": 4,
        "target": 31,
        "feature_idx": 421
      },
      {
        "source": 5,
        "target": 23,
        "feature_idx": 422
      },
      {
        "source": 5,
        "target": 24,
        "feature_idx": 423
      },
      {
        "source": 5,
        "target": 25,
        "feature_idx": 424
      },
      {
        "source": 5,
        "target": 28,
        "feature_idx": 425
      },
      {
        "source": 5,
        "target": 30,
        "feature_idx": 426
      },
      {
        "source": 5,
        "target": 31,
        "feature_idx": 427
      },
      {
        "source": 6,
        "target": 23,
        "feature_idx": 428
      },
      {
        "source": 6,
        "target": 24,
        "feature_idx": 429
      },
      {
        "source": 6,
        "target": 25,
        "feature_idx": 430
      },
      {
        "source": 6,
        "target": 28,
        "feature_idx": 431
      },
      {
        "source": 6,
        "target": 30,
        "feature_idx": 432
      },
      {
        "source": 6,
        "target": 31,
        "feature_idx": 433
      },
      {
        "source": 7,
        "target": 23,
        "feature_idx": 434
      },
      {
        "source": 7,
        "target": 24,
        "feature_idx": 435
      },
      {
        "source": 7,
        "target": 25,
        "feature_idx": 436
      },
      {
        "source": 7,
        "target": 28,
        "feature_idx": 437
      },
      {
        "source": 7,
        "target": 30,
        "feature_idx": 438
      },
      {
        "source": 7,
        "target": 31,
        "feature_idx": 439
      },
      {
        "source": 8,
        "target": 23,
        "feature_idx": 440
      },
      {
        "source": 8,
        "target": 24,
        "feature_idx": 441
      },
      {
        "source": 8,
        "target": 25,
        "feature_idx": 442
      },
      {
        "source": 8,
        "target": 28,
        "feature_idx": 443
      },
      {
        "source": 8,
        "target": 30,
        "feature_idx": 444
      },
      {
        "source": 8,
        "target": 31,
        "feature_idx": 445
      },
      {
        "source": 9,
        "target": 23,
        "feature_idx": 446
      },
      {
        "source": 9,
        "target": 24,
        "feature_idx": 447
      },
      {
        "source": 9,
        "target": 25,
        "feature_idx": 448
      },
      {
        "source": 9,
        "target": 28,
        "feature_idx": 449
      },
      {
        "source": 9,
        "target": 30,
        "feature_idx": 450
      },
      {
        "source": 9,
        "target": 31,
        "feature_idx": 451
      },
      {
        "source": 10,
        "target": 24,
        "feature_idx": 452
      },
      {
        "source": 10,
        "target": 25,
        "feature_idx": 453
      },
      {
        "source": 11,
        "target": 24,
        "feature_idx": 454
      },
      {
        "source": 11,
        "target": 25,
        "feature_idx": 455
      },
      {
        "source": 12,
        "target": 25,
        "feature_idx": 456
      },
      {
        "source": 12,
        "target": 26,
        "feature_idx": 457
      },
      {
        "source": 12,
        "target": 29,
        "feature_idx": 458
      },
      {
        "source": 13,
        "target": 26,
        "feature_idx": 459
      },
      {
        "source": 13,
        "target": 29,
        "feature_idx": 460
      },
      {
        "source": 14,
        "target": 26,
        "feature_idx": 461
      },
      {
        "source": 14,
        "target": 29,
        "feature_idx": 462
      },
      {
        "source": 15,
        "target": 26,
        "feature_idx": 463
      },
      {
        "source": 15,
        "target": 29,
        "feature_idx": 464
      },
      {
        "source": 16,
        "target": 26,
        "feature_idx": 465
      },
      {
        "source": 16,
        "target": 29,
        "feature_idx": 466
      },
      {
        "source": 17,
        "target": 26,
        "feature_idx": 467
      },
      {
        "source": 17,
        "target": 29,
        "feature_idx": 468
      },
      {
        "source": 18,
        "target": 26,
        "feature_idx": 469
      },
      {
        "source": 18,
        "target": 29,
        "feature_idx": 470
      },
      {
        "source": 19,
        "target": 26,
        "feature_idx": 471
      },
      {
        "source": 19,
        "target": 29,
        "feature_idx": 472
      },
      {
        "source": 20,
        "target": 27,
        "feature_idx": 473
      },
      {
        "source": 21,
        "target": 27,
        "feature_idx": 474
      },
      {
        "source": 22,
        "target": 27,
        "feature_idx": 475
      },
      {
        "source": 23,
        "target": 28,
        "feature_idx": 476
      },
      {
        "source": 23,
        "target": 30,
        "feature_idx": 477
      },
      {
        "source": 23,
        "target": 31,
        "feature_idx": 478
      },
      {
        "source": 24,
        "target": 28,
        "feature_idx": 479
      },
      {
        "source": 24,
        "target": 30,
        "feature_idx": 480
      },
      {
        "source": 24,
        "target": 31,
        "feature_idx": 481
      },
      {
        "source": 26,
        "target": 29,
        "feature_idx": 482
      },
      {
        "source": 28,
        "target": 30,
        "feature_idx": 483
      },
      {
        "source": 28,
        "target": 31,
        "feature_idx": 484
      },
      {
        "source": 30,
        "target": 31,
        "feature_idx": 485
      }
    ],
    "discovered": []
  },
  "rich_edge_features": [
    