#!/usr/bin/env python3
"""
IRONFORGE Lazy Loading Container
================================

Integrates IRONPULSE lazy loading system with IRONFORGE components
to resolve Sprint 2 timeout issues and achieve 88.7% performance improvement.

Based on IRONPULSE container architecture with IRONFORGE-specific adaptations:
- Enhanced graph builder lazy loading
- TGAT discovery lazy initialization  
- Sprint 2 components (regime segmentation, precursor detection)
- Performance monitoring integration
"""

import sys
import os
import time
import logging
from typing import Dict, Any, Optional, Callable
from pathlib import Path

# Clean imports using properly installed iron_core package
from iron_core.performance.lazy_loader import LazyComponent, LazyLoadingManager
from iron_core.performance.container import IRONContainer

class IRONFORGEContainer:
    """
    IRONFORGE-specific dependency injection container with lazy loading.
    
    Resolves Sprint 2 timeout issues by implementing lazy loading for:
    - Enhanced graph builder (37D features)  
    - TGAT discovery engine (sophisticated temporal attention)
    - Regime segmentation (DBSCAN clustering)
    - Precursor detection (temporal cycles + structural context)
    - Performance monitoring system
    - Analyst reporting layer
    """
    
    def __init__(self):
        # Initialize base IRON-CORE container
        self._base_container = IRONContainer()
        
        # IRONFORGE-specific component registry
        self._ironforge_components: Dict[str, LazyComponent] = {}
        self._performance_metrics: Dict[str, float] = {}
        self._initialization_times: Dict[str, float] = {}
        
        # Set up logging
        self.logger = logging.getLogger('ironforge.container')
        self.logger.setLevel(logging.INFO)
        
        # Register IRONFORGE components for lazy loading
        self._register_ironforge_components()
        
    def _register_ironforge_components(self):
        """Register all IRONFORGE components for lazy loading"""
        
        # Core graph building components
        self._register_component(
            'enhanced_graph_builder',
            'learning.enhanced_graph_builder',
            'EnhancedGraphBuilder',
            'Enhanced graph builder with 37D temporal cycle features'
        )
        
        # TGAT discovery engine
        self._register_component(
            'tgat_discovery',
            'learning.tgat_discovery', 
            'IRONFORGEDiscovery',
            'TGAT discovery engine with 4 edge types and regime integration'
        )
        
        # Sprint 2 structural intelligence components
        self._register_component(
            'regime_segmentation',
            'learning.regime_segmentation',
            'RegimeSegmentation', 
            'DBSCAN-based regime clustering for pattern analysis'
        )
        
        self._register_component(
            'precursor_detection',
            'learning.precursor_detection',
            'EventPrecursorDetector',
            'Temporal cycles + structural context precursor detection'
        )
        
        # Performance monitoring system
        self._register_component(
            'performance_monitor',
            'performance_monitor',
            'PerformanceMonitor',
            'Sprint 2 performance monitoring with 15% regression threshold'
        )
        
        # Analyst reporting layer  
        self._register_component(
            'analyst_reports',
            'reporting.analyst_reports',
            'AnalystReports',
            'Comprehensive Sprint 2 analyst reporting and visibility'
        )
        
        self.logger.info(f"Registered {len(self._ironforge_components)} IRONFORGE components for lazy loading")
        
    def _register_component(self, name: str, module_path: str, class_name: str, 
                          description: str):
        """Register a single IRONFORGE component for lazy loading"""
                
        # Create lazy component wrapper
        lazy_component = LazyComponent(
            module_path=module_path,
            class_name=class_name,
            cache_enabled=True
        )
        
        self._ironforge_components[name] = lazy_component
        
    def get_component(self, name: str) -> Any:
        """Get IRONFORGE component with lazy loading"""
        if name not in self._ironforge_components:
            raise ValueError(f"Unknown IRONFORGE component: {name}")
            
        # LazyComponent creates instance on call
        lazy_component = self._ironforge_components[name]
        
        # Handle special initialization for components that need parameters
        if name == 'tgat_discovery':
            return lazy_component(node_features=37)  # 37D features
        else:
            return lazy_component()
        
    def get_enhanced_graph_builder(self):
        """Get enhanced graph builder with lazy loading"""
        return self.get_component('enhanced_graph_builder')
        
    def get_tgat_discovery(self):
        """Get TGAT discovery engine with lazy loading"""  
        return self.get_component('tgat_discovery')
        
    def get_regime_segmentation(self):
        """Get regime segmentation with lazy loading"""
        return self.get_component('regime_segmentation')
        
    def get_precursor_detection(self):
        """Get precursor detection with lazy loading"""
        return self.get_component('precursor_detection')
        
    def get_performance_monitor(self):
        """Get performance monitor with lazy loading"""
        return self.get_component('performance_monitor')
        
    def get_analyst_reports(self):
        """Get analyst reports with lazy loading"""
        return self.get_component('analyst_reports')
        
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get container performance metrics"""
        total_components = len(self._ironforge_components)
        loaded_components = len(self._initialization_times)
        
        total_init_time = sum(self._initialization_times.values()) if self._initialization_times else 0
        avg_init_time = total_init_time / max(1, loaded_components)
        
        return {
            'total_components': total_components,
            'loaded_components': loaded_components,
            'total_initialization_time': total_init_time,
            'average_component_load_time': avg_init_time,
            'component_load_times': self._initialization_times.copy(),
            'performance_sla_met': avg_init_time < 5.0,  # <5 second target
            'lazy_loading_active': True
        }
        
    def preload_critical_components(self):
        """Preload critical components for immediate use"""
        critical_components = [
            'enhanced_graph_builder',
            'tgat_discovery'
        ]
        
        self.logger.info("Preloading critical IRONFORGE components...")
        
        for component_name in critical_components:
            try:
                start_time = time.time()
                self.get_component(component_name)
                load_time = time.time() - start_time
                self.logger.info(f"✅ Preloaded {component_name} in {load_time:.3f}s")
            except Exception as e:
                self.logger.error(f"❌ Failed to preload {component_name}: {e}")
                
    def cleanup(self):
        """Clean up container resources"""
        for component in self._ironforge_components.values():
            if hasattr(component, 'cleanup'):
                component.cleanup()
                
        self.logger.info("IRONFORGE container cleanup completed")


# Global container instance for IRONFORGE
_ironforge_container: Optional[IRONFORGEContainer] = None

def get_ironforge_container() -> IRONFORGEContainer:
    """Get or create the global IRONFORGE container"""
    global _ironforge_container
    
    if _ironforge_container is None:
        _ironforge_container = IRONFORGEContainer()
        
    return _ironforge_container

def initialize_ironforge_lazy_loading():
    """Initialize IRONFORGE lazy loading system"""
    container = get_ironforge_container()
    
    print("🚀 IRONFORGE Lazy Loading System Initialized")
    print("=" * 50)
    
    metrics = container.get_performance_metrics()
    print(f"📊 Components registered: {metrics['total_components']}")
    print(f"⚡ Lazy loading active: {metrics['lazy_loading_active']}")
    print(f"🎯 Performance SLA: <5 second component load time")
    
    return container

if __name__ == "__main__":
    # Test IRONFORGE container initialization
    container = initialize_ironforge_lazy_loading()
    
    # Test lazy loading
    print(f"\n🧪 Testing lazy component loading...")
    
    start_time = time.time()
    builder = container.get_enhanced_graph_builder()
    load_time = time.time() - start_time
    print(f"✅ Enhanced graph builder loaded in {load_time:.3f}s")
    
    # Show performance metrics
    metrics = container.get_performance_metrics()
    print(f"\n📊 Performance Metrics:")
    print(f"   Components loaded: {metrics['loaded_components']}/{metrics['total_components']}")
    print(f"   Average load time: {metrics['average_component_load_time']:.3f}s")
    print(f"   SLA met: {metrics['performance_sla_met']}")