2025-08-14 16:59:04,259 - <PERSON>RONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:04,259 - iron_core.lazy_manager.singleton - INFO - Initializing lazy loading manager singleton
2025-08-14 16:59:04,260 - iron_core.lazy_manager - INFO - Standard mathematical components registered for lazy loading
2025-08-14 16:59:04,260 - ironforge.container - INFO - Registered 6 IRONFORGE components for lazy loading
2025-08-14 16:59:05,087 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:05,168 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,187 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,201 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,214 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,240 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,259 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,269 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:05,273 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:05,297 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,320 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,343 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,361 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:05,367 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:05,368 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 16:59:05,370 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 16:59:05,370 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,388 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,389 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,389 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,402 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,403 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,403 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,415 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,416 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,416 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,438 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,438 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,438 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 16:59:05,451 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,452 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 16:59:05,452 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,468 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,469 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,469 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,480 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,481 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,481 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,490 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREASIA_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,490 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,491 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,505 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,505 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,505 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,525 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYPM_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,526 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,526 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,539 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,539 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,539 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,557 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,557 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,558 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,576 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,577 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 16:59:05,577 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,594 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,595 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,596 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,613 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,613 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,613 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,631 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,632 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,632 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,646 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,647 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,647 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,657 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,657 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,657 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,669 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,670 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,670 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 16:59:05,681 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,682 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 16:59:05,682 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,691 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,691 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,691 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,703 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,704 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,704 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,715 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,716 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,716 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,726 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,727 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,727 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 16:59:05,738 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,739 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 16:59:05,739 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,760 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,760 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,760 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 16:59:05,771 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,774 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 16:59:05,774 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 16:59:05,794 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,794 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 16:59:05,794 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 16:59:05,807 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,807 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 16:59:05,807 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,818 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,818 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,819 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,843 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,844 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 16:59:05,844 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,855 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,856 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,856 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,867 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,867 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,868 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,879 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,880 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 16:59:05,880 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,894 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,895 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,895 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,905 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,906 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 16:59:05,906 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,918 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,919 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,919 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,930 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,931 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 16:59:05,931 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,951 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,952 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 16:59:05,952 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,966 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,966 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 16:59:05,966 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 16:59:05,997 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:05,998 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 16:59:05,998 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,017 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,018 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,018 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,028 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,029 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,029 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,049 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYAM_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,050 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,050 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,061 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,062 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,062 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,086 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,086 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,087 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,098 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,098 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,098 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,110 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,110 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,110 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 16:59:06,130 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,131 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 16:59:06,131 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,142 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,142 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,142 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,153 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,153 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,153 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,164 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,165 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,165 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,175 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,175 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,175 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 16:59:06,193 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,194 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 16:59:06,194 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,206 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,207 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,207 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,218 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,219 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,219 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,238 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,239 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,240 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 0 patterns from 0 sessions in 0.9s
2025-08-14 16:59:06,240 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_165906.json
2025-08-14 16:59:06,263 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,264 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:06,270 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:06,293 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,295 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:06,299 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:06,299 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 16:59:06,299 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 16:59:06,299 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,318 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,319 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,319 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,332 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,332 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,332 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,357 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,357 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,357 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,378 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,378 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,378 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 16:59:06,391 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,391 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 16:59:06,391 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,405 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,406 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,406 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,417 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,418 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,418 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,429 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREASIA_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,429 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,429 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,450 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,451 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,451 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,470 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYPM_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,471 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,471 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,482 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,482 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,483 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,500 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,501 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,501 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,518 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,519 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 16:59:06,519 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,530 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,530 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,530 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,541 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,542 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,542 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,570 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,571 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,571 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,586 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,587 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,587 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,598 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,598 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,599 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,610 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,610 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,610 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,620 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,620 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,620 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,631 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,632 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,632 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,643 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,643 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,643 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,655 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,656 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,656 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,668 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,668 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,668 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,685 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,686 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,686 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,700 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,700 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,700 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,719 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,719 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 16:59:06,719 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,742 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,743 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,743 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,755 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,755 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,755 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,767 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,767 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,768 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,780 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,780 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 16:59:06,780 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,796 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,797 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,797 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,809 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,809 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,810 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,825 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,826 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 16:59:06,826 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,840 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,841 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,841 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,852 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,852 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,852 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,864 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,865 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,865 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,887 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,888 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 16:59:06,888 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,904 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,905 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 16:59:06,905 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,916 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,916 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 16:59:06,916 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 16:59:06,945 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,946 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 16:59:06,946 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,967 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,968 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 16:59:06,968 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,979 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:06,980 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 16:59:06,980 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,000 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYAM_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,001 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,001 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,012 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,012 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,012 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,026 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,027 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,027 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,043 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,044 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,044 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,057 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,058 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,058 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 16:59:07,081 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,082 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 16:59:07,082 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,092 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,092 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,093 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,103 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,103 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,103 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,113 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,113 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,113 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,123 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,123 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,123 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 16:59:07,137 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,138 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 16:59:07,138 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,151 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,151 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,151 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,167 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,167 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,168 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,193 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,193 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,194 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 0 patterns from 0 sessions in 0.9s
2025-08-14 16:59:07,194 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_165907.json
2025-08-14 16:59:07,196 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:07,199 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:07,200 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:07,204 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:07,224 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,225 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:07,229 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:07,229 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 16:59:07,230 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 16:59:07,230 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,248 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,249 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,249 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,261 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,262 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,262 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,284 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,285 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,285 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,311 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,312 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,312 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 16:59:07,324 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,325 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 16:59:07,325 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,339 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,339 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,339 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,350 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,351 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,351 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,360 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREASIA_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,360 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,361 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,373 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,373 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,373 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,391 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYPM_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,392 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,392 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,415 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,415 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,415 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,432 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,433 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,433 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,451 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,452 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,452 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,461 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,462 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,462 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,475 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,475 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,475 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,493 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,494 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,494 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,510 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,511 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,511 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,531 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,532 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,532 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,543 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,543 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,543 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,554 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,554 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,554 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,566 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,566 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,566 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,579 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,579 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,580 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,592 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,592 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,592 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,603 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,604 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,604 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,614 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,615 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,615 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,631 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,631 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,631 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,642 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,642 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 16:59:07,642 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,660 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,661 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,661 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,675 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,675 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,675 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,687 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,687 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,687 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,698 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_31.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,699 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 16:59:07,699 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,709 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,710 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,710 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,720 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,721 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,721 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,734 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_07_30.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,734 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 16:59:07,734 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,750 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,750 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,750 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,770 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,771 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,771 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,785 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,786 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,786 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,797 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,797 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 16:59:07,798 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,810 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,810 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,810 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,820 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,820 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,821 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 16:59:07,841 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,842 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 16:59:07,842 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,861 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,861 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 16:59:07,862 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,874 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,875 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,875 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,901 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYAM_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,902 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,902 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,913 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_28.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,914 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 16:59:07,914 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,938 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,939 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 16:59:07,939 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,951 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,952 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 16:59:07,952 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,961 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,962 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,962 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 16:59:07,984 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,984 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 16:59:07,984 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,996 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_24.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:07,997 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 16:59:07,998 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 16:59:08,017 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LONDON_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,017 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 16:59:08,017 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 16:59:08,028 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,028 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 16:59:08,028 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 16:59:08,039 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,039 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 16:59:08,039 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 16:59:08,055 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_LUNCH_Lvl-1_2025_08_04.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,055 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 16:59:08,056 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 16:59:08,067 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,068 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 16:59:08,068 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 16:59:08,079 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_ASIA_Lvl-1_2025_08_06.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,079 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 16:59:08,079 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 16:59:08,101 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_AM_Lvl-1_2025_08_05.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,101 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 16:59:08,102 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 0 patterns from 0 sessions in 0.9s
2025-08-14 16:59:08,102 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_165908.json
2025-08-14 16:59:08,103 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:08,107 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:08,133 - IRONFORGE_SDK - ERROR - Pattern discovery failed for enhanced_rel_NY_PM_Lvl-1_2025_07_29.json: IRONFORGEDiscovery._extract_htf_confluence_patterns() takes 4 positional arguments but 5 were given
2025-08-14 16:59:08,134 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:08,138 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:58,875 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:58,875 - iron_core.lazy_manager.singleton - INFO - Initializing lazy loading manager singleton
2025-08-14 16:59:58,875 - iron_core.lazy_manager - INFO - Standard mathematical components registered for lazy loading
2025-08-14 16:59:58,876 - ironforge.container - INFO - Registered 6 IRONFORGE components for lazy loading
2025-08-14 16:59:59,720 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:59,784 - IRONFORGE_SDK - INFO - 🔗 Searching for cross-session links (similarity ≥ 0.5)
2025-08-14 16:59:59,785 - IRONFORGE_SDK - INFO - ✅ Found 0 cross-session links
2025-08-14 16:59:59,787 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:59,792 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:59,923 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 16:59:59,930 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 16:59:59,930 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 16:59:59,931 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 16:59:59,931 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:59,961 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 16:59:59,961 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:59,997 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 16:59:59,997 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,021 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,021 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,058 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,058 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:00:00,079 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:00:00,079 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,101 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,101 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,125 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,126 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,135 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,135 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,148 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,149 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,172 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,172 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,195 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,195 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,213 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,214 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,233 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,233 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,243 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,243 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,255 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,255 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,273 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,273 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,290 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,291 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,308 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,309 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,323 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,324 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,334 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,334 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,345 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,345 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,358 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,358 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,381 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,384 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,395 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,396 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,406 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,407 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,428 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,428 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,444 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,445 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,464 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,465 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,478 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,479 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,490 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,491 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,506 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:00:00,507 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,520 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,520 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,531 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,532 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,545 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:00:00,545 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,561 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,562 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,579 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,580 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,598 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,599 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,612 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,613 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,626 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,627 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,637 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,638 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:00:00,668 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:00:00,669 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,694 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,694 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,708 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,708 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,731 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,731 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,743 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:00:00,744 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,759 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,760 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,773 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,774 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,785 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,786 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:00:00,807 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:00:00,807 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,818 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:00:00,819 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,830 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,830 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,841 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:00:00,842 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,852 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:00:00,853 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:00:00,869 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:00:00,870 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,882 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:00:00,883 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,896 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:00:00,897 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,928 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:00:00,929 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 66 patterns from 35 sessions in 1.0s
2025-08-14 17:00:00,930 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_170000.json
2025-08-14 17:00:00,953 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:00,959 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:00:01,006 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:01,013 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:00:01,014 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 17:00:01,016 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 17:00:01,016 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,052 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,053 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,068 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,069 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,083 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,084 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,107 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,107 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:00:01,121 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:00:01,122 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,137 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,138 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,159 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,160 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,172 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,172 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,187 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,188 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,208 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,209 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,222 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,223 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,241 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,242 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,263 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,267 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,284 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,285 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,296 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,297 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,317 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,317 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,333 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,334 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,345 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,346 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,357 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,358 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,369 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,370 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,387 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,389 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,407 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,408 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,420 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,421 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,432 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,432 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,445 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,445 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,458 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,459 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,469 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,470 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,489 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,490 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,504 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,505 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,517 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,518 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,532 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:00:01,533 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,544 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,545 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,556 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,556 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,581 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:00:01,581 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,598 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,598 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,610 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,610 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,638 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,638 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,650 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,651 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,664 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,665 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,676 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,677 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:00:01,701 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:00:01,702 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,723 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,724 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,739 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,740 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,770 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,771 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,784 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:00:01,785 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,799 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,800 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,811 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,812 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,822 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,823 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:00:01,846 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:00:01,847 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,859 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:00:01,859 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,876 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,877 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,892 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:00:01,893 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,905 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:00:01,906 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:00:01,924 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:00:01,925 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,938 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:00:01,938 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,951 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:00:01,952 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,997 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:00:01,998 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 76 patterns from 44 sessions in 1.0s
2025-08-14 17:00:01,999 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_170001.json
2025-08-14 17:00:02,001 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:02,008 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:00:02,009 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:02,013 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:00:02,033 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:02,037 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:00:02,038 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 17:00:02,039 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 17:00:02,039 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,059 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,059 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,073 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,074 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,088 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,088 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,118 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,119 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:00:02,135 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:00:02,136 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,151 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,151 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,163 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,164 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,188 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,188 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,205 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,205 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,232 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,233 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,247 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,248 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,266 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,267 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,287 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,288 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,298 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,298 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,311 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,312 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,330 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,330 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,356 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,358 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,370 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,371 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,382 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,383 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,394 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,395 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,405 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,406 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,419 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,419 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,432 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,433 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,444 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,445 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,457 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,457 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,481 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,482 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,492 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,493 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,514 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,515 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,528 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,529 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,543 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,544 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,558 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:00:02,559 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,570 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,571 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,583 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,584 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,607 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:00:02,608 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,623 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,624 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,634 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,635 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,649 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,650 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,662 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:00:02,663 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,676 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,677 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,687 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,688 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:00:02,721 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:00:02,722 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,742 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,743 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,755 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,756 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,841 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,842 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,854 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:00:02,855 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,869 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:00:02,870 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,882 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:00:02,882 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,893 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,894 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:00:02,917 - IRONFORGE_SDK - INFO - ✅ Discovered 5 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:00:02,918 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,929 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:00:02,929 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,941 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,941 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,953 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:00:02,953 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,964 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:00:02,964 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:00:02,987 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:00:02,988 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:00:03,005 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:00:03,005 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:00:03,017 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:00:03,017 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:00:03,038 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:00:03,040 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 80 patterns from 42 sessions in 1.0s
2025-08-14 17:00:03,040 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_170003.json
2025-08-14 17:00:03,042 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:03,047 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:00:03,070 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:00:03,079 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:18:01,813 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:18:01,814 - iron_core.lazy_manager.singleton - INFO - Initializing lazy loading manager singleton
2025-08-14 17:18:01,814 - iron_core.lazy_manager - INFO - Standard mathematical components registered for lazy loading
2025-08-14 17:18:01,814 - ironforge.container - INFO - Registered 6 IRONFORGE components for lazy loading
2025-08-14 17:18:02,584 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
2025-08-14 17:18:02,584 - IRONFORGE_SDK - INFO - 🚀 Starting systematic discovery across all enhanced sessions
2025-08-14 17:18:02,585 - IRONFORGE_SDK - INFO - 📊 Found 57 enhanced sessions to process
2025-08-14 17:18:02,585 - IRONFORGE_SDK - INFO - 🔍 Processing session 1/57: enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,675 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,676 - IRONFORGE_SDK - INFO - 🔍 Processing session 2/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 17:18:02,699 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_30.json
2025-08-14 17:18:02,700 - IRONFORGE_SDK - INFO - 🔍 Processing session 3/57: enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:18:02,725 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_30.json
2025-08-14 17:18:02,725 - IRONFORGE_SDK - INFO - 🔍 Processing session 4/57: enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,758 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,758 - IRONFORGE_SDK - INFO - 🔍 Processing session 5/57: enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:18:02,777 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_08_04.json
2025-08-14 17:18:02,778 - IRONFORGE_SDK - INFO - 🔍 Processing session 6/57: enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:18:02,805 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LUNCH_Lvl-1_2025_08_06.json
2025-08-14 17:18:02,805 - IRONFORGE_SDK - INFO - 🔍 Processing session 7/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,824 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,824 - IRONFORGE_SDK - INFO - 🔍 Processing session 8/57: enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:18:02,847 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREASIA_Lvl-1_2025_07_30.json
2025-08-14 17:18:02,847 - IRONFORGE_SDK - INFO - 🔍 Processing session 9/57: enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:18:02,862 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_31.json
2025-08-14 17:18:02,862 - IRONFORGE_SDK - INFO - 🔍 Processing session 10/57: enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:18:02,891 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NYPM_Lvl-1_2025_08_06.json
2025-08-14 17:18:02,891 - IRONFORGE_SDK - INFO - 🔍 Processing session 11/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:18:02,914 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_31.json
2025-08-14 17:18:02,914 - IRONFORGE_SDK - INFO - 🔍 Processing session 12/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,937 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,937 - IRONFORGE_SDK - INFO - 🔍 Processing session 13/57: enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,963 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_05.json
2025-08-14 17:18:02,964 - IRONFORGE_SDK - INFO - 🔍 Processing session 14/57: enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:18:02,987 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_24.json
2025-08-14 17:18:02,987 - IRONFORGE_SDK - INFO - 🔍 Processing session 15/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:18:03,000 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_31.json
2025-08-14 17:18:03,002 - IRONFORGE_SDK - INFO - 🔍 Processing session 16/57: enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,029 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,029 - IRONFORGE_SDK - INFO - 🔍 Processing session 17/57: enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,056 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,056 - IRONFORGE_SDK - INFO - 🔍 Processing session 18/57: enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,071 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,071 - IRONFORGE_SDK - INFO - 🔍 Processing session 19/57: enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,091 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,092 - IRONFORGE_SDK - INFO - 🔍 Processing session 20/57: enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,109 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_NY_AM_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,110 - IRONFORGE_SDK - INFO - 🔍 Processing session 21/57: enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,126 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,126 - IRONFORGE_SDK - INFO - 🔍 Processing session 22/57: enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,152 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,152 - IRONFORGE_SDK - INFO - 🔍 Processing session 23/57: enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,168 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,168 - IRONFORGE_SDK - INFO - 🔍 Processing session 24/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,189 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,189 - IRONFORGE_SDK - INFO - 🔍 Processing session 25/57: enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,204 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,204 - IRONFORGE_SDK - INFO - 🔍 Processing session 26/57: enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,224 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,224 - IRONFORGE_SDK - INFO - 🔍 Processing session 27/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,235 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,236 - IRONFORGE_SDK - INFO - 🔍 Processing session 28/57: enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,265 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,265 - IRONFORGE_SDK - INFO - 🔍 Processing session 29/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,289 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,289 - IRONFORGE_SDK - INFO - 🔍 Processing session 30/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,305 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,305 - IRONFORGE_SDK - INFO - 🔍 Processing session 31/57: enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:18:03,325 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_31.json
2025-08-14 17:18:03,326 - IRONFORGE_SDK - INFO - 🔍 Processing session 32/57: enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,346 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,346 - IRONFORGE_SDK - INFO - 🔍 Processing session 33/57: enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,360 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_ASIA_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,360 - IRONFORGE_SDK - INFO - 🔍 Processing session 34/57: enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,381 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_AM_Lvl-1_2025_07_30.json
2025-08-14 17:18:03,382 - IRONFORGE_SDK - INFO - 🔍 Processing session 35/57: enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,399 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_NY_PM_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,399 - IRONFORGE_SDK - INFO - 🔍 Processing session 36/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,421 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,421 - IRONFORGE_SDK - INFO - 🔍 Processing session 37/57: enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,440 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,441 - IRONFORGE_SDK - INFO - 🔍 Processing session 38/57: enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,459 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_ASIA_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,459 - IRONFORGE_SDK - INFO - 🔍 Processing session 39/57: enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,485 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,485 - IRONFORGE_SDK - INFO - 🔍 Processing session 40/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,506 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,507 - IRONFORGE_SDK - INFO - 🔍 Processing session 41/57: enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:18:03,541 - IRONFORGE_SDK - INFO - ✅ Discovered 4 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_04.json
2025-08-14 17:18:03,541 - IRONFORGE_SDK - INFO - 🔍 Processing session 42/57: enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,572 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,572 - IRONFORGE_SDK - INFO - 🔍 Processing session 43/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,593 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,593 - IRONFORGE_SDK - INFO - 🔍 Processing session 44/57: enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,621 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,621 - IRONFORGE_SDK - INFO - 🔍 Processing session 45/57: enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,640 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_28.json
2025-08-14 17:18:03,640 - IRONFORGE_SDK - INFO - 🔍 Processing session 46/57: enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:18:03,665 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_05.json
2025-08-14 17:18:03,666 - IRONFORGE_SDK - INFO - 🔍 Processing session 47/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,690 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,690 - IRONFORGE_SDK - INFO - 🔍 Processing session 48/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,708 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,708 - IRONFORGE_SDK - INFO - 🔍 Processing session 49/57: enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:18:03,739 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NYAM_Lvl-1_2025_08_07_FRESH.json
2025-08-14 17:18:03,740 - IRONFORGE_SDK - INFO - 🔍 Processing session 50/57: enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,758 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_LONDON_Lvl-1_2025_07_24.json
2025-08-14 17:18:03,758 - IRONFORGE_SDK - INFO - 🔍 Processing session 51/57: enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,774 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LONDON_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,774 - IRONFORGE_SDK - INFO - 🔍 Processing session 52/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,791 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_PREMARKET_Lvl-1_2025_07_25.json
2025-08-14 17:18:03,792 - IRONFORGE_SDK - INFO - 🔍 Processing session 53/57: enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,811 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_MIDNIGHT_Lvl-1_2025_08_07.json
2025-08-14 17:18:03,811 - IRONFORGE_SDK - INFO - 🔍 Processing session 54/57: enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:18:03,833 - IRONFORGE_SDK - INFO - ✅ Discovered 2 patterns in enhanced_rel_LUNCH_Lvl-1_2025_08_04.json
2025-08-14 17:18:03,833 - IRONFORGE_SDK - INFO - 🔍 Processing session 55/57: enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,857 - IRONFORGE_SDK - WARNING - ⚠️ No patterns found in enhanced_rel_PREMARKET_Lvl-1_2025_07_29.json
2025-08-14 17:18:03,857 - IRONFORGE_SDK - INFO - 🔍 Processing session 56/57: enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,878 - IRONFORGE_SDK - INFO - ✅ Discovered 1 patterns in enhanced_rel_ASIA_Lvl-1_2025_08_06.json
2025-08-14 17:18:03,878 - IRONFORGE_SDK - INFO - 🔍 Processing session 57/57: enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:18:03,914 - IRONFORGE_SDK - INFO - ✅ Discovered 3 patterns in enhanced_rel_NY_AM_Lvl-1_2025_08_05.json
2025-08-14 17:18:03,916 - IRONFORGE_SDK - INFO - 🎉 Discovery complete: 78 patterns from 43 sessions in 1.3s
2025-08-14 17:18:03,916 - IRONFORGE_SDK - INFO - 💾 Results cached to /Users/<USER>/IRONPULSE/IRONFORGE/discovery_cache/discovery_results_20250814_171803.json
2025-08-14 17:43:08,915 - IRONFORGE_SDK - INFO - IRONFORGE Discovery SDK initialized
2025-08-14 17:43:08,915 - iron_core.lazy_manager.singleton - INFO - Initializing lazy loading manager singleton
2025-08-14 17:43:08,916 - iron_core.lazy_manager - INFO - Standard mathematical components registered for lazy loading
2025-08-14 17:43:08,916 - ironforge.container - INFO - Registered 6 IRONFORGE components for lazy loading
2025-08-14 17:43:09,840 - IRONFORGE_SDK - INFO - ✅ IRONFORGE discovery engine initialized successfully
