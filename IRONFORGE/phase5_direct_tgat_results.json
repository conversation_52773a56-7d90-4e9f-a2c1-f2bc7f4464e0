{"validation_timestamp": "2025-08-14T16:06:09.527528", "test_sessions": ["enhanced_rel_NY_PM_Lvl-1_2025_07_29.json", "enhanced_rel_ASIA_Lvl-1_2025_07_30.json", "enhanced_rel_NY_AM_Lvl-1_2025_07_25.json", "enhanced_rel_LONDON_Lvl-1_2025_07_28.json", "enhanced_rel_LONDON_Lvl-1_2025_07_25.json"], "discovery_results": {"enhanced_rel_NY_PM_Lvl-1_2025_07_29.json": {"session_name": "enhanced_rel_NY_PM_Lvl-1_2025_07_29.json", "discovery_success": true, "patterns_found": 7, "patterns": [{"type": "temporal_structural", "description": "Price position 81.8% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "ny_pm", "structural_position": 0.8178528347406514, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 89.3% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "ny_pm", "structural_position": 0.8926417370325693, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 0.0% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "ny_pm", "structural_position": 0.0, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 29.4% at 9540 minutes", "confidence": 0.7, "time_span_hours": 2.65, "session": "ny_pm", "structural_position": 0.2943305186972256, "temporal_position": 9540}, {"type": "temporal_structural", "description": "Price position 81.8% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "ny_pm", "structural_position": 0.8178528347406514, "temporal_position": 0}, {"type": "htf_confluence", "description": "HTF confluence 0.99 strength with 0.95 energy density", "confidence": 0.95, "time_span_hours": 4.0, "session": "ny_pm", "htf_strength": 0.99, "energy_level": 0.95}, {"type": "scale_alignment", "description": "Multi-scale alignment with 6 cross-session events", "confidence": 0.9, "time_span_hours": 2.0, "session": "ny_pm", "event_count": 30, "cross_session_count": 6}], "embeddings_shape": "<PERSON>.<PERSON><PERSON>([61, 256])", "graph_info": {"graph": {"nodes": {"1m": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29], "5m": [], "15m": [30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59], "1h": [60], "D": [], "W": []}, "rich_node_features": [{"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}], "edges": {"temporal": [{"source": 1, "target": 5, "feature_idx": 0}, {"source": 5, "target": 2, "feature_idx": 1}, {"source": 2, "target": 6, "feature_idx": 2}, {"source": 6, "target": 8, "feature_idx": 3}, {"source": 8, "target": 0, "feature_idx": 4}, {"source": 0, "target": 4, "feature_idx": 5}, {"source": 4, "target": 9, "feature_idx": 6}, {"source": 9, "target": 10, "feature_idx": 7}, {"source": 10, "target": 11, "feature_idx": 8}, {"source": 11, "target": 12, "feature_idx": 9}, {"source": 12, "target": 13, "feature_idx": 10}, {"source": 13, "target": 14, "feature_idx": 11}, {"source": 14, "target": 15, "feature_idx": 12}, {"source": 15, "target": 16, "feature_idx": 13}, {"source": 16, "target": 17, "feature_idx": 14}, {"source": 17, "target": 18, "feature_idx": 15}, {"source": 18, "target": 19, "feature_idx": 16}, {"source": 19, "target": 20, "feature_idx": 17}, {"source": 20, "target": 21, "feature_idx": 18}, {"source": 21, "target": 22, "feature_idx": 19}, {"source": 22, "target": 23, "feature_idx": 20}, {"source": 23, "target": 24, "feature_idx": 21}, {"source": 24, "target": 25, "feature_idx": 22}, {"source": 25, "target": 26, "feature_idx": 23}, {"source": 26, "target": 27, "feature_idx": 24}, {"source": 27, "target": 28, "feature_idx": 25}, {"source": 28, "target": 3, "feature_idx": 26}, {"source": 3, "target": 7, "feature_idx": 27}, {"source": 7, "target": 29, "feature_idx": 28}, {"source": 31, "target": 35, "feature_idx": 29}, {"source": 35, "target": 32, "feature_idx": 30}, {"source": 32, "target": 36, "feature_idx": 31}, {"source": 36, "target": 38, "feature_idx": 32}, {"source": 38, "target": 30, "feature_idx": 33}, {"source": 30, "target": 34, "feature_idx": 34}, {"source": 34, "target": 39, "feature_idx": 35}, {"source": 39, "target": 40, "feature_idx": 36}, {"source": 40, "target": 41, "feature_idx": 37}, {"source": 41, "target": 42, "feature_idx": 38}, {"source": 42, "target": 43, "feature_idx": 39}, {"source": 43, "target": 44, "feature_idx": 40}, {"source": 44, "target": 45, "feature_idx": 41}, {"source": 45, "target": 46, "feature_idx": 42}, {"source": 46, "target": 47, "feature_idx": 43}, {"source": 47, "target": 48, "feature_idx": 44}, {"source": 48, "target": 49, "feature_idx": 45}, {"source": 49, "target": 50, "feature_idx": 46}, {"source": 50, "target": 51, "feature_idx": 47}, {"source": 51, "target": 52, "feature_idx": 48}, {"source": 52, "target": 53, "feature_idx": 49}, {"source": 53, "target": 54, "feature_idx": 50}, {"source": 54, "target": 55, "feature_idx": 51}, {"source": 55, "target": 56, "feature_idx": 52}, {"source": 56, "target": 57, "feature_idx": 53}, {"source": 57, "target": 58, "feature_idx": 54}, {"source": 58, "target": 33, "feature_idx": 55}, {"source": 33, "target": 37, "feature_idx": 56}, {"source": 37, "target": 59, "feature_idx": 57}], "scale": [{"source": 49, "target": 60, "feature_idx": 58}, {"source": 50, "target": 60, "feature_idx": 59}, {"source": 51, "target": 60, "feature_idx": 60}, {"source": 52, "target": 60, "feature_idx": 61}, {"source": 53, "target": 60, "feature_idx": 62}, {"source": 54, "target": 60, "feature_idx": 63}, {"source": 55, "target": 60, "feature_idx": 64}], "cascade": [], "pd_array": [], "cross_tf_confluence": [{"source": 30, "target": 60, "feature_idx": 65}, {"source": 31, "target": 60, "feature_idx": 66}, {"source": 32, "target": 60, "feature_idx": 67}, {"source": 33, "target": 60, "feature_idx": 68}, {"source": 34, "target": 60, "feature_idx": 69}, {"source": 35, "target": 60, "feature_idx": 70}, {"source": 36, "target": 60, "feature_idx": 71}, {"source": 37, "target": 60, "feature_idx": 72}, {"source": 38, "target": 60, "feature_idx": 73}, {"source": 39, "target": 60, "feature_idx": 74}, {"source": 40, "target": 60, "feature_idx": 75}, {"source": 41, "target": 60, "feature_idx": 76}, {"source": 42, "target": 60, "feature_idx": 77}, {"source": 43, "target": 60, "feature_idx": 78}, {"source": 44, "target": 60, "feature_idx": 79}, {"source": 45, "target": 60, "feature_idx": 80}, {"source": 46, "target": 60, "feature_idx": 81}, {"source": 47, "target": 60, "feature_idx": 82}, {"source": 48, "target": 60, "feature_idx": 83}, {"source": 49, "target": 60, "feature_idx": 84}, {"source": 50, "target": 60, "feature_idx": 85}, {"source": 51, "target": 60, "feature_idx": 86}, {"source": 52, "target": 60, "feature_idx": 87}, {"source": 53, "target": 60, "feature_idx": 88}, {"source": 54, "target": 60, "feature_idx": 89}, {"source": 55, "target": 60, "feature_idx": 90}, {"source": 56, "target": 60, "feature_idx": 91}, {"source": 57, "target": 60, "feature_idx": 92}, {"source": 58, "target": 60, "feature_idx": 93}, {"source": 59, "target": 60, "feature_idx": 94}], "temporal_echo": [{"source": 0, "target": 30, "feature_idx": 95}, {"source": 0, "target": 34, "feature_idx": 96}, {"source": 0, "target": 38, "feature_idx": 97}, {"source": 0, "target": 39, "feature_idx": 98}, {"source": 0, "target": 40, "feature_idx": 99}, {"source": 0, "target": 41, "feature_idx": 100}, {"source": 0, "target": 42, "feature_idx": 101}, {"source": 0, "target": 43, "feature_idx": 102}, {"source": 1, "target": 31, "feature_idx": 103}, {"source": 1, "target": 35, "feature_idx": 104}, {"source": 2, "target": 32, "feature_idx": 105}, {"source": 2, "target": 36, "feature_idx": 106}, {"source": 3, "target": 33, "feature_idx": 107}, {"source": 3, "target": 37, "feature_idx": 108}, {"source": 3, "target": 55, "feature_idx": 109}, {"source": 3, "target": 56, "feature_idx": 110}, {"source": 3, "target": 57, "feature_idx": 111}, {"source": 3, "target": 58, "feature_idx": 112}, {"source": 3, "target": 59, "feature_idx": 113}, {"source": 4, "target": 30, "feature_idx": 114}, {"source": 4, "target": 34, "feature_idx": 115}, {"source": 4, "target": 38, "feature_idx": 116}, {"source": 4, "target": 39, "feature_idx": 117}, {"source": 4, "target": 40, "feature_idx": 118}, {"source": 4, "target": 41, "feature_idx": 119}, {"source": 4, "target": 42, "feature_idx": 120}, {"source": 4, "target": 43, "feature_idx": 121}, {"source": 5, "target": 31, "feature_idx": 122}, {"source": 5, "target": 35, "feature_idx": 123}, {"source": 6, "target": 32, "feature_idx": 124}, {"source": 6, "target": 36, "feature_idx": 125}, {"source": 7, "target": 33, "feature_idx": 126}, {"source": 7, "target": 37, "feature_idx": 127}, {"source": 7, "target": 55, "feature_idx": 128}, {"source": 7, "target": 56, "feature_idx": 129}, {"source": 7, "target": 57, "feature_idx": 130}, {"source": 7, "target": 58, "feature_idx": 131}, {"source": 7, "target": 59, "feature_idx": 132}, {"source": 8, "target": 30, "feature_idx": 133}, {"source": 8, "target": 34, "feature_idx": 134}, {"source": 8, "target": 38, "feature_idx": 135}, {"source": 8, "target": 39, "feature_idx": 136}, {"source": 8, "target": 40, "feature_idx": 137}, {"source": 8, "target": 41, "feature_idx": 138}, {"source": 8, "target": 42, "feature_idx": 139}, {"source": 9, "target": 30, "feature_idx": 140}, {"source": 9, "target": 34, "feature_idx": 141}, {"source": 9, "target": 38, "feature_idx": 142}, {"source": 9, "target": 39, "feature_idx": 143}, {"source": 9, "target": 40, "feature_idx": 144}, {"source": 9, "target": 41, "feature_idx": 145}, {"source": 9, "target": 42, "feature_idx": 146}, {"source": 9, "target": 43, "feature_idx": 147}, {"source": 10, "target": 30, "feature_idx": 148}, {"source": 10, "target": 34, "feature_idx": 149}, {"source": 10, "target": 38, "feature_idx": 150}, {"source": 10, "target": 39, "feature_idx": 151}, {"source": 10, "target": 40, "feature_idx": 152}, {"source": 10, "target": 41, "feature_idx": 153}, {"source": 10, "target": 42, "feature_idx": 154}, {"source": 10, "target": 43, "feature_idx": 155}, {"source": 11, "target": 30, "feature_idx": 156}, {"source": 11, "target": 34, "feature_idx": 157}, {"source": 11, "target": 38, "feature_idx": 158}, {"source": 11, "target": 39, "feature_idx": 159}, {"source": 11, "target": 40, "feature_idx": 160}, {"source": 11, "target": 41, "feature_idx": 161}, {"source": 11, "target": 42, "feature_idx": 162}, {"source": 11, "target": 43, "feature_idx": 163}, {"source": 12, "target": 30, "feature_idx": 164}, {"source": 12, "target": 34, "feature_idx": 165}, {"source": 12, "target": 38, "feature_idx": 166}, {"source": 12, "target": 39, "feature_idx": 167}, {"source": 12, "target": 40, "feature_idx": 168}, {"source": 12, "target": 41, "feature_idx": 169}, {"source": 12, "target": 42, "feature_idx": 170}, {"source": 12, "target": 43, "feature_idx": 171}, {"source": 13, "target": 30, "feature_idx": 172}, {"source": 13, "target": 34, "feature_idx": 173}, {"source": 13, "target": 39, "feature_idx": 174}, {"source": 13, "target": 40, "feature_idx": 175}, {"source": 13, "target": 41, "feature_idx": 176}, {"source": 13, "target": 42, "feature_idx": 177}, {"source": 13, "target": 43, "feature_idx": 178}, {"source": 13, "target": 44, "feature_idx": 179}, {"source": 13, "target": 45, "feature_idx": 180}, {"source": 14, "target": 43, "feature_idx": 181}, {"source": 14, "target": 44, "feature_idx": 182}, {"source": 14, "target": 45, "feature_idx": 183}, {"source": 14, "target": 46, "feature_idx": 184}, {"source": 14, "target": 47, "feature_idx": 185}, {"source": 15, "target": 43, "feature_idx": 186}, {"source": 15, "target": 44, "feature_idx": 187}, {"source": 15, "target": 45, "feature_idx": 188}, {"source": 15, "target": 46, "feature_idx": 189}, {"source": 15, "target": 47, "feature_idx": 190}, {"source": 15, "target": 48, "feature_idx": 191}, {"source": 16, "target": 44, "feature_idx": 192}, {"source": 16, "target": 45, "feature_idx": 193}, {"source": 16, "target": 46, "feature_idx": 194}, {"source": 16, "target": 47, "feature_idx": 195}, {"source": 16, "target": 48, "feature_idx": 196}, {"source": 17, "target": 44, "feature_idx": 197}, {"source": 17, "target": 45, "feature_idx": 198}, {"source": 17, "target": 46, "feature_idx": 199}, {"source": 17, "target": 47, "feature_idx": 200}, {"source": 17, "target": 48, "feature_idx": 201}, {"source": 18, "target": 45, "feature_idx": 202}, {"source": 18, "target": 46, "feature_idx": 203}, {"source": 18, "target": 47, "feature_idx": 204}, {"source": 18, "target": 48, "feature_idx": 205}, {"source": 18, "target": 49, "feature_idx": 206}, {"source": 19, "target": 48, "feature_idx": 207}, {"source": 19, "target": 49, "feature_idx": 208}, {"source": 19, "target": 50, "feature_idx": 209}, {"source": 19, "target": 51, "feature_idx": 210}, {"source": 19, "target": 52, "feature_idx": 211}, {"source": 19, "target": 60, "feature_idx": 212}, {"source": 20, "target": 49, "feature_idx": 213}, {"source": 20, "target": 50, "feature_idx": 214}, {"source": 20, "target": 51, "feature_idx": 215}, {"source": 20, "target": 52, "feature_idx": 216}, {"source": 20, "target": 53, "feature_idx": 217}, {"source": 20, "target": 54, "feature_idx": 218}, {"source": 20, "target": 55, "feature_idx": 219}, {"source": 20, "target": 60, "feature_idx": 220}, {"source": 21, "target": 49, "feature_idx": 221}, {"source": 21, "target": 50, "feature_idx": 222}, {"source": 21, "target": 51, "feature_idx": 223}, {"source": 21, "target": 52, "feature_idx": 224}, {"source": 21, "target": 53, "feature_idx": 225}, {"source": 21, "target": 54, "feature_idx": 226}, {"source": 21, "target": 55, "feature_idx": 227}, {"source": 21, "target": 60, "feature_idx": 228}, {"source": 22, "target": 49, "feature_idx": 229}, {"source": 22, "target": 50, "feature_idx": 230}, {"source": 22, "target": 51, "feature_idx": 231}, {"source": 22, "target": 52, "feature_idx": 232}, {"source": 22, "target": 53, "feature_idx": 233}, {"source": 22, "target": 54, "feature_idx": 234}, {"source": 22, "target": 55, "feature_idx": 235}, {"source": 22, "target": 56, "feature_idx": 236}, {"source": 22, "target": 60, "feature_idx": 237}, {"source": 23, "target": 50, "feature_idx": 238}, {"source": 23, "target": 51, "feature_idx": 239}, {"source": 23, "target": 52, "feature_idx": 240}, {"source": 23, "target": 53, "feature_idx": 241}, {"source": 23, "target": 54, "feature_idx": 242}, {"source": 23, "target": 55, "feature_idx": 243}, {"source": 23, "target": 56, "feature_idx": 244}, {"source": 23, "target": 57, "feature_idx": 245}, {"source": 23, "target": 58, "feature_idx": 246}, {"source": 23, "target": 60, "feature_idx": 247}, {"source": 24, "target": 50, "feature_idx": 248}, {"source": 24, "target": 51, "feature_idx": 249}, {"source": 24, "target": 52, "feature_idx": 250}, {"source": 24, "target": 53, "feature_idx": 251}, {"source": 24, "target": 54, "feature_idx": 252}, {"source": 24, "target": 55, "feature_idx": 253}, {"source": 24, "target": 56, "feature_idx": 254}, {"source": 24, "target": 57, "feature_idx": 255}, {"source": 24, "target": 58, "feature_idx": 256}, {"source": 24, "target": 60, "feature_idx": 257}, {"source": 25, "target": 33, "feature_idx": 258}, {"source": 25, "target": 37, "feature_idx": 259}, {"source": 25, "target": 50, "feature_idx": 260}, {"source": 25, "target": 51, "feature_idx": 261}, {"source": 25, "target": 52, "feature_idx": 262}, {"source": 25, "target": 53, "feature_idx": 263}, {"source": 25, "target": 54, "feature_idx": 264}, {"source": 25, "target": 55, "feature_idx": 265}, {"source": 25, "target": 56, "feature_idx": 266}, {"source": 25, "target": 57, "feature_idx": 267}, {"source": 25, "target": 58, "feature_idx": 268}, {"source": 25, "target": 59, "feature_idx": 269}, {"source": 25, "target": 60, "feature_idx": 270}, {"source": 26, "target": 33, "feature_idx": 271}, {"source": 26, "target": 37, "feature_idx": 272}, {"source": 26, "target": 52, "feature_idx": 273}, {"source": 26, "target": 53, "feature_idx": 274}, {"source": 26, "target": 54, "feature_idx": 275}, {"source": 26, "target": 55, "feature_idx": 276}, {"source": 26, "target": 56, "feature_idx": 277}, {"source": 26, "target": 57, "feature_idx": 278}, {"source": 26, "target": 58, "feature_idx": 279}, {"source": 26, "target": 59, "feature_idx": 280}, {"source": 27, "target": 33, "feature_idx": 281}, {"source": 27, "target": 37, "feature_idx": 282}, {"source": 27, "target": 53, "feature_idx": 283}, {"source": 27, "target": 54, "feature_idx": 284}, {"source": 27, "target": 55, "feature_idx": 285}, {"source": 27, "target": 56, "feature_idx": 286}, {"source": 27, "target": 57, "feature_idx": 287}, {"source": 27, "target": 58, "feature_idx": 288}, {"source": 27, "target": 59, "feature_idx": 289}, {"source": 28, "target": 33, "feature_idx": 290}, {"source": 28, "target": 37, "feature_idx": 291}, {"source": 28, "target": 53, "feature_idx": 292}, {"source": 28, "target": 54, "feature_idx": 293}, {"source": 28, "target": 55, "feature_idx": 294}, {"source": 28, "target": 56, "feature_idx": 295}, {"source": 28, "target": 57, "feature_idx": 296}, {"source": 28, "target": 58, "feature_idx": 297}, {"source": 28, "target": 59, "feature_idx": 298}, {"source": 29, "target": 33, "feature_idx": 299}, {"source": 29, "target": 37, "feature_idx": 300}, {"source": 29, "target": 55, "feature_idx": 301}, {"source": 29, "target": 56, "feature_idx": 302}, {"source": 29, "target": 57, "feature_idx": 303}, {"source": 29, "target": 58, "feature_idx": 304}, {"source": 29, "target": 59, "feature_idx": 305}, {"source": 49, "target": 60, "feature_idx": 306}, {"source": 50, "target": 60, "feature_idx": 307}, {"source": 51, "target": 60, "feature_idx": 308}, {"source": 52, "target": 60, "feature_idx": 309}, {"source": 53, "target": 60, "feature_idx": 310}, {"source": 54, "target": 60, "feature_idx": 311}, {"source": 55, "target": 60, "feature_idx": 312}], "discovered": [{"source": 0, "target": 38, "feature_idx": 313}, {"source": 0, "target": 41, "feature_idx": 314}, {"source": 0, "target": 42, "feature_idx": 315}, {"source": 0, "target": 45, "feature_idx": 316}, {"source": 0, "target": 46, "feature_idx": 317}, {"source": 4, "target": 38, "feature_idx": 318}, {"source": 4, "target": 41, "feature_idx": 319}, {"source": 4, "target": 42, "feature_idx": 320}, {"source": 4, "target": 45, "feature_idx": 321}, {"source": 4, "target": 46, "feature_idx": 322}, {"source": 8, "target": 38, "feature_idx": 323}, {"source": 8, "target": 41, "feature_idx": 324}, {"source": 8, "target": 42, "feature_idx": 325}, {"source": 8, "target": 45, "feature_idx": 326}, {"source": 8, "target": 46, "feature_idx": 327}, {"source": 10, "target": 38, "feature_idx": 328}, {"source": 10, "target": 41, "feature_idx": 329}, {"source": 10, "target": 42, "feature_idx": 330}, {"source": 10, "target": 45, "feature_idx": 331}, {"source": 10, "target": 46, "feature_idx": 332}, {"source": 11, "target": 38, "feature_idx": 333}, {"source": 11, "target": 41, "feature_idx": 334}, {"source": 11, "target": 42, "feature_idx": 335}, {"source": 11, "target": 45, "feature_idx": 336}, {"source": 11, "target": 46, "feature_idx": 337}, {"source": 14, "target": 38, "feature_idx": 338}, {"source": 14, "target": 41, "feature_idx": 339}, {"source": 14, "target": 42, "feature_idx": 340}, {"source": 14, "target": 45, "feature_idx": 341}, {"source": 14, "target": 46, "feature_idx": 342}, {"source": 16, "target": 38, "feature_idx": 343}, {"source": 16, "target": 41, "feature_idx": 344}, {"source": 16, "target": 42, "feature_idx": 345}, {"source": 16, "target": 45, "feature_idx": 346}, {"source": 16, "target": 46, "feature_idx": 347}, {"source": 18, "target": 38, "feature_idx": 348}, {"source": 18, "target": 41, "feature_idx": 349}, {"source": 18, "target": 42, "feature_idx": 350}, {"source": 18, "target": 45, "feature_idx": 351}, {"source": 18, "target": 46, "feature_idx": 352}], "structural_context": []}, "rich_edge_features": [{"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}], "metadata": {"session_id": "unknown", "total_nodes": 61, "timeframe_counts": {"1m": 30, "5m": 0, "15m": 30, "1h": 1, "D": 0, "W": 0}, "feature_dimensions": 37, "preserved_raw": {"session_metadata": {"session_type": "ny_pm", "session_date": "2025-07-29", "session_start": "13:30:00", "session_end": "16:09:00", "session_duration": 159, "transcription_source": "live_market_data", "data_completeness": "complete_session", "timezone": "ET", "session_status": "completed"}, "session_fpfvg": {"fpfvg_present": false, "fpfvg_formation": {"formation_time": "00:00:00", "premium_high": 0.0, "discount_low": 0.0, "gap_size": 0.0, "interactions": []}}, "price_movements": [{"timestamp": "13:30:00", "price_level": 23506.0, "movement_type": "open", "normalized_price": 0.8178528347406514, "pct_from_open": 0.0, "pct_from_high": 18.21471652593486, "pct_from_low": 81.78528347406514, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": -5400, "price_momentum": 0.0, "range_position": 0.8178528347406514, "absolute_price": 23506.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348068"}, {"timestamp": "12:00:00", "price_level": 23513.75, "movement_type": "session_high", "normalized_price": 0.8926417370325693, "pct_from_open": 0.03297030545392666, "pct_from_high": 10.735826296743065, "pct_from_low": 89.26417370325693, "time_since_session_open": 0, "normalized_time": -0.5660377358490566, "time_to_next_event": 1800, "price_momentum": 0.03297030545392666, "range_position": 0.8926417370325693, "absolute_price": 23513.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348121"}, {"timestamp": "12:30:00", "price_level": 23421.25, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.36054624351229475, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 0, "normalized_time": -0.37735849056603776, "time_to_next_event": 13140, "price_momentum": -0.3933868481207804, "range_position": 0.0, "absolute_price": 23421.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348166"}, {"timestamp": "16:09:00", "price_level": 23451.75, "movement_type": "close", "normalized_price": 0.2943305186972256, "pct_from_open": -0.23079213817748662, "pct_from_high": 70.56694813027744, "pct_from_low": 29.43305186972256, "time_since_session_open": 9540, "normalized_time": 1.0, "time_to_next_event": -9540, "price_momentum": 0.13022362171105298, "range_position": 0.2943305186972256, "absolute_price": 23451.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348210"}, {"timestamp": "13:30:00", "price_level": 23506.0, "movement_type": "open", "normalized_price": 0.8178528347406514, "pct_from_open": 0.0, "pct_from_high": 18.21471652593486, "pct_from_low": 81.78528347406514, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": -5400, "price_momentum": 0.23132602044623535, "range_position": 0.8178528347406514, "absolute_price": 23506.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348253"}, {"timestamp": "12:00:00", "price_level": 23513.75, "movement_type": "session_high", "normalized_price": 0.8926417370325693, "pct_from_open": 0.03297030545392666, "pct_from_high": 10.735826296743065, "pct_from_low": 89.26417370325693, "time_since_session_open": 0, "normalized_time": -0.5660377358490566, "time_to_next_event": 1800, "price_momentum": 0.03297030545392666, "range_position": 0.8926417370325693, "absolute_price": 23513.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348295"}, {"timestamp": "12:30:00", "price_level": 23421.25, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.36054624351229475, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 0, "normalized_time": -0.37735849056603776, "time_to_next_event": 13140, "price_momentum": -0.3933868481207804, "range_position": 0.0, "absolute_price": 23421.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348337"}, {"timestamp": "16:09:00", "price_level": 23451.75, "movement_type": "close", "normalized_price": 0.2943305186972256, "pct_from_open": -0.23079213817748662, "pct_from_high": 70.56694813027744, "pct_from_low": 29.43305186972256, "time_since_session_open": 9540, "normalized_time": 1.0, "time_to_next_event": -10140, "price_momentum": 0.13022362171105298, "range_position": 0.2943305186972256, "absolute_price": 23451.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348379"}, {"timestamp": "13:20:00", "price": 23524.875, "action": "delivery", "context": "Today's Asia First Presentation FPFVG redelivered before session open", "normalized_price": 1.0, "pct_from_open": 0.08029864715391816, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 0, "normalized_time": -0.06289308176100629, "time_to_next_event": 600, "price_momentum": 0.31181041926508685, "range_position": 1.0, "absolute_price": 23524.875, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348422", "price_level": 23524.875}, {"timestamp": "13:30:00", "price": 23513.75, "action": "touch", "context": "Session opens with expansion lower from order block interaction", "normalized_price": 0.8926417370325693, "pct_from_open": 0.03297030545392666, "pct_from_high": 10.735826296743065, "pct_from_low": 89.26417370325693, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 180, "price_momentum": -0.04729036817411357, "range_position": 0.8926417370325693, "absolute_price": 23513.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348464", "price_level": 23513.75}, {"timestamp": "13:33:00", "price": 23506.25, "action": "touch", "context": "PM Session First Presentation FPFVG premium high created", "normalized_price": 0.8202653799758746, "pct_from_open": 0.0010635582404492471, "pct_from_high": 17.973462002412546, "pct_from_low": 82.02653799758745, "time_since_session_open": 180, "normalized_time": 0.018867924528301886, "time_to_next_event": 120, "price_momentum": -0.031896230928711924, "range_position": 0.8202653799758746, "absolute_price": 23506.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348506", "price_level": 23506.25}, {"timestamp": "13:35:00", "price": 23494.875, "action": "delivery", "context": "Today's Lunch FPFVG and Previous day's Pre-market FPFVG redelivered", "normalized_price": 0.7104945717732207, "pct_from_open": -0.04732834169999149, "pct_from_high": 28.950542822677928, "pct_from_low": 71.04945717732207, "time_since_session_open": 300, "normalized_time": 0.031446540880503145, "time_to_next_event": 180, "price_momentum": -0.048391385269875034, "range_position": 0.7104945717732207, "absolute_price": 23494.875, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348547", "price_level": 23494.875}, {"timestamp": "13:38:00", "price": 23485.0, "action": "delivery", "context": "Multiple FPFVG redeliveries: Today's Lunch, Previous day's Lunch, Previous day's PM", "normalized_price": 0.6151990349819059, "pct_from_open": -0.08933889219773675, "pct_from_high": 38.480096501809406, "pct_from_low": 61.51990349819059, "time_since_session_open": 480, "normalized_time": 0.050314465408805034, "time_to_next_event": 720, "price_momentum": -0.04203044280933608, "range_position": 0.6151990349819059, "absolute_price": 23485.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348592", "price_level": 23485.0}, {"timestamp": "13:50:00", "price": 23462.0, "action": "touch", "context": "First major expansion lower ends", "normalized_price": 0.39324487334137515, "pct_from_open": -0.18718625031906747, "pct_from_high": 60.675512665862485, "pct_from_low": 39.324487334137515, "time_since_session_open": 1200, "normalized_time": 0.12578616352201258, "time_to_next_event": 840, "price_momentum": -0.09793485203321269, "range_position": 0.39324487334137515, "absolute_price": 23462.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348634", "price_level": 23462.0}, {"timestamp": "14:04:00", "price": 23502.5, "action": "touch", "context": "Retracement phase ends, new expansion lower initiated", "normalized_price": 0.7840772014475271, "pct_from_open": -0.01488981536628946, "pct_from_high": 21.592279855247288, "pct_from_low": 78.40772014475272, "time_since_session_open": 2040, "normalized_time": 0.2138364779874214, "time_to_next_event": 480, "price_momentum": 0.17261955502514706, "range_position": 0.7840772014475271, "absolute_price": 23502.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348675", "price_level": 23502.5}, {"timestamp": "14:12:00", "price": 23482.75, "action": "delivery", "context": "Previous day's Lunch FPFVG redelivered again", "normalized_price": 0.5934861278648975, "pct_from_open": -0.09891091636177998, "pct_from_high": 40.651387213510255, "pct_from_low": 59.34861278648975, "time_since_session_open": 2520, "normalized_time": 0.2641509433962264, "time_to_next_event": 300, "price_momentum": -0.08403361344537816, "range_position": 0.5934861278648975, "absolute_price": 23482.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348716", "price_level": 23482.75}, {"timestamp": "14:17:00", "price": 23497.75, "action": "delivery", "context": "Multiple redeliveries: Previous day's NY AM, Pre-market, Today's Lunch FPFVGs", "normalized_price": 0.738238841978287, "pct_from_open": -0.03509742193482515, "pct_from_high": 26.17611580217129, "pct_from_low": 73.8238841978287, "time_since_session_open": 2820, "normalized_time": 0.29559748427672955, "time_to_next_event": 360, "price_momentum": 0.06387667543196601, "range_position": 0.738238841978287, "absolute_price": 23497.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348757", "price_level": 23497.75}, {"timestamp": "14:23:00", "price": 23467.75, "action": "touch", "context": "Failure expansion swing, deeper retracement initiated", "normalized_price": 0.4487334137515078, "pct_from_open": -0.16272441078873479, "pct_from_high": 55.12665862484921, "pct_from_low": 44.87334137515078, "time_since_session_open": 3180, "normalized_time": 0.3333333333333333, "time_to_next_event": 1020, "price_momentum": -0.12767179836367312, "range_position": 0.4487334137515078, "absolute_price": 23467.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348803", "price_level": 23467.75}, {"timestamp": "14:40:00", "price": 23507.5, "action": "delivery", "context": "Today's PM Session FPFVG redelivered, takes short-term high, order block redelivered", "normalized_price": 0.8323281061519904, "pct_from_open": 0.0063813494426954815, "pct_from_high": 16.767189384800965, "pct_from_low": 83.23281061519904, "time_since_session_open": 4200, "normalized_time": 0.44025157232704404, "time_to_next_event": 1020, "price_momentum": 0.16938138509230752, "range_position": 0.8323281061519904, "absolute_price": 23507.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348850", "price_level": 23507.5}, {"timestamp": "14:57:00", "price": 23473.75, "action": "touch", "context": "Expansion lower ends, consolidation initiated", "normalized_price": 0.5066344993968637, "pct_from_open": -0.13719901301795287, "pct_from_high": 49.336550060313634, "pct_from_low": 50.663449939686366, "time_since_session_open": 5220, "normalized_time": 0.5471698113207547, "time_to_next_event": 1260, "price_momentum": -0.14357120068063384, "range_position": 0.5066344993968637, "absolute_price": 23473.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348893", "price_level": 23473.75}, {"timestamp": "15:18:00", "price": 23472.25, "action": "touch", "context": "Brief expansion lower from consolidation", "normalized_price": 0.49215922798552475, "pct_from_open": -0.14358036246064834, "pct_from_high": 50.78407720144753, "pct_from_low": 49.21592279855248, "time_since_session_open": 6480, "normalized_time": 0.6792452830188679, "time_to_next_event": 120, "price_momentum": -0.006390116619628308, "range_position": 0.49215922798552475, "absolute_price": 23472.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348935", "price_level": 23472.25}, {"timestamp": "15:20:00", "price": 23480.0, "action": "delivery", "context": "Previous day's PM Session FPFVG redelivered during retracement", "normalized_price": 0.5669481302774427, "pct_from_open": -0.11061005700672168, "pct_from_high": 43.30518697225573, "pct_from_low": 56.694813027744274, "time_since_session_open": 6600, "normalized_time": 0.6918238993710691, "time_to_next_event": 180, "price_momentum": 0.033017712405074076, "range_position": 0.5669481302774427, "absolute_price": 23480.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.348976", "price_level": 23480.0}, {"timestamp": "15:23:00", "price": 23463.0, "action": "touch", "context": "New expansion lower ends", "normalized_price": 0.4028950542822678, "pct_from_open": -0.18293201735727047, "pct_from_high": 59.71049457177322, "pct_from_low": 40.28950542822678, "time_since_session_open": 6780, "normalized_time": 0.710691823899371, "time_to_next_event": 780, "price_momentum": -0.07240204429301533, "range_position": 0.4028950542822678, "absolute_price": 23463.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349020", "price_level": 23463.0}, {"timestamp": "15:36:00", "price": 23438.25, "action": "sweep", "context": "Today's AM session low taken out", "normalized_price": 0.1640530759951749, "pct_from_open": -0.288224283161746, "pct_from_high": 83.5946924004825, "pct_from_low": 16.405307599517492, "time_since_session_open": 7560, "normalized_time": 0.7924528301886793, "time_to_next_event": 0, "price_momentum": -0.10548523206751054, "range_position": 0.1640530759951749, "absolute_price": 23438.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349063", "price_level": 23438.25}, {"timestamp": "15:36:00", "price": 23450.75, "action": "sweep", "context": "Previous day's AM session low taken out", "normalized_price": 0.28468033775633295, "pct_from_open": -0.2350463711392836, "pct_from_high": 71.5319662243667, "pct_from_low": 28.468033775633295, "time_since_session_open": 7560, "normalized_time": 0.7924528301886793, "time_to_next_event": 180, "price_momentum": 0.05333162672127825, "range_position": 0.28468033775633295, "absolute_price": 23450.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349105", "price_level": 23450.75}, {"timestamp": "15:39:00", "price": 23421.25, "action": "touch", "context": "PM session low created, multiple historical FPFVG redeliveries, reversal point", "normalized_price": 0.0, "pct_from_open": -0.36054624351229475, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 7740, "normalized_time": 0.8113207547169812, "time_to_next_event": 900, "price_momentum": -0.12579555024892594, "range_position": 0.0, "absolute_price": 23421.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349146", "price_level": 23421.25}, {"timestamp": "15:54:00", "price": 23463.25, "action": "touch", "context": "Expansion higher from session low ends", "normalized_price": 0.40530759951749096, "pct_from_open": -0.18186845911682123, "pct_from_high": 59.4692400482509, "pct_from_low": 40.5307599517491, "time_since_session_open": 8640, "normalized_time": 0.9056603773584906, "time_to_next_event": 180, "price_momentum": 0.179324331536532, "range_position": 0.40530759951749096, "absolute_price": 23463.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349187", "price_level": 23463.25}, {"timestamp": "15:57:00", "price": 23434.5, "action": "touch", "context": "Retracement lower ends", "normalized_price": 0.1278648974668275, "pct_from_open": -0.3041776567684846, "pct_from_high": 87.21351025331725, "pct_from_low": 12.78648974668275, "time_since_session_open": 8820, "normalized_time": 0.9245283018867925, "time_to_next_event": 240, "price_momentum": -0.12253204479345359, "range_position": 0.1278648974668275, "absolute_price": 23434.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349228", "price_level": 23434.5}, {"timestamp": "16:01:00", "price": 23462.5, "action": "touch", "context": "Final expansion higher ends", "normalized_price": 0.39806996381182147, "pct_from_open": -0.185059133838169, "pct_from_high": 60.193003618817855, "pct_from_low": 39.806996381182145, "time_since_session_open": 9060, "normalized_time": 0.949685534591195, "time_to_next_event": 480, "price_momentum": 0.11948196035759244, "range_position": 0.39806996381182147, "absolute_price": 23462.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349270", "price_level": 23462.5}, {"timestamp": "16:09:00", "price": 23451.75, "action": "touch", "context": "PM session closing price", "normalized_price": 0.2943305186972256, "pct_from_open": -0.23079213817748662, "pct_from_high": 70.56694813027744, "pct_from_low": 29.43305186972256, "time_since_session_open": 9540, "normalized_time": 1.0, "time_to_next_event": 0, "price_momentum": -0.04581779435269046, "range_position": 0.2943305186972256, "absolute_price": 23451.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.349301", "price_level": 23451.75}], "session_liquidity_events": [{"timestamp": "13:30:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "16:09:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "13:30:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "16:09:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "13:20:00", "event_type": "interaction", "liquidity_type": "fpfvg", "target_level": "session_today's asia first presentation fpfvg redelivered before session open", "magnitude": "high", "context": "generated_from_today's asia first presentation fpfvg redelivered before session open"}, {"timestamp": "13:30:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session opens with expansion lower from order block interaction", "magnitude": "medium", "context": "generated_from_session opens with expansion lower from order block interaction"}, {"timestamp": "13:33:00", "event_type": "rebalance", "liquidity_type": "fpfvg", "target_level": "session_pm session first presentation fpfvg premium high created", "magnitude": "low", "context": "generated_from_pm session first presentation fpfvg premium high created"}, {"timestamp": "13:35:00", "event_type": "interaction", "liquidity_type": "cross_session", "target_level": "session_today's lunch fpfvg and previous day's pre-market fpfvg redelivered", "magnitude": "high", "context": "generated_from_today's lunch fpfvg and previous day's pre-market fpfvg redelivered"}, {"timestamp": "13:38:00", "event_type": "interaction", "liquidity_type": "cross_session", "target_level": "session_multiple fpfvg redeliveries: today's lunch, previous day's lunch, previous day's pm", "magnitude": "high", "context": "generated_from_multiple fpfvg redeliveries: today's lunch, previous day's lunch, previous day's pm"}, {"timestamp": "13:50:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_first major expansion lower ends", "magnitude": "medium", "context": "generated_from_first major expansion lower ends"}, {"timestamp": "14:04:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_retracement phase ends, new expansion lower initiated", "magnitude": "medium", "context": "generated_from_retracement phase ends, new expansion lower initiated"}, {"timestamp": "14:12:00", "event_type": "interaction", "liquidity_type": "cross_session", "target_level": "session_previous day's lunch fpfvg redelivered again", "magnitude": "high", "context": "generated_from_previous day's lunch fpfvg redelivered again"}, {"timestamp": "14:17:00", "event_type": "interaction", "liquidity_type": "cross_session", "target_level": "session_multiple redeliveries: previous day's ny am, pre-market, today's lunch fpfvgs", "magnitude": "high", "context": "generated_from_multiple redeliveries: previous day's ny am, pre-market, today's lunch fpfvgs"}, {"timestamp": "14:23:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_failure expansion swing, deeper retracement initiated", "magnitude": "medium", "context": "generated_from_failure expansion swing, deeper retracement initiated"}, {"timestamp": "14:40:00", "event_type": "rebalance", "liquidity_type": "fpfvg", "target_level": "session_today's pm session fpfvg redelivered, takes short-term high, order block redelivered", "magnitude": "low", "context": "generated_from_today's pm session fpfvg redelivered, takes short-term high, order block redelivered"}, {"timestamp": "14:57:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_expansion lower ends, consolidation initiated", "magnitude": "low", "context": "generated_from_expansion lower ends, consolidation initiated"}, {"timestamp": "15:18:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_brief expansion lower from consolidation", "magnitude": "medium", "context": "generated_from_brief expansion lower from consolidation"}, {"timestamp": "15:20:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "session_previous day's pm session fpfvg redelivered during retracement", "magnitude": "medium", "context": "generated_from_previous day's pm session fpfvg redelivered during retracement"}, {"timestamp": "15:23:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_new expansion lower ends", "magnitude": "medium", "context": "generated_from_new expansion lower ends"}, {"timestamp": "15:36:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_today's am session low taken out", "magnitude": "low", "context": "generated_from_today's am session low taken out"}, {"timestamp": "15:36:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "session_previous day's am session low taken out", "magnitude": "low", "context": "generated_from_previous day's am session low taken out"}, {"timestamp": "15:39:00", "event_type": "redelivery", "liquidity_type": "fpfvg", "target_level": "session_pm session low created, multiple historical fpfvg redeliveries, reversal point", "magnitude": "low", "context": "generated_from_pm session low created, multiple historical fpfvg redeliveries, reversal point"}, {"timestamp": "15:54:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_expansion higher from session low ends", "magnitude": "low", "context": "generated_from_expansion higher from session low ends"}, {"timestamp": "15:57:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_retracement lower ends", "magnitude": "low", "context": "generated_from_retracement lower ends"}, {"timestamp": "16:01:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_final expansion higher ends", "magnitude": "medium", "context": "generated_from_final expansion higher ends"}, {"timestamp": "16:09:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_pm session closing price", "magnitude": "medium", "context": "generated_from_pm session closing price"}], "energy_state": {"energy_density": 0.95, "total_accumulated": 79.5, "energy_rate": 30.0, "energy_source": "standardization_estimation", "session_duration": 159, "expansion_phases": 8, "retracement_phases": 6, "consolidation_phases": 5, "phase_transitions": 25, "session_status": "complete"}, "contamination_analysis": {"htf_contamination": {"immediate_cross_session_interaction": false, "htf_carryover_strength": 0.99, "cross_session_inheritance": 0.3}, "cross_session_inheritance": {"energy_carryover_coefficient": 0.3}}, "phase_transitions": [{"phase_type": "expansion", "start_time": "13:30:00", "end_time": "13:50:00", "high": 23513.75, "low": 23462.0, "description": "Major expansion lower from session open with multiple FPFVG redeliveries"}, {"phase_type": "expansion", "start_time": "14:04:00", "end_time": "14:12:00", "high": 23502.5, "low": 23482.75, "description": "Expansion lower with lunch FPFVG redelivery"}, {"phase_type": "expansion", "start_time": "14:17:00", "end_time": "14:23:00", "high": 23497.75, "low": 23467.75, "description": "Failure expansion swing"}, {"phase_type": "expansion", "start_time": "14:40:00", "end_time": "14:57:00", "high": 23507.5, "low": 23473.75, "description": "Expansion lower after PM FPFVG redelivery"}, {"phase_type": "consolidation", "start_time": "14:57:00", "end_time": "15:17:00", "high": 23486.0, "low": 23470.0, "description": "Consolidation before final major move"}, {"phase_type": "expansion", "start_time": "15:17:00", "end_time": "15:18:00", "high": 23484.0, "low": 23472.25, "description": "Brief expansion lower"}, {"phase_type": "expansion", "start_time": "15:20:00", "end_time": "15:23:00", "high": 23480.0, "low": 23463.0, "description": "Expansion lower after PM FPFVG redelivery"}, {"phase_type": "expansion", "start_time": "15:24:00", "end_time": "15:39:00", "high": 23473.75, "low": 23421.25, "description": "Major expansion lower to session low with multiple liquidity sweeps"}, {"phase_type": "expansion", "start_time": "15:39:00", "end_time": "15:54:00", "high": 23463.25, "low": 23421.25, "description": "Expansion higher from session low"}, {"phase_type": "expansion", "start_time": "15:57:00", "end_time": "16:01:00", "high": 23462.5, "low": 23434.5, "description": "Final expansion higher"}, {"phase_type": "consolidation", "start_time": "16:01:00", "end_time": "16:09:00", "high": 23462.5, "low": 23447.0, "description": "Final consolidation to session close"}], "structures_identified": {"fair_value_gaps": [{"id": "pm_fpfvg_2025_07_29", "formation_time": "13:33:00", "type": "first_presentation", "premium_high": 23506.25, "discount_low": 23505.0, "size_points": 1.25, "formation_context": "PM session first presentation FPFVG during initial expansion", "delivery_status": "delivered", "delivery_time": "14:40:00"}], "session_levels": [{"type": "session_high", "level": 23513.75, "formation_time": "13:30:00", "touches": ["13:30:00"], "holds": true, "context": "PM session high at session open with order block interaction"}, {"type": "session_low", "level": 23421.25, "formation_time": "15:39:00", "touches": ["15:39:00"], "holds": true, "context": "PM session low with multiple liquidity sweeps and historical FPFVG redeliveries"}], "order_blocks": [{"id": "pm_order_block_2025_07_29", "formation_time": "13:30:00", "high": 23513.75, "low": 23505.75, "delivery_status": "delivered", "delivery_time": "14:40:00"}]}, "level_interactions": [{"timestamp": "13:35:00", "level": 23494.875, "level_origin": "lunch_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "14:12:00", "level": 23482.5, "level_origin": "previous_day_lunch_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "14:17:00", "level": 23495.25, "level_origin": "previous_day_nyam_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "14:40:00", "level": 23505.625, "level_origin": "pm_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "15:20:00", "level": 23477.25, "level_origin": "previous_day_pm_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "15:36:00", "level": 23438.25, "level_origin": "nyam_session_low", "interaction_type": "sweep", "result": "broken"}, {"timestamp": "15:36:00", "level": 23450.75, "level_origin": "previous_day_nyam_low", "interaction_type": "sweep", "result": "broken"}], "consolidation_expansion_raw": {"consolidation_periods": [{"start": "14:57:00", "end": "15:17:00", "range_high": 23486.0, "range_low": 23470.0, "touches_high": 1, "touches_low": 2}, {"start": "16:01:00", "end": "16:09:00", "range_high": 23462.5, "range_low": 23447.0, "touches_high": 1, "touches_low": 1}], "expansion_periods": [{"start": "13:30:00", "end": "13:50:00", "direction": "down", "start_price": 23513.75, "end_price": 23462.0, "total_distance": 51.75}, {"start": "13:50:00", "end": "14:04:00", "direction": "up", "start_price": 23462.0, "end_price": 23502.5, "total_distance": 40.5}, {"start": "14:04:00", "end": "14:12:00", "direction": "down", "start_price": 23502.5, "end_price": 23482.75, "total_distance": 19.75}, {"start": "14:12:00", "end": "14:17:00", "direction": "up", "start_price": 23482.75, "end_price": 23497.75, "total_distance": 15.0}, {"start": "14:17:00", "end": "14:23:00", "direction": "down", "start_price": 23497.75, "end_price": 23467.75, "total_distance": 30.0}, {"start": "14:23:00", "end": "14:40:00", "direction": "up", "start_price": 23467.75, "end_price": 23507.5, "total_distance": 39.75}, {"start": "14:40:00", "end": "14:57:00", "direction": "down", "start_price": 23507.5, "end_price": 23473.75, "total_distance": 33.75}, {"start": "15:24:00", "end": "15:39:00", "direction": "down", "start_price": 23473.75, "end_price": 23421.25, "total_distance": 52.5}, {"start": "15:39:00", "end": "15:54:00", "direction": "up", "start_price": 23421.25, "end_price": 23463.25, "total_distance": 42.0}, {"start": "15:57:00", "end": "16:01:00", "direction": "up", "start_price": 23434.5, "end_price": 23462.5, "total_distance": 28.0}]}, "fpfvg_observations": {"session_fpfvg_created": true, "previous_fpfvg_interactions": [{"gap_origin": "asia_session", "interaction_time": "13:20:00", "interaction_type": "complete_fill", "price_entered": 23524.875, "result": "respected"}, {"gap_origin": "lunch_session", "interaction_time": "13:35:00", "interaction_type": "complete_fill", "price_entered": 23494.875, "result": "respected"}, {"gap_origin": "previous_day_premarket", "interaction_time": "13:35:00", "interaction_type": "complete_fill", "price_entered": 23602.125, "result": "respected"}, {"gap_origin": "previous_day_lunch", "interaction_time": "13:38:00", "interaction_type": "complete_fill", "price_entered": 23482.5, "result": "respected"}, {"gap_origin": "previous_day_pm", "interaction_time": "13:38:00", "interaction_type": "complete_fill", "price_entered": 23477.25, "result": "respected"}, {"gap_origin": "three_day_midnight", "interaction_time": "15:39:00", "interaction_type": "complete_fill", "price_entered": 23551.0, "result": "respected"}, {"gap_origin": "three_day_lunch", "interaction_time": "15:39:00", "interaction_type": "complete_fill", "price_entered": 23482.5, "result": "respected"}]}, "behavioral_observations": {"session_type_observed": "trending", "directional_attempts": [{"time": "13:30:00", "direction": "down", "outcome": "successful"}, {"time": "14:23:00", "direction": "up", "outcome": "successful"}, {"time": "14:40:00", "direction": "down", "outcome": "successful"}, {"time": "15:24:00", "direction": "down", "outcome": "successful"}, {"time": "15:39:00", "direction": "up", "outcome": "successful"}], "institutional_activity": "extremely_high_activity_with_major_liquidity_sweeps_and_extensive_historical_gap_redeliveries", "session_completion": "complete"}, "micro_timing_analysis": {"cascade_events": [{"timestamp": "15:17:00", "event_type": "heuristic_cascade_initiation", "confidence": 3, "trigger_source": "Phase Transition"}]}, "processing_metadata": {"original_file": "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYPM_Lvl-1_2025_07_29.json", "standardization_date": "2025-08-07T19:29:57.255732", "conversion_type": "legacy_schema_converted", "schema_version": "target_v1.0", "data_recovery": {"recovery_date": "2025-08-07T19:45:57.278387", "corrections_applied": ["Recovered 30 price movements", "Added recovered price movements"], "recovery_source": "backup_file_recovery"}, "relativity_enhancement": {"applied": true, "timestamp": "2025-08-14T13:06:58.349314", "features_added": ["normalized_price", "pct_from_open", "pct_from_high", "pct_from_low", "time_since_session_open", "normalized_time", "time_to_next_event", "price_momentum", "range_position", "absolute_price"], "permanent_validity": true, "regime_independence": true}}, "phase2_enhancement": {"enhancement_date": "2025-08-14T12:15:27.230506", "enhancement_version": "phase2_v1.0", "features_enhanced": ["htf_carryover_strength", "energy_density", "session_liquidity_events"], "authenticity_method": "market_derived_calculations", "pre_enhancement_score": 0.0, "post_enhancement_score": 100.0}, "relativity_stats": {"session_high": 23524.875, "session_low": 23421.25, "session_open": 23506.0, "session_close": 23451.75, "session_range": 103.625, "session_duration_seconds": 9540, "normalization_applied": true, "structural_relationships_enabled": true, "permanent_pattern_capability": true}}}}, "success": true, "node_count": 61, "edge_count": 353}}, "enhanced_rel_ASIA_Lvl-1_2025_07_30.json": {"session_name": "enhanced_rel_ASIA_Lvl-1_2025_07_30.json", "discovery_success": true, "patterns_found": 5, "patterns": [{"type": "temporal_structural", "description": "Price position 18.0% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "asia", "structural_position": 0.18028169014084508, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 100.0% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "asia", "structural_position": 1.0, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 0.0% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "asia", "structural_position": 0.0, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 96.6% at 17940 minutes", "confidence": 0.7, "time_span_hours": 4.983333333333333, "session": "asia", "structural_position": 0.9661971830985916, "temporal_position": 17940}, {"type": "temporal_structural", "description": "Price position 18.0% at 120 minutes", "confidence": 0.7, "time_span_hours": 0.03333333333333333, "session": "asia", "structural_position": 0.18028169014084508, "temporal_position": 120}], "embeddings_shape": "<PERSON>.<PERSON><PERSON>([26, 256])", "graph_info": {"graph": {"nodes": {"1m": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12], "5m": [], "15m": [13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25], "1h": [], "D": [], "W": []}, "rich_node_features": [{"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}], "edges": {"temporal": [{"source": 1, "target": 2, "feature_idx": 0}, {"source": 2, "target": 0, "feature_idx": 1}, {"source": 0, "target": 4, "feature_idx": 2}, {"source": 4, "target": 5, "feature_idx": 3}, {"source": 5, "target": 6, "feature_idx": 4}, {"source": 6, "target": 7, "feature_idx": 5}, {"source": 7, "target": 8, "feature_idx": 6}, {"source": 8, "target": 9, "feature_idx": 7}, {"source": 9, "target": 10, "feature_idx": 8}, {"source": 10, "target": 11, "feature_idx": 9}, {"source": 11, "target": 12, "feature_idx": 10}, {"source": 12, "target": 3, "feature_idx": 11}, {"source": 14, "target": 15, "feature_idx": 12}, {"source": 15, "target": 13, "feature_idx": 13}, {"source": 13, "target": 17, "feature_idx": 14}, {"source": 17, "target": 18, "feature_idx": 15}, {"source": 18, "target": 19, "feature_idx": 16}, {"source": 19, "target": 20, "feature_idx": 17}, {"source": 20, "target": 21, "feature_idx": 18}, {"source": 21, "target": 22, "feature_idx": 19}, {"source": 22, "target": 23, "feature_idx": 20}, {"source": 23, "target": 24, "feature_idx": 21}, {"source": 24, "target": 25, "feature_idx": 22}, {"source": 25, "target": 16, "feature_idx": 23}], "scale": [], "cascade": [], "pd_array": [], "cross_tf_confluence": [], "temporal_echo": [{"source": 0, "target": 13, "feature_idx": 24}, {"source": 0, "target": 17, "feature_idx": 25}, {"source": 0, "target": 18, "feature_idx": 26}, {"source": 0, "target": 19, "feature_idx": 27}, {"source": 0, "target": 20, "feature_idx": 28}, {"source": 0, "target": 21, "feature_idx": 29}, {"source": 0, "target": 22, "feature_idx": 30}, {"source": 1, "target": 14, "feature_idx": 31}, {"source": 2, "target": 15, "feature_idx": 32}, {"source": 3, "target": 16, "feature_idx": 33}, {"source": 3, "target": 25, "feature_idx": 34}, {"source": 4, "target": 13, "feature_idx": 35}, {"source": 4, "target": 17, "feature_idx": 36}, {"source": 4, "target": 18, "feature_idx": 37}, {"source": 4, "target": 19, "feature_idx": 38}, {"source": 4, "target": 20, "feature_idx": 39}, {"source": 4, "target": 21, "feature_idx": 40}, {"source": 4, "target": 22, "feature_idx": 41}, {"source": 5, "target": 13, "feature_idx": 42}, {"source": 5, "target": 17, "feature_idx": 43}, {"source": 5, "target": 18, "feature_idx": 44}, {"source": 5, "target": 19, "feature_idx": 45}, {"source": 5, "target": 20, "feature_idx": 46}, {"source": 5, "target": 21, "feature_idx": 47}, {"source": 5, "target": 22, "feature_idx": 48}, {"source": 6, "target": 13, "feature_idx": 49}, {"source": 6, "target": 17, "feature_idx": 50}, {"source": 6, "target": 18, "feature_idx": 51}, {"source": 6, "target": 19, "feature_idx": 52}, {"source": 6, "target": 20, "feature_idx": 53}, {"source": 6, "target": 21, "feature_idx": 54}, {"source": 6, "target": 22, "feature_idx": 55}, {"source": 6, "target": 23, "feature_idx": 56}, {"source": 7, "target": 13, "feature_idx": 57}, {"source": 7, "target": 17, "feature_idx": 58}, {"source": 7, "target": 18, "feature_idx": 59}, {"source": 7, "target": 19, "feature_idx": 60}, {"source": 7, "target": 20, "feature_idx": 61}, {"source": 7, "target": 21, "feature_idx": 62}, {"source": 7, "target": 22, "feature_idx": 63}, {"source": 7, "target": 23, "feature_idx": 64}, {"source": 8, "target": 13, "feature_idx": 65}, {"source": 8, "target": 17, "feature_idx": 66}, {"source": 8, "target": 18, "feature_idx": 67}, {"source": 8, "target": 19, "feature_idx": 68}, {"source": 8, "target": 20, "feature_idx": 69}, {"source": 8, "target": 21, "feature_idx": 70}, {"source": 8, "target": 22, "feature_idx": 71}, {"source": 8, "target": 23, "feature_idx": 72}, {"source": 8, "target": 24, "feature_idx": 73}, {"source": 9, "target": 13, "feature_idx": 74}, {"source": 9, "target": 17, "feature_idx": 75}, {"source": 9, "target": 18, "feature_idx": 76}, {"source": 9, "target": 19, "feature_idx": 77}, {"source": 9, "target": 20, "feature_idx": 78}, {"source": 9, "target": 21, "feature_idx": 79}, {"source": 9, "target": 22, "feature_idx": 80}, {"source": 9, "target": 23, "feature_idx": 81}, {"source": 9, "target": 24, "feature_idx": 82}, {"source": 10, "target": 19, "feature_idx": 83}, {"source": 10, "target": 20, "feature_idx": 84}, {"source": 10, "target": 21, "feature_idx": 85}, {"source": 10, "target": 22, "feature_idx": 86}, {"source": 10, "target": 23, "feature_idx": 87}, {"source": 10, "target": 24, "feature_idx": 88}, {"source": 11, "target": 21, "feature_idx": 89}, {"source": 11, "target": 22, "feature_idx": 90}, {"source": 11, "target": 23, "feature_idx": 91}, {"source": 11, "target": 24, "feature_idx": 92}, {"source": 12, "target": 16, "feature_idx": 93}, {"source": 12, "target": 25, "feature_idx": 94}], "discovered": [{"source": 1, "target": 19, "feature_idx": 95}, {"source": 1, "target": 20, "feature_idx": 96}, {"source": 1, "target": 22, "feature_idx": 97}, {"source": 1, "target": 23, "feature_idx": 98}, {"source": 3, "target": 19, "feature_idx": 99}, {"source": 3, "target": 20, "feature_idx": 100}, {"source": 3, "target": 22, "feature_idx": 101}, {"source": 3, "target": 23, "feature_idx": 102}, {"source": 12, "target": 19, "feature_idx": 103}, {"source": 12, "target": 20, "feature_idx": 104}, {"source": 12, "target": 22, "feature_idx": 105}, {"source": 12, "target": 23, "feature_idx": 106}], "structural_context": []}, "rich_edge_features": [{"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}], "metadata": {"session_id": "unknown", "total_nodes": 26, "timeframe_counts": {"1m": 13, "5m": 0, "15m": 13, "1h": 0, "D": 0, "W": 0}, "feature_dimensions": 37, "preserved_raw": {"session_metadata": {"session_type": "asia", "session_date": "2025-07-30", "session_start": "19:00:00", "session_end": "23:59:00", "session_duration": 299, "transcription_source": "live_market_data", "data_completeness": "complete_session", "timezone": "ET", "session_status": "completed"}, "session_fpfvg": {"fpfvg_present": false, "fpfvg_formation": {"formation_time": "00:00:00", "premium_high": 0.0, "discount_low": 0.0, "gap_size": 0.0, "interactions": []}}, "price_movements": [{"timestamp": "19:00:00", "price_level": 23728.0, "movement_type": "open", "normalized_price": 0.18028169014084508, "pct_from_open": 0.0, "pct_from_high": 81.97183098591549, "pct_from_low": 18.028169014084508, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": -25200, "price_momentum": 0.0, "range_position": 0.18028169014084508, "absolute_price": 23728.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463547"}, {"timestamp": "12:00:00", "price_level": 23800.75, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.30659979770734996, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 0, "normalized_time": -1.4046822742474916, "time_to_next_event": 1800, "price_momentum": 0.30659979770734996, "range_position": 1.0, "absolute_price": 23800.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463598"}, {"timestamp": "12:30:00", "price_level": 23712.0, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.06743088334457181, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 0, "normalized_time": -1.3043478260869565, "time_to_next_event": 41340, "price_momentum": -0.37288740901022027, "range_position": 0.0, "absolute_price": 23712.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463643"}, {"timestamp": "23:59:00", "price_level": 23797.75, "movement_type": "close", "normalized_price": 0.9661971830985916, "pct_from_open": 0.29395650708024273, "pct_from_high": 3.3802816901408446, "pct_from_low": 96.61971830985917, "time_since_session_open": 17940, "normalized_time": 1.0, "time_to_next_event": -17820, "price_momentum": 0.36163124156545207, "range_position": 0.9661971830985916, "absolute_price": 23797.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463685"}, {"timestamp": "19:02:00", "price": 23728.0, "action": "touch", "context": "Asia_FPFVG_formation_premium_high", "normalized_price": 0.18028169014084508, "pct_from_open": 0.0, "pct_from_high": 81.97183098591549, "pct_from_low": 18.028169014084508, "time_since_session_open": 120, "normalized_time": 0.006688963210702341, "time_to_next_event": 60, "price_momentum": -0.29309493544557785, "range_position": 0.18028169014084508, "absolute_price": 23728.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463729", "price_level": 23728.0}, {"timestamp": "19:03:00", "price": 23725.5, "action": "touch", "context": "expansion_initiation_from_discount_low", "normalized_price": 0.15211267605633802, "pct_from_open": -0.010536075522589346, "pct_from_high": 84.78873239436619, "pct_from_low": 15.211267605633802, "time_since_session_open": 180, "normalized_time": 0.010033444816053512, "time_to_next_event": 120, "price_momentum": -0.010536075522589346, "range_position": 0.15211267605633802, "absolute_price": 23725.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463772", "price_level": 23725.5}, {"timestamp": "19:05:00", "price": 23728.0, "action": "touch", "context": "Asia_FPFVG_rebalanced", "normalized_price": 0.18028169014084508, "pct_from_open": 0.0, "pct_from_high": 81.97183098591549, "pct_from_low": 18.028169014084508, "time_since_session_open": 300, "normalized_time": 0.016722408026755852, "time_to_next_event": 240, "price_momentum": 0.01053718572843565, "range_position": 0.18028169014084508, "absolute_price": 23728.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463813", "price_level": 23728.0}, {"timestamp": "19:09:00", "price": 23728.0, "action": "touch", "context": "Asia_FPFVG_redelivered", "normalized_price": 0.18028169014084508, "pct_from_open": 0.0, "pct_from_high": 81.97183098591549, "pct_from_low": 18.028169014084508, "time_since_session_open": 540, "normalized_time": 0.030100334448160536, "time_to_next_event": 780, "price_momentum": 0.0, "range_position": 0.18028169014084508, "absolute_price": 23728.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463854", "price_level": 23728.0}, {"timestamp": "19:22:00", "price": 23726.5, "action": "touch", "context": "Asia_FPFVG_redelivered_on_retracement", "normalized_price": 0.16338028169014085, "pct_from_open": -0.006321645313553607, "pct_from_high": 83.66197183098592, "pct_from_low": 16.338028169014084, "time_since_session_open": 1320, "normalized_time": 0.07357859531772576, "time_to_next_event": 2220, "price_momentum": -0.006321645313553607, "range_position": 0.16338028169014085, "absolute_price": 23726.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463896", "price_level": 23726.5}, {"timestamp": "19:59:00", "price": 23728.0, "action": "touch", "context": "Asia_FPFVG_rebalanced_again", "normalized_price": 0.18028169014084508, "pct_from_open": 0.0, "pct_from_high": 81.97183098591549, "pct_from_low": 18.028169014084508, "time_since_session_open": 3540, "normalized_time": 0.19732441471571907, "time_to_next_event": 180, "price_momentum": 0.006322044970813226, "range_position": 0.18028169014084508, "absolute_price": 23728.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463937", "price_level": 23728.0}, {"timestamp": "20:02:00", "price": 23725.5, "action": "touch", "context": "Asia_FPFVG_redelivered_final", "normalized_price": 0.15211267605633802, "pct_from_open": -0.010536075522589346, "pct_from_high": 84.78873239436619, "pct_from_low": 15.211267605633802, "time_since_session_open": 3720, "normalized_time": 0.20735785953177258, "time_to_next_event": 300, "price_momentum": -0.010536075522589346, "range_position": 0.15211267605633802, "absolute_price": 23725.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.463978", "price_level": 23725.5}, {"timestamp": "20:07:00", "price": 23712.0, "action": "touch", "context": "Asia_session_low_formation", "normalized_price": 0.0, "pct_from_open": -0.06743088334457181, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 4020, "normalized_time": 0.22408026755852842, "time_to_next_event": 13500, "price_momentum": -0.056900802933552506, "range_position": 0.0, "absolute_price": 23712.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.464019", "price_level": 23712.0}, {"timestamp": "23:52:00", "price": 23800.75, "action": "touch", "context": "Asia_session_high_formation", "normalized_price": 1.0, "pct_from_open": 0.30659979770734996, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 17520, "normalized_time": 0.9765886287625418, "time_to_next_event": 0, "price_momentum": 0.37428306342780027, "range_position": 1.0, "absolute_price": 23800.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.464050", "price_level": 23800.75}], "session_liquidity_events": [{"timestamp": "19:00:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "23:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "19:02:00", "event_type": "rebalance", "liquidity_type": "fpfvg", "target_level": "session_asia_fpfvg_formation_premium_high", "magnitude": "low", "context": "generated_from_asia_fpfvg_formation_premium_high"}, {"timestamp": "19:03:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_expansion_initiation_from_discount_low", "magnitude": "medium", "context": "generated_from_expansion_initiation_from_discount_low"}, {"timestamp": "19:05:00", "event_type": "interaction", "liquidity_type": "fpfvg", "target_level": "session_asia_fpfvg_rebalanced", "magnitude": "high", "context": "generated_from_asia_fpfvg_rebalanced"}, {"timestamp": "19:09:00", "event_type": "interaction", "liquidity_type": "fpfvg", "target_level": "session_asia_fpfvg_redelivered", "magnitude": "high", "context": "generated_from_asia_fpfvg_redelivered"}, {"timestamp": "19:22:00", "event_type": "redelivery", "liquidity_type": "fpfvg", "target_level": "session_asia_fpfvg_redelivered_on_retracement", "magnitude": "medium", "context": "generated_from_asia_fpfvg_redelivered_on_retracement"}, {"timestamp": "19:59:00", "event_type": "interaction", "liquidity_type": "fpfvg", "target_level": "session_asia_fpfvg_rebalanced_again", "magnitude": "high", "context": "generated_from_asia_fpfvg_rebalanced_again"}, {"timestamp": "20:02:00", "event_type": "interaction", "liquidity_type": "fpfvg", "target_level": "session_asia_fpfvg_redelivered_final", "magnitude": "high", "context": "generated_from_asia_fpfvg_redelivered_final"}, {"timestamp": "20:07:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_asia_session_low_formation", "magnitude": "low", "context": "generated_from_asia_session_low_formation"}, {"timestamp": "23:52:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_asia_session_high_formation", "magnitude": "low", "context": "generated_from_asia_session_high_formation"}], "energy_state": {"energy_density": 0.92, "total_accumulated": 149.5, "energy_rate": 30.0, "energy_source": "standardization_estimation", "session_duration": 299, "expansion_phases": 4, "retracement_phases": 3, "consolidation_phases": 2, "phase_transitions": 12, "session_status": "complete"}, "contamination_analysis": {"htf_contamination": {"immediate_cross_session_interaction": false, "htf_carryover_strength": 0.33, "cross_session_inheritance": 0.3}, "cross_session_inheritance": {"energy_carryover_coefficient": 0.3}}, "phase_transitions": [{"phase_type": "expansion", "start_time": "19:03:00", "end_time": "19:14:00", "high": 23740.0, "low": 23725.5, "description": "initial_expansion_higher_from_FPFVG_discount"}, {"phase_type": "consolidation", "start_time": "19:22:00", "end_time": "19:38:00", "high": 23733.25, "low": 23726.5, "description": "tight_consolidation_after_retracement"}, {"phase_type": "expansion", "start_time": "19:38:00", "end_time": "19:46:00", "high": 23736.5, "low": 23726.5, "description": "modest_expansion_higher"}, {"phase_type": "expansion", "start_time": "19:46:00", "end_time": "20:00:00", "high": 23736.5, "low": 23717.25, "description": "expansion_lower_testing_support"}, {"phase_type": "expansion", "start_time": "20:00:00", "end_time": "20:07:00", "high": 23725.5, "low": 23712.0, "description": "expansion_to_session_low"}, {"phase_type": "expansion", "start_time": "20:07:00", "end_time": "20:24:00", "high": 23747.5, "low": 23712.0, "description": "reversal_expansion_higher"}, {"phase_type": "consolidation", "start_time": "20:31:00", "end_time": "20:36:00", "high": 23739.0, "low": 23733.0, "description": "brief_consolidation_mid_session"}, {"phase_type": "expansion", "start_time": "20:36:00", "end_time": "20:44:00", "high": 23755.0, "low": 23734.0, "description": "expansion_higher_building_momentum"}, {"phase_type": "consolidation", "start_time": "20:44:00", "end_time": "21:01:00", "high": 23758.5, "low": 23745.75, "description": "consolidation_at_higher_levels"}, {"phase_type": "expansion", "start_time": "21:01:00", "end_time": "21:46:00", "high": 23780.0, "low": 23746.0, "description": "major_expansion_higher"}, {"phase_type": "expansion", "start_time": "21:57:00", "end_time": "22:57:00", "high": 23793.0, "low": 23767.25, "description": "continued_expansion_to_new_highs"}, {"phase_type": "consolidation", "start_time": "23:07:00", "end_time": "23:33:00", "high": 23788.75, "low": 23778.5, "description": "consolidation_near_session_highs"}, {"phase_type": "expansion", "start_time": "23:33:00", "end_time": "23:52:00", "high": 23800.75, "low": 23783.0, "description": "final_expansion_to_session_high"}, {"phase_type": "consolidation", "start_time": "23:52:00", "end_time": "23:59:00", "high": 23800.75, "low": 23795.25, "description": "end_of_session_consolidation"}], "structures_identified": {"fair_value_gaps": [{"id": "Asia_fpfvg_2025_07_30", "formation_time": "19:02:00", "type": "first_presentation", "premium_high": 23728.0, "discount_low": 23725.5, "size_points": 2.5, "formation_context": "session_opening_expansion_initiation", "delivery_status": "delivered", "delivery_time": "19:09:00"}], "session_levels": [{"type": "session_high", "level": 23800.75, "formation_time": "23:52:00", "touches": ["23:52:00"], "holds": true, "context": "final_expansion_peak"}, {"type": "session_low", "level": 23712.0, "formation_time": "20:07:00", "touches": ["20:07:00"], "holds": true, "context": "mid_session_low_reversal_point"}]}, "level_interactions": [{"timestamp": "19:05:00", "level": 23728.0, "level_origin": "Asia_FPFVG_premium", "interaction_type": "test", "result": "held"}, {"timestamp": "19:09:00", "level": 23728.0, "level_origin": "Asia_FPFVG_premium", "interaction_type": "test", "result": "absorbed"}, {"timestamp": "19:22:00", "level": 23726.5, "level_origin": "Asia_FPFVG_range", "interaction_type": "bounce", "result": "held"}, {"timestamp": "19:59:00", "level": 23728.0, "level_origin": "Asia_FPFVG_premium", "interaction_type": "test", "result": "held"}, {"timestamp": "20:02:00", "level": 23725.5, "level_origin": "Asia_FPFVG_discount", "interaction_type": "bounce", "result": "absorbed"}, {"timestamp": "20:07:00", "level": 23712.0, "level_origin": "session_support", "interaction_type": "test", "result": "held"}], "consolidation_expansion_raw": {"consolidation_periods": [{"start": "19:22:00", "end": "19:38:00", "range_high": 23733.25, "range_low": 23726.5, "touches_high": 1, "touches_low": 2}, {"start": "20:31:00", "end": "20:36:00", "range_high": 23739.0, "range_low": 23733.0, "touches_high": 1, "touches_low": 1}, {"start": "20:44:00", "end": "21:01:00", "range_high": 23758.5, "range_low": 23745.75, "touches_high": 1, "touches_low": 1}, {"start": "23:07:00", "end": "23:33:00", "range_high": 23788.75, "range_low": 23778.5, "touches_high": 1, "touches_low": 2}, {"start": "23:52:00", "end": "23:59:00", "range_high": 23800.75, "range_low": 23795.25, "touches_high": 1, "touches_low": 1}], "expansion_periods": [{"start": "19:03:00", "end": "19:14:00", "direction": "up", "start_price": 23725.5, "end_price": 23740.0, "total_distance": 14.5}, {"start": "19:38:00", "end": "19:46:00", "direction": "up", "start_price": 23726.5, "end_price": 23736.5, "total_distance": 10.0}, {"start": "19:46:00", "end": "20:00:00", "direction": "down", "start_price": 23736.5, "end_price": 23717.25, "total_distance": 19.25}, {"start": "20:00:00", "end": "20:07:00", "direction": "down", "start_price": 23725.5, "end_price": 23712.0, "total_distance": 13.5}, {"start": "20:07:00", "end": "20:24:00", "direction": "up", "start_price": 23712.0, "end_price": 23747.5, "total_distance": 35.5}, {"start": "20:36:00", "end": "20:44:00", "direction": "up", "start_price": 23734.0, "end_price": 23755.0, "total_distance": 21.0}, {"start": "21:01:00", "end": "21:46:00", "direction": "up", "start_price": 23746.0, "end_price": 23780.0, "total_distance": 34.0}, {"start": "21:57:00", "end": "22:57:00", "direction": "up", "start_price": 23767.25, "end_price": 23793.0, "total_distance": 25.75}, {"start": "23:33:00", "end": "23:52:00", "direction": "up", "start_price": 23783.0, "end_price": 23800.75, "total_distance": 17.75}]}, "fpfvg_observations": {"session_fpfvg_created": true, "previous_fpfvg_interactions": []}, "behavioral_observations": {"session_type_observed": "trending", "directional_attempts": [{"time": "19:03:00", "direction": "up", "outcome": "successful"}, {"time": "19:38:00", "direction": "up", "outcome": "partial"}, {"time": "19:46:00", "direction": "down", "outcome": "successful"}, {"time": "20:07:00", "direction": "up", "outcome": "successful"}, {"time": "21:01:00", "direction": "up", "outcome": "successful"}, {"time": "23:33:00", "direction": "up", "outcome": "successful"}], "institutional_activity": "accumulation_with_controlled_retracements", "session_completion": "complete"}, "processing_metadata": {"original_file": "/Users/<USER>/grok-claude-automation/ASIA_Lvl-1_2025_07_31.json", "standardization_date": "2025-08-07T19:29:57.184056", "conversion_type": "legacy_schema_converted", "schema_version": "target_v1.0", "relativity_enhancement": {"applied": true, "timestamp": "2025-08-14T13:06:58.464058", "features_added": ["normalized_price", "pct_from_open", "pct_from_high", "pct_from_low", "time_since_session_open", "normalized_time", "time_to_next_event", "price_momentum", "range_position", "absolute_price"], "permanent_validity": true, "regime_independence": true}}, "phase2_enhancement": {"enhancement_date": "2025-08-14T12:15:27.161096", "enhancement_version": "phase2_v1.0", "features_enhanced": ["htf_carryover_strength", "energy_density", "session_liquidity_events"], "authenticity_method": "market_derived_calculations", "pre_enhancement_score": 0.0, "post_enhancement_score": 100.0}, "relativity_stats": {"session_high": 23800.75, "session_low": 23712.0, "session_open": 23728.0, "session_close": 23800.75, "session_range": 88.75, "session_duration_seconds": 17940, "normalization_applied": true, "structural_relationships_enabled": true, "permanent_pattern_capability": true}}}}, "success": true, "node_count": 26, "edge_count": 107}}, "enhanced_rel_NY_AM_Lvl-1_2025_07_25.json": {"session_name": "enhanced_rel_NY_AM_Lvl-1_2025_07_25.json", "discovery_success": true, "patterns_found": 6, "patterns": [{"type": "temporal_structural", "description": "Price position 29.7% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "ny_am", "structural_position": 0.2969543147208122, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 100.0% at 9000 minutes", "confidence": 0.7, "time_span_hours": 2.5, "session": "ny_am", "structural_position": 1.0, "temporal_position": 9000}, {"type": "temporal_structural", "description": "Price position 0.0% at 10800 minutes", "confidence": 0.7, "time_span_hours": 3.0, "session": "ny_am", "structural_position": 0.0, "temporal_position": 10800}, {"type": "temporal_structural", "description": "Price position 91.6% at 8940 minutes", "confidence": 0.7, "time_span_hours": 2.4833333333333334, "session": "ny_am", "structural_position": 0.916243654822335, "temporal_position": 8940}, {"type": "temporal_structural", "description": "Price position 29.7% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "ny_am", "structural_position": 0.2969543147208122, "temporal_position": 0}, {"type": "htf_confluence", "description": "HTF confluence 0.98 strength with 0.86 energy density", "confidence": 0.86, "time_span_hours": 4.0, "session": "ny_am", "htf_strength": 0.98, "energy_level": 0.86}], "embeddings_shape": "<PERSON>.<PERSON><PERSON>([29, 256])", "graph_info": {"graph": {"nodes": {"1m": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "5m": [], "15m": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "1h": [24, 25, 26, 27, 28], "D": [], "W": []}, "rich_node_features": [{"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}], "edges": {"temporal": [{"source": 0, "target": 4, "feature_idx": 0}, {"source": 4, "target": 8, "feature_idx": 1}, {"source": 8, "target": 9, "feature_idx": 2}, {"source": 9, "target": 10, "feature_idx": 3}, {"source": 10, "target": 11, "feature_idx": 4}, {"source": 11, "target": 3, "feature_idx": 5}, {"source": 3, "target": 7, "feature_idx": 6}, {"source": 7, "target": 1, "feature_idx": 7}, {"source": 1, "target": 5, "feature_idx": 8}, {"source": 5, "target": 2, "feature_idx": 9}, {"source": 2, "target": 6, "feature_idx": 10}, {"source": 12, "target": 16, "feature_idx": 11}, {"source": 16, "target": 20, "feature_idx": 12}, {"source": 20, "target": 21, "feature_idx": 13}, {"source": 21, "target": 22, "feature_idx": 14}, {"source": 22, "target": 23, "feature_idx": 15}, {"source": 23, "target": 15, "feature_idx": 16}, {"source": 15, "target": 19, "feature_idx": 17}, {"source": 19, "target": 13, "feature_idx": 18}, {"source": 13, "target": 17, "feature_idx": 19}, {"source": 17, "target": 14, "feature_idx": 20}, {"source": 14, "target": 18, "feature_idx": 21}, {"source": 24, "target": 25, "feature_idx": 22}, {"source": 25, "target": 26, "feature_idx": 23}, {"source": 26, "target": 27, "feature_idx": 24}, {"source": 27, "target": 28, "feature_idx": 25}], "scale": [{"source": 12, "target": 24, "feature_idx": 26}, {"source": 12, "target": 25, "feature_idx": 27}, {"source": 13, "target": 28, "feature_idx": 28}, {"source": 15, "target": 27, "feature_idx": 29}, {"source": 15, "target": 28, "feature_idx": 30}, {"source": 16, "target": 24, "feature_idx": 31}, {"source": 16, "target": 25, "feature_idx": 32}, {"source": 17, "target": 28, "feature_idx": 33}, {"source": 19, "target": 27, "feature_idx": 34}, {"source": 19, "target": 28, "feature_idx": 35}, {"source": 20, "target": 24, "feature_idx": 36}, {"source": 20, "target": 25, "feature_idx": 37}, {"source": 21, "target": 24, "feature_idx": 38}, {"source": 21, "target": 25, "feature_idx": 39}, {"source": 22, "target": 24, "feature_idx": 40}, {"source": 22, "target": 25, "feature_idx": 41}, {"source": 23, "target": 26, "feature_idx": 42}, {"source": 23, "target": 27, "feature_idx": 43}, {"source": 23, "target": 28, "feature_idx": 44}], "cascade": [], "pd_array": [], "cross_tf_confluence": [{"source": 12, "target": 24, "feature_idx": 45}, {"source": 12, "target": 25, "feature_idx": 46}, {"source": 12, "target": 26, "feature_idx": 47}, {"source": 12, "target": 27, "feature_idx": 48}, {"source": 12, "target": 28, "feature_idx": 49}, {"source": 13, "target": 24, "feature_idx": 50}, {"source": 13, "target": 25, "feature_idx": 51}, {"source": 13, "target": 26, "feature_idx": 52}, {"source": 13, "target": 27, "feature_idx": 53}, {"source": 13, "target": 28, "feature_idx": 54}, {"source": 14, "target": 24, "feature_idx": 55}, {"source": 14, "target": 25, "feature_idx": 56}, {"source": 14, "target": 26, "feature_idx": 57}, {"source": 14, "target": 27, "feature_idx": 58}, {"source": 14, "target": 28, "feature_idx": 59}, {"source": 15, "target": 24, "feature_idx": 60}, {"source": 15, "target": 25, "feature_idx": 61}, {"source": 15, "target": 26, "feature_idx": 62}, {"source": 15, "target": 27, "feature_idx": 63}, {"source": 15, "target": 28, "feature_idx": 64}, {"source": 16, "target": 24, "feature_idx": 65}, {"source": 16, "target": 25, "feature_idx": 66}, {"source": 16, "target": 26, "feature_idx": 67}, {"source": 16, "target": 27, "feature_idx": 68}, {"source": 16, "target": 28, "feature_idx": 69}, {"source": 17, "target": 24, "feature_idx": 70}, {"source": 17, "target": 25, "feature_idx": 71}, {"source": 17, "target": 26, "feature_idx": 72}, {"source": 17, "target": 27, "feature_idx": 73}, {"source": 17, "target": 28, "feature_idx": 74}, {"source": 18, "target": 24, "feature_idx": 75}, {"source": 18, "target": 25, "feature_idx": 76}, {"source": 18, "target": 26, "feature_idx": 77}, {"source": 18, "target": 27, "feature_idx": 78}, {"source": 18, "target": 28, "feature_idx": 79}, {"source": 19, "target": 24, "feature_idx": 80}, {"source": 19, "target": 25, "feature_idx": 81}, {"source": 19, "target": 26, "feature_idx": 82}, {"source": 19, "target": 27, "feature_idx": 83}, {"source": 19, "target": 28, "feature_idx": 84}, {"source": 20, "target": 24, "feature_idx": 85}, {"source": 20, "target": 25, "feature_idx": 86}, {"source": 20, "target": 26, "feature_idx": 87}, {"source": 20, "target": 27, "feature_idx": 88}, {"source": 20, "target": 28, "feature_idx": 89}, {"source": 21, "target": 24, "feature_idx": 90}, {"source": 21, "target": 25, "feature_idx": 91}, {"source": 21, "target": 26, "feature_idx": 92}, {"source": 21, "target": 27, "feature_idx": 93}, {"source": 21, "target": 28, "feature_idx": 94}, {"source": 22, "target": 24, "feature_idx": 95}, {"source": 22, "target": 25, "feature_idx": 96}, {"source": 22, "target": 26, "feature_idx": 97}, {"source": 22, "target": 27, "feature_idx": 98}, {"source": 22, "target": 28, "feature_idx": 99}, {"source": 23, "target": 24, "feature_idx": 100}, {"source": 23, "target": 25, "feature_idx": 101}, {"source": 23, "target": 26, "feature_idx": 102}, {"source": 23, "target": 27, "feature_idx": 103}, {"source": 23, "target": 28, "feature_idx": 104}], "temporal_echo": [{"source": 0, "target": 12, "feature_idx": 105}, {"source": 0, "target": 16, "feature_idx": 106}, {"source": 0, "target": 20, "feature_idx": 107}, {"source": 0, "target": 21, "feature_idx": 108}, {"source": 0, "target": 22, "feature_idx": 109}, {"source": 0, "target": 24, "feature_idx": 110}, {"source": 0, "target": 25, "feature_idx": 111}, {"source": 1, "target": 13, "feature_idx": 112}, {"source": 1, "target": 15, "feature_idx": 113}, {"source": 1, "target": 17, "feature_idx": 114}, {"source": 1, "target": 19, "feature_idx": 115}, {"source": 1, "target": 28, "feature_idx": 116}, {"source": 2, "target": 14, "feature_idx": 117}, {"source": 2, "target": 18, "feature_idx": 118}, {"source": 3, "target": 13, "feature_idx": 119}, {"source": 3, "target": 15, "feature_idx": 120}, {"source": 3, "target": 17, "feature_idx": 121}, {"source": 3, "target": 19, "feature_idx": 122}, {"source": 3, "target": 28, "feature_idx": 123}, {"source": 4, "target": 12, "feature_idx": 124}, {"source": 4, "target": 16, "feature_idx": 125}, {"source": 4, "target": 20, "feature_idx": 126}, {"source": 4, "target": 21, "feature_idx": 127}, {"source": 4, "target": 22, "feature_idx": 128}, {"source": 4, "target": 24, "feature_idx": 129}, {"source": 4, "target": 25, "feature_idx": 130}, {"source": 5, "target": 13, "feature_idx": 131}, {"source": 5, "target": 15, "feature_idx": 132}, {"source": 5, "target": 17, "feature_idx": 133}, {"source": 5, "target": 19, "feature_idx": 134}, {"source": 5, "target": 28, "feature_idx": 135}, {"source": 6, "target": 14, "feature_idx": 136}, {"source": 6, "target": 18, "feature_idx": 137}, {"source": 7, "target": 13, "feature_idx": 138}, {"source": 7, "target": 15, "feature_idx": 139}, {"source": 7, "target": 17, "feature_idx": 140}, {"source": 7, "target": 19, "feature_idx": 141}, {"source": 7, "target": 28, "feature_idx": 142}, {"source": 8, "target": 12, "feature_idx": 143}, {"source": 8, "target": 16, "feature_idx": 144}, {"source": 8, "target": 20, "feature_idx": 145}, {"source": 8, "target": 21, "feature_idx": 146}, {"source": 8, "target": 22, "feature_idx": 147}, {"source": 8, "target": 24, "feature_idx": 148}, {"source": 8, "target": 25, "feature_idx": 149}, {"source": 9, "target": 12, "feature_idx": 150}, {"source": 9, "target": 16, "feature_idx": 151}, {"source": 9, "target": 20, "feature_idx": 152}, {"source": 9, "target": 21, "feature_idx": 153}, {"source": 9, "target": 22, "feature_idx": 154}, {"source": 9, "target": 24, "feature_idx": 155}, {"source": 9, "target": 25, "feature_idx": 156}, {"source": 10, "target": 12, "feature_idx": 157}, {"source": 10, "target": 16, "feature_idx": 158}, {"source": 10, "target": 20, "feature_idx": 159}, {"source": 10, "target": 21, "feature_idx": 160}, {"source": 10, "target": 22, "feature_idx": 161}, {"source": 10, "target": 24, "feature_idx": 162}, {"source": 10, "target": 25, "feature_idx": 163}, {"source": 11, "target": 23, "feature_idx": 164}, {"source": 11, "target": 26, "feature_idx": 165}, {"source": 11, "target": 27, "feature_idx": 166}, {"source": 11, "target": 28, "feature_idx": 167}, {"source": 12, "target": 24, "feature_idx": 168}, {"source": 12, "target": 25, "feature_idx": 169}, {"source": 13, "target": 28, "feature_idx": 170}, {"source": 15, "target": 28, "feature_idx": 171}, {"source": 16, "target": 24, "feature_idx": 172}, {"source": 16, "target": 25, "feature_idx": 173}, {"source": 17, "target": 28, "feature_idx": 174}, {"source": 19, "target": 28, "feature_idx": 175}, {"source": 20, "target": 24, "feature_idx": 176}, {"source": 20, "target": 25, "feature_idx": 177}, {"source": 21, "target": 24, "feature_idx": 178}, {"source": 21, "target": 25, "feature_idx": 179}, {"source": 22, "target": 24, "feature_idx": 180}, {"source": 22, "target": 25, "feature_idx": 181}, {"source": 23, "target": 26, "feature_idx": 182}, {"source": 23, "target": 27, "feature_idx": 183}, {"source": 23, "target": 28, "feature_idx": 184}], "discovered": [{"source": 1, "target": 24, "feature_idx": 185}, {"source": 1, "target": 25, "feature_idx": 186}, {"source": 1, "target": 26, "feature_idx": 187}, {"source": 1, "target": 27, "feature_idx": 188}, {"source": 1, "target": 28, "feature_idx": 189}, {"source": 3, "target": 24, "feature_idx": 190}, {"source": 3, "target": 25, "feature_idx": 191}, {"source": 3, "target": 26, "feature_idx": 192}, {"source": 3, "target": 27, "feature_idx": 193}, {"source": 3, "target": 28, "feature_idx": 194}, {"source": 5, "target": 24, "feature_idx": 195}, {"source": 5, "target": 25, "feature_idx": 196}, {"source": 5, "target": 26, "feature_idx": 197}, {"source": 5, "target": 27, "feature_idx": 198}, {"source": 5, "target": 28, "feature_idx": 199}, {"source": 7, "target": 24, "feature_idx": 200}, {"source": 7, "target": 25, "feature_idx": 201}, {"source": 7, "target": 26, "feature_idx": 202}, {"source": 7, "target": 27, "feature_idx": 203}, {"source": 7, "target": 28, "feature_idx": 204}, {"source": 8, "target": 24, "feature_idx": 205}, {"source": 8, "target": 25, "feature_idx": 206}, {"source": 8, "target": 26, "feature_idx": 207}, {"source": 8, "target": 27, "feature_idx": 208}, {"source": 8, "target": 28, "feature_idx": 209}, {"source": 10, "target": 24, "feature_idx": 210}, {"source": 10, "target": 25, "feature_idx": 211}, {"source": 10, "target": 26, "feature_idx": 212}, {"source": 10, "target": 27, "feature_idx": 213}, {"source": 10, "target": 28, "feature_idx": 214}, {"source": 11, "target": 24, "feature_idx": 215}, {"source": 11, "target": 25, "feature_idx": 216}, {"source": 11, "target": 26, "feature_idx": 217}, {"source": 11, "target": 27, "feature_idx": 218}, {"source": 11, "target": 28, "feature_idx": 219}], "structural_context": []}, "rich_edge_features": [{"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}], "metadata": {"session_id": "unknown", "total_nodes": 29, "timeframe_counts": {"1m": 12, "5m": 0, "15m": 12, "1h": 5, "D": 0, "W": 0}, "feature_dimensions": 37, "preserved_raw": {"session_metadata": {"session_type": "ny_am", "session_date": "2025-07-25", "session_start": "09:30:00", "session_end": "11:59:00", "session_duration": 149, "transcription_source": "live_market_data", "data_completeness": "complete_session", "timezone": "ET", "session_status": "completed"}, "session_fpfvg": {"fpfvg_present": false, "fpfvg_formation": {"formation_time": "00:00:00", "premium_high": 0.0, "discount_low": 0.0, "gap_size": 0.0, "interactions": []}}, "price_movements": [{"timestamp": "09:30:00", "price_level": 23360.0, "movement_type": "open", "normalized_price": 0.2969543147208122, "pct_from_open": 0.0, "pct_from_high": 70.30456852791879, "pct_from_low": 29.695431472081218, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 9000, "price_momentum": 0.0, "range_position": 0.2969543147208122, "absolute_price": 23360.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.539946"}, {"timestamp": "12:00:00", "price_level": 23429.25, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.2964469178082192, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 9000, "normalized_time": 1.0067114093959733, "time_to_next_event": 1800, "price_momentum": 0.2964469178082192, "range_position": 1.0, "absolute_price": 23429.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.539997"}, {"timestamp": "12:30:00", "price_level": 23330.75, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.1252140410958904, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 10800, "normalized_time": 1.2080536912751678, "time_to_next_event": -1860, "price_momentum": -0.42041465262439043, "range_position": 0.0, "absolute_price": 23330.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540041"}, {"timestamp": "11:59:00", "price_level": 23421.0, "movement_type": "close", "normalized_price": 0.916243654822335, "pct_from_open": 0.2611301369863014, "pct_from_high": 8.375634517766498, "pct_from_low": 91.6243654822335, "time_since_session_open": 8940, "normalized_time": 1.0, "time_to_next_event": -8940, "price_momentum": 0.38682854173140596, "range_position": 0.916243654822335, "absolute_price": 23421.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540085"}, {"timestamp": "09:30:00", "price_level": 23360.0, "movement_type": "open", "normalized_price": 0.2969543147208122, "pct_from_open": 0.0, "pct_from_high": 70.30456852791879, "pct_from_low": 29.695431472081218, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 9000, "price_momentum": -0.26045002348319884, "range_position": 0.2969543147208122, "absolute_price": 23360.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540127"}, {"timestamp": "12:00:00", "price_level": 23429.25, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.2964469178082192, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 9000, "normalized_time": 1.0067114093959733, "time_to_next_event": 1800, "price_momentum": 0.2964469178082192, "range_position": 1.0, "absolute_price": 23429.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540168"}, {"timestamp": "12:30:00", "price_level": 23330.75, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.1252140410958904, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 10800, "normalized_time": 1.2080536912751678, "time_to_next_event": -1860, "price_momentum": -0.42041465262439043, "range_position": 0.0, "absolute_price": 23330.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540210"}, {"timestamp": "11:59:00", "price_level": 23421.0, "movement_type": "close", "normalized_price": 0.916243654822335, "pct_from_open": 0.2611301369863014, "pct_from_high": 8.375634517766498, "pct_from_low": 91.6243654822335, "time_since_session_open": 8940, "normalized_time": 1.0, "time_to_next_event": -8520, "price_momentum": 0.38682854173140596, "range_position": 0.916243654822335, "absolute_price": 23421.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540251"}, {"timestamp": "09:37:00", "price": 23361.75, "action": "touch", "context": "NY_AM_FPFVG_formation_premium_high", "normalized_price": 0.3147208121827411, "pct_from_open": 0.007491438356164384, "pct_from_high": 68.52791878172589, "pct_from_low": 31.472081218274113, "time_since_session_open": 420, "normalized_time": 0.04697986577181208, "time_to_next_event": 60, "price_momentum": -0.2529780965799923, "range_position": 0.3147208121827411, "absolute_price": 23361.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540291", "price_level": 23361.75}, {"timestamp": "09:38:00", "price": 23330.75, "action": "break", "context": "previous_day_lunch_low_taken_session_low_formation", "normalized_price": 0.0, "pct_from_open": -0.1252140410958904, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 480, "normalized_time": 0.053691275167785234, "time_to_next_event": 1080, "price_momentum": -0.13269553864757563, "range_position": 0.0, "absolute_price": 23330.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540332", "price_level": 23330.75}, {"timestamp": "09:56:00", "price": 23408.0, "action": "break", "context": "premarket_high_taken_out", "normalized_price": 0.7842639593908629, "pct_from_open": 0.2054794520547945, "pct_from_high": 21.573604060913706, "pct_from_low": 78.42639593908629, "time_since_session_open": 1560, "normalized_time": 0.174496644295302, "time_to_next_event": 5640, "price_momentum": 0.33110808696677135, "range_position": 0.7842639593908629, "absolute_price": 23408.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540373", "price_level": 23408.0}, {"timestamp": "11:30:00", "price": 23429.25, "action": "touch", "context": "NY_AM_session_high_formation_midnight_FVG_delivery", "normalized_price": 1.0, "pct_from_open": 0.2964469178082192, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 7200, "normalized_time": 0.8053691275167785, "time_to_next_event": 0, "price_momentum": 0.09078092959671907, "range_position": 1.0, "absolute_price": 23429.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.540404", "price_level": 23429.25}], "session_liquidity_events": [{"timestamp": "09:30:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "11:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "09:30:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "11:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "09:37:00", "event_type": "rebalance", "liquidity_type": "fpfvg", "target_level": "session_ny_am_fpfvg_formation_premium_high", "magnitude": "low", "context": "generated_from_ny_am_fpfvg_formation_premium_high"}, {"timestamp": "09:38:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "session_previous_day_lunch_low_taken_session_low_formation", "magnitude": "low", "context": "generated_from_previous_day_lunch_low_taken_session_low_formation"}, {"timestamp": "09:56:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_premarket_high_taken_out", "magnitude": "low", "context": "generated_from_premarket_high_taken_out"}, {"timestamp": "11:30:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_ny_am_session_high_formation_midnight_fvg_delivery", "magnitude": "low", "context": "generated_from_ny_am_session_high_formation_midnight_fvg_delivery"}], "energy_state": {"energy_density": 0.86, "total_accumulated": 74.5, "energy_rate": 30.0, "energy_source": "standardization_estimation", "session_duration": 149, "expansion_phases": 2, "retracement_phases": 2, "consolidation_phases": 1, "phase_transitions": 7, "session_status": "complete"}, "contamination_analysis": {"htf_contamination": {"immediate_cross_session_interaction": false, "htf_carryover_strength": 0.98, "cross_session_inheritance": 0.3}, "cross_session_inheritance": {"energy_carryover_coefficient": 0.3}}, "phase_transitions": [{"phase_type": "expansion", "start_time": "09:35:00", "end_time": "09:38:00", "high": 23378.25, "low": 23330.75, "description": "opening_downward_expansion_taking_lunch_low_liquidity"}, {"phase_type": "expansion", "start_time": "09:38:00", "end_time": "09:56:00", "high": 23408.0, "low": 23361.75, "description": "reversal_expansion_higher_taking_premarket_high"}, {"phase_type": "consolidation", "start_time": "09:56:00", "end_time": "11:21:00", "high": 23423.5, "low": 23382.75, "description": "extended_consolidation_taking_London_high_during_range"}, {"phase_type": "expansion", "start_time": "11:21:00", "end_time": "11:30:00", "high": 23429.25, "low": 23403.25, "description": "breakthrough_expansion_creating_session_high"}, {"phase_type": "expansion", "start_time": "11:30:00", "end_time": "11:45:00", "high": 23429.25, "low": 23402.5, "description": "retracement_phase_from_session_high"}, {"phase_type": "expansion", "start_time": "11:45:00", "end_time": "11:59:00", "high": 23427.0, "low": 23402.5, "description": "final_expansion_higher_session_close"}], "structures_identified": {"fair_value_gaps": [{"id": "nyam_fpfvg_2025_07_25", "formation_time": "09:37:00", "type": "first_presentation", "premium_high": 23361.75, "discount_low": 23348.75, "size_points": 13.0, "formation_context": "NY_AM_opening_expansion_phase", "delivery_status": "undelivered", "delivery_time": "pending"}], "session_levels": [{"type": "session_high", "level": 23429.25, "formation_time": "11:30:00", "touches": ["11:30:00"], "holds": true, "context": "breakthrough_expansion_session_high_formation"}, {"type": "session_low", "level": 23330.75, "formation_time": "09:38:00", "touches": ["09:38:00"], "holds": true, "context": "opening_expansion_reversal_point"}]}, "level_interactions": [{"timestamp": "09:38:00", "level": 23330.75, "level_origin": "Previous_Day_Lunch_Low", "interaction_type": "break", "result": "broken"}, {"timestamp": "09:56:00", "level": 23408.0, "level_origin": "Premarket_High", "interaction_type": "break", "result": "broken"}, {"timestamp": "11:21:00", "level": 23423.5, "level_origin": "London_High", "interaction_type": "break", "result": "broken"}, {"timestamp": "11:30:00", "level": 23429.25, "level_origin": "Midnight_OR_FVG", "interaction_type": "break", "result": "delivered"}], "micro_timing_analysis": {"cascade_events": [{"timestamp": "09:35:00", "event_type": "cascade_initiation", "price_level": 23378.25, "magnitude": 47.5, "duration_minutes": 3.0}, {"timestamp": "09:38:00", "event_type": "reversal_expansion", "price_level": 23361.75, "magnitude": 46.25, "duration_minutes": 18.0}, {"timestamp": "11:21:00", "event_type": "consolidation_break", "price_level": 23403.25, "magnitude": 26.0, "duration_minutes": 9.0}, {"timestamp": "11:30:00", "event_type": "retracement_phase", "price_level": 23429.25, "magnitude": 26.75, "duration_minutes": 15.0}, {"timestamp": "11:45:00", "event_type": "final_expansion", "price_level": 23402.5, "magnitude": 24.5, "duration_minutes": 14.0}], "phase_transitions": [{"from_phase": "consolidation", "to_phase": "expansion", "transition_time": "09:35:00", "trigger_event": "opening_momentum"}, {"from_phase": "expansion", "to_phase": "expansion", "transition_time": "09:38:00", "trigger_event": "reversal_point_formation"}, {"from_phase": "expansion", "to_phase": "consolidation", "transition_time": "09:56:00", "trigger_event": "momentum_exhaustion"}, {"from_phase": "consolidation", "to_phase": "expansion", "transition_time": "11:21:00", "trigger_event": "energy_accumulation_release"}, {"from_phase": "expansion", "to_phase": "expansion", "transition_time": "11:30:00", "trigger_event": "session_high_reversal"}, {"from_phase": "expansion", "to_phase": "expansion", "transition_time": "11:45:00", "trigger_event": "final_push_momentum"}], "level_interactions": [{"level_type": "liquidity_zone", "price_level": 23330.75, "interaction_time": "09:38:00", "interaction_type": "break", "significance": "high"}, {"level_type": "liquidity_zone", "price_level": 23408.0, "interaction_time": "09:56:00", "interaction_type": "break", "significance": "medium"}, {"level_type": "liquidity_zone", "price_level": 23423.5, "interaction_time": "11:21:00", "interaction_type": "break", "significance": "high"}, {"level_type": "fvg", "price_level": 23429.25, "interaction_time": "11:30:00", "interaction_type": "delivery", "significance": "high"}], "timing_metrics": {"total_cascade_events": 5, "average_phase_duration_minutes": 11.8, "consolidation_percentage": 57.0, "expansion_percentage": 43.0}}, "consolidation_expansion_raw": {"consolidation_periods": [{"start": "09:56:00", "end": "11:21:00", "range_high": 23423.5, "range_low": 23382.75, "touches_high": 3, "touches_low": 4}], "expansion_periods": [{"start": "09:35:00", "end": "09:38:00", "direction": "down", "start_price": 23378.25, "end_price": 23330.75, "total_distance": 47.5}, {"start": "09:38:00", "end": "09:56:00", "direction": "up", "start_price": 23361.75, "end_price": 23408.0, "total_distance": 46.25}, {"start": "11:21:00", "end": "11:30:00", "direction": "up", "start_price": 23403.25, "end_price": 23429.25, "total_distance": 26.0}, {"start": "11:30:00", "end": "11:45:00", "direction": "down", "start_price": 23429.25, "end_price": 23402.5, "total_distance": 26.75}, {"start": "11:45:00", "end": "11:59:00", "direction": "up", "start_price": 23402.5, "end_price": 23427.0, "total_distance": 24.5}]}, "fpfvg_observations": {"session_fpfvg_created": true, "previous_fpfvg_interactions": [{"gap_origin": "Midnight_Opening_Range", "interaction_time": "11:30:00", "interaction_type": "complete_fill", "price_entered": 23429.25, "result": "respected"}]}, "news_context": {"news_impacted": false, "news_events": []}, "liquidity_analysis": {"untaken_liquidity": [{"level": 23429.25, "side": "sell", "significance": "high", "time_identified": "11:30:00"}, {"level": 23330.75, "side": "buy", "significance": "high", "time_identified": "09:38:00"}], "liquidity_sweeps": [{"timestamp": "09:38:00", "swept_level": 23330.75, "sweep_magnitude": 47.5, "follow_through": true}, {"timestamp": "09:56:00", "swept_level": 23408.0, "sweep_magnitude": 46.25, "follow_through": true}, {"timestamp": "11:21:00", "swept_level": 23423.5, "sweep_magnitude": 26.0, "follow_through": true}]}, "fvg_analysis": {"fair_value_gaps": [{"gap_id": "nyam_fpfvg_2025_07_25", "top": 23361.75, "bottom": 23348.75, "created_time": "09:37:00", "gap_type": "bullish", "status": "open"}]}, "behavioral_observations": {"session_type_observed": "trending", "directional_attempts": [{"time": "09:35:00", "direction": "down", "outcome": "successful"}, {"time": "09:38:00", "direction": "up", "outcome": "successful"}, {"time": "11:21:00", "direction": "up", "outcome": "successful"}, {"time": "11:45:00", "direction": "up", "outcome": "successful"}], "institutional_activity": "strong_bidirectional_momentum_with_extended_consolidation_and_multiple_liquidity_targeting", "session_completion": "complete"}, "processing_metadata": {"original_file": "/Users/<USER>/grok-claude-automation/data/sessions/level_1/NYAM_Lvl-1_2025_07_25.json", "standardization_date": "2025-08-07T19:29:57.241654", "conversion_type": "legacy_schema_converted", "schema_version": "target_v1.0", "data_recovery": {"recovery_date": "2025-08-07T19:45:57.174085", "corrections_applied": ["Recovered 12 price movements", "Added recovered price movements"], "recovery_source": "backup_file_recovery"}, "relativity_enhancement": {"applied": true, "timestamp": "2025-08-14T13:06:58.540413", "features_added": ["normalized_price", "pct_from_open", "pct_from_high", "pct_from_low", "time_since_session_open", "normalized_time", "time_to_next_event", "price_momentum", "range_position", "absolute_price"], "permanent_validity": true, "regime_independence": true}}, "phase2_enhancement": {"enhancement_date": "2025-08-14T12:15:27.206614", "enhancement_version": "phase2_v1.0", "features_enhanced": ["htf_carryover_strength", "energy_density", "session_liquidity_events"], "authenticity_method": "market_derived_calculations", "pre_enhancement_score": 0.0, "post_enhancement_score": 100.0}, "relativity_stats": {"session_high": 23429.25, "session_low": 23330.75, "session_open": 23360.0, "session_close": 23429.25, "session_range": 98.5, "session_duration_seconds": 8940, "normalization_applied": true, "structural_relationships_enabled": true, "permanent_pattern_capability": true}}}}, "success": true, "node_count": 29, "edge_count": 220}}, "enhanced_rel_LONDON_Lvl-1_2025_07_28.json": {"session_name": "enhanced_rel_LONDON_Lvl-1_2025_07_28.json", "discovery_success": true, "patterns_found": 6, "patterns": [{"type": "temporal_structural", "description": "Price position 77.8% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "london", "structural_position": 0.7777777777777778, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 100.0% at 36000 minutes", "confidence": 0.7, "time_span_hours": 10.0, "session": "london", "structural_position": 1.0, "temporal_position": 36000}, {"type": "temporal_structural", "description": "Price position 0.0% at 37800 minutes", "confidence": 0.7, "time_span_hours": 10.5, "session": "london", "structural_position": 0.0, "temporal_position": 37800}, {"type": "temporal_structural", "description": "Price position 6.3% at 10740 minutes", "confidence": 0.7, "time_span_hours": 2.9833333333333334, "session": "london", "structural_position": 0.06280193236714976, "temporal_position": 10740}, {"type": "temporal_structural", "description": "Price position 77.8% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "london", "structural_position": 0.7777777777777778, "temporal_position": 0}, {"type": "htf_confluence", "description": "HTF confluence 0.79 strength with 0.84 energy density", "confidence": 0.79, "time_span_hours": 4.0, "session": "london", "htf_strength": 0.79, "energy_level": 0.839}], "embeddings_shape": "<PERSON>.<PERSON><PERSON>([29, 256])", "graph_info": {"graph": {"nodes": {"1m": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], "5m": [], "15m": [15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28], "1h": [], "D": [], "W": []}, "rich_node_features": [{"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}], "edges": {"temporal": [{"source": 0, "target": 4, "feature_idx": 0}, {"source": 4, "target": 8, "feature_idx": 1}, {"source": 8, "target": 9, "feature_idx": 2}, {"source": 9, "target": 10, "feature_idx": 3}, {"source": 10, "target": 11, "feature_idx": 4}, {"source": 11, "target": 12, "feature_idx": 5}, {"source": 12, "target": 13, "feature_idx": 6}, {"source": 13, "target": 14, "feature_idx": 7}, {"source": 14, "target": 3, "feature_idx": 8}, {"source": 3, "target": 7, "feature_idx": 9}, {"source": 7, "target": 1, "feature_idx": 10}, {"source": 1, "target": 5, "feature_idx": 11}, {"source": 5, "target": 2, "feature_idx": 12}, {"source": 2, "target": 6, "feature_idx": 13}, {"source": 15, "target": 19, "feature_idx": 14}, {"source": 19, "target": 23, "feature_idx": 15}, {"source": 23, "target": 24, "feature_idx": 16}, {"source": 24, "target": 25, "feature_idx": 17}, {"source": 25, "target": 26, "feature_idx": 18}, {"source": 26, "target": 27, "feature_idx": 19}, {"source": 27, "target": 28, "feature_idx": 20}, {"source": 28, "target": 18, "feature_idx": 21}, {"source": 18, "target": 22, "feature_idx": 22}, {"source": 22, "target": 16, "feature_idx": 23}, {"source": 16, "target": 20, "feature_idx": 24}, {"source": 20, "target": 17, "feature_idx": 25}, {"source": 17, "target": 21, "feature_idx": 26}], "scale": [], "cascade": [], "pd_array": [], "cross_tf_confluence": [], "temporal_echo": [{"source": 0, "target": 15, "feature_idx": 27}, {"source": 0, "target": 19, "feature_idx": 28}, {"source": 0, "target": 23, "feature_idx": 29}, {"source": 0, "target": 24, "feature_idx": 30}, {"source": 1, "target": 16, "feature_idx": 31}, {"source": 1, "target": 20, "feature_idx": 32}, {"source": 2, "target": 17, "feature_idx": 33}, {"source": 2, "target": 21, "feature_idx": 34}, {"source": 3, "target": 18, "feature_idx": 35}, {"source": 3, "target": 22, "feature_idx": 36}, {"source": 4, "target": 15, "feature_idx": 37}, {"source": 4, "target": 19, "feature_idx": 38}, {"source": 4, "target": 23, "feature_idx": 39}, {"source": 4, "target": 24, "feature_idx": 40}, {"source": 5, "target": 16, "feature_idx": 41}, {"source": 5, "target": 20, "feature_idx": 42}, {"source": 6, "target": 17, "feature_idx": 43}, {"source": 6, "target": 21, "feature_idx": 44}, {"source": 7, "target": 18, "feature_idx": 45}, {"source": 7, "target": 22, "feature_idx": 46}, {"source": 8, "target": 15, "feature_idx": 47}, {"source": 8, "target": 19, "feature_idx": 48}, {"source": 8, "target": 23, "feature_idx": 49}, {"source": 8, "target": 24, "feature_idx": 50}, {"source": 9, "target": 15, "feature_idx": 51}, {"source": 9, "target": 19, "feature_idx": 52}, {"source": 9, "target": 23, "feature_idx": 53}, {"source": 9, "target": 24, "feature_idx": 54}, {"source": 10, "target": 15, "feature_idx": 55}, {"source": 10, "target": 19, "feature_idx": 56}, {"source": 10, "target": 23, "feature_idx": 57}, {"source": 10, "target": 24, "feature_idx": 58}, {"source": 10, "target": 25, "feature_idx": 59}, {"source": 11, "target": 25, "feature_idx": 60}, {"source": 11, "target": 26, "feature_idx": 61}, {"source": 12, "target": 25, "feature_idx": 62}, {"source": 12, "target": 26, "feature_idx": 63}, {"source": 13, "target": 27, "feature_idx": 64}, {"source": 13, "target": 28, "feature_idx": 65}, {"source": 14, "target": 27, "feature_idx": 66}, {"source": 14, "target": 28, "feature_idx": 67}], "discovered": [], "structural_context": []}, "rich_edge_features": [{"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}], "metadata": {"session_id": "unknown", "total_nodes": 29, "timeframe_counts": {"1m": 15, "5m": 0, "15m": 14, "1h": 0, "D": 0, "W": 0}, "feature_dimensions": 37, "preserved_raw": {"session_metadata": {"session_type": "london", "session_date": "2025-07-28", "session_start": "02:00:00", "session_end": "04:59:00", "session_duration": 179, "transcription_source": "live_market_data", "data_completeness": "complete_session", "timezone": "ET", "session_status": "completed"}, "session_fpfvg": {"fpfvg_present": false, "fpfvg_formation": {"formation_time": "00:00:00", "premium_high": 0.0, "discount_low": 0.0, "gap_size": 0.0, "interactions": []}}, "price_movements": [{"timestamp": "02:00:00", "price_level": 23573.75, "movement_type": "open", "normalized_price": 0.7777777777777778, "pct_from_open": 0.0, "pct_from_high": 22.22222222222222, "pct_from_low": 77.77777777777779, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 36000, "price_momentum": 0.0, "range_position": 0.7777777777777778, "absolute_price": 23573.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542275"}, {"timestamp": "12:00:00", "price_level": 23585.25, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.048783074394188455, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 36000, "normalized_time": 3.35195530726257, "time_to_next_event": 1800, "price_momentum": 0.048783074394188455, "range_position": 1.0, "absolute_price": 23585.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542326"}, {"timestamp": "12:30:00", "price_level": 23533.5, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.17074076037965957, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 37800, "normalized_time": 3.5195530726256985, "time_to_next_event": -27060, "price_momentum": -0.21941679651477092, "range_position": 0.0, "absolute_price": 23533.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542371"}, {"timestamp": "04:59:00", "price_level": 23536.75, "movement_type": "close", "normalized_price": 0.06280193236714976, "pct_from_open": -0.15695423935521502, "pct_from_high": 93.71980676328504, "pct_from_low": 6.280193236714976, "time_since_session_open": 10740, "normalized_time": 1.0, "time_to_next_event": -10740, "price_momentum": 0.013810100495038988, "range_position": 0.06280193236714976, "absolute_price": 23536.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542413"}, {"timestamp": "02:00:00", "price_level": 23573.75, "movement_type": "open", "normalized_price": 0.7777777777777778, "pct_from_open": 0.0, "pct_from_high": 22.22222222222222, "pct_from_low": 77.77777777777779, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 36000, "price_momentum": 0.1572009729465623, "range_position": 0.7777777777777778, "absolute_price": 23573.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542455"}, {"timestamp": "12:00:00", "price_level": 23585.25, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.048783074394188455, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 36000, "normalized_time": 3.35195530726257, "time_to_next_event": 1800, "price_momentum": 0.048783074394188455, "range_position": 1.0, "absolute_price": 23585.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542496"}, {"timestamp": "12:30:00", "price_level": 23533.5, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.17074076037965957, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 37800, "normalized_time": 3.5195530726256985, "time_to_next_event": -27060, "price_momentum": -0.21941679651477092, "range_position": 0.0, "absolute_price": 23533.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542538"}, {"timestamp": "04:59:00", "price_level": 23536.75, "movement_type": "close", "normalized_price": 0.06280193236714976, "pct_from_open": -0.15695423935521502, "pct_from_high": 93.71980676328504, "pct_from_low": 6.280193236714976, "time_since_session_open": 10740, "normalized_time": 1.0, "time_to_next_event": -10620, "price_momentum": 0.013810100495038988, "range_position": 0.06280193236714976, "absolute_price": 23536.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542580"}, {"timestamp": "02:02:00", "price": 23576.25, "action": "touch", "context": "London first presentation FVG premium high created", "normalized_price": 0.8260869565217391, "pct_from_open": 0.010605016172649664, "pct_from_high": 17.391304347826086, "pct_from_low": 82.6086956521739, "time_since_session_open": 120, "normalized_time": 0.0111731843575419, "time_to_next_event": 720, "price_momentum": 0.1678226603078165, "range_position": 0.8260869565217391, "absolute_price": 23576.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542621", "price_level": 23576.25}, {"timestamp": "02:14:00", "price": 23585.25, "action": "touch", "context": "London session high created, reversal point", "normalized_price": 1.0, "pct_from_open": 0.048783074394188455, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 840, "normalized_time": 0.0782122905027933, "time_to_next_event": 780, "price_momentum": 0.038174009861619215, "range_position": 1.0, "absolute_price": 23585.25, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542662", "price_level": 23585.25}, {"timestamp": "02:27:00", "price": 23575.375, "action": "delivery", "context": "London first presentation FVG balanced and redelivered", "normalized_price": 0.8091787439613527, "pct_from_open": 0.006893260512222281, "pct_from_high": 19.082125603864732, "pct_from_low": 80.91787439613528, "time_since_session_open": 1620, "normalized_time": 0.15083798882681565, "time_to_next_event": 1320, "price_momentum": -0.04186938870692488, "range_position": 0.8091787439613527, "absolute_price": 23575.375, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542703", "price_level": 23575.375}, {"timestamp": "02:49:00", "price": 23573.75, "action": "touch", "context": "Retracement touches London opening price", "normalized_price": 0.7777777777777778, "pct_from_open": 0.0, "pct_from_high": 22.22222222222222, "pct_from_low": 77.77777777777779, "time_since_session_open": 2940, "normalized_time": 0.2737430167597765, "time_to_next_event": 840, "price_momentum": -0.006892785374569863, "range_position": 0.7777777777777778, "absolute_price": 23573.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542744", "price_level": 23573.75}, {"timestamp": "03:03:00", "price": 23550.875, "action": "delivery", "context": "Midnight Opening Range FVG redelivered during expansion lower", "normalized_price": 0.3357487922705314, "pct_from_open": -0.09703589797974442, "pct_from_high": 66.42512077294685, "pct_from_low": 33.57487922705314, "time_since_session_open": 3780, "normalized_time": 0.35195530726256985, "time_to_next_event": 2460, "price_momentum": -0.09703589797974442, "range_position": 0.3357487922705314, "absolute_price": 23550.875, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542785", "price_level": 23550.875}, {"timestamp": "03:44:00", "price": 23533.5, "action": "touch", "context": "London session low created", "normalized_price": 0.0, "pct_from_open": -0.17074076037965957, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 6240, "normalized_time": 0.5810055865921788, "time_to_next_event": 720, "price_momentum": -0.07377645204265235, "range_position": 0.0, "absolute_price": 23533.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542826", "price_level": 23533.5}, {"timestamp": "03:56:00", "price": 23550.875, "action": "delivery", "context": "Midnight Opening Range FVG redelivered during retracement", "normalized_price": 0.3357487922705314, "pct_from_open": -0.09703589797974442, "pct_from_high": 66.42512077294685, "pct_from_low": 33.57487922705314, "time_since_session_open": 6960, "normalized_time": 0.6480446927374302, "time_to_next_event": 0, "price_momentum": 0.07383092187732382, "range_position": 0.3357487922705314, "absolute_price": 23550.875, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.542856", "price_level": 23550.875}], "session_liquidity_events": [{"timestamp": "02:00:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "04:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "02:00:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "04:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "02:02:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_london first presentation fvg premium high created", "magnitude": "low", "context": "generated_from_london first presentation fvg premium high created"}, {"timestamp": "02:14:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_london session high created, reversal point", "magnitude": "low", "context": "generated_from_london session high created, reversal point"}, {"timestamp": "02:49:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_retracement touches london opening price", "magnitude": "low", "context": "generated_from_retracement touches london opening price"}, {"timestamp": "03:03:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_midnight opening range fvg redelivered during expansion lower", "magnitude": "medium", "context": "generated_from_midnight opening range fvg redelivered during expansion lower"}, {"timestamp": "03:44:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_london session low created", "magnitude": "low", "context": "generated_from_london session low created"}, {"timestamp": "03:56:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_midnight opening range fvg redelivered during retracement", "magnitude": "medium", "context": "generated_from_midnight opening range fvg redelivered during retracement"}], "energy_state": {"energy_density": 0.839, "total_accumulated": 89.5, "energy_rate": 30.0, "energy_source": "standardization_estimation", "session_duration": 179, "expansion_phases": 3, "retracement_phases": 2, "consolidation_phases": 2, "phase_transitions": 10, "session_status": "complete"}, "contamination_analysis": {"htf_contamination": {"immediate_cross_session_interaction": false, "htf_carryover_strength": 0.79, "cross_session_inheritance": 0.3}, "cross_session_inheritance": {"energy_carryover_coefficient": 0.3}}, "phase_transitions": [{"phase_type": "expansion", "start_time": "02:00:00", "end_time": "02:14:00", "high": 23585.25, "low": 23572.5, "description": "Initial expansion higher from session open to create session high"}, {"phase_type": "expansion", "start_time": "02:14:00", "end_time": "02:35:00", "high": 23585.25, "low": 23564.0, "description": "Expansion lower with FVG redelivery"}, {"phase_type": "consolidation", "start_time": "03:04:00", "end_time": "03:21:00", "high": 23556.0, "low": 23543.25, "description": "Consolidation phase after initial expansion phases"}, {"phase_type": "consolidation", "start_time": "03:31:00", "end_time": "03:44:00", "high": 23543.5, "low": 23533.5, "description": "Consolidation before session low formation"}, {"phase_type": "consolidation", "start_time": "04:13:00", "end_time": "04:59:00", "high": 23546.5, "low": 23533.5, "description": "Final consolidation phase to session close"}], "structures_identified": {"fair_value_gaps": [{"id": "london_fpfvg_2025_07_28", "formation_time": "02:02:00", "type": "first_presentation", "premium_high": 23576.25, "discount_low": 23574.5, "size_points": 1.75, "formation_context": "London first presentation FVG at session start", "delivery_status": "delivered", "delivery_time": "02:27:00"}], "session_levels": [{"type": "session_high", "level": 23585.25, "formation_time": "02:14:00", "touches": ["02:14:00"], "holds": true, "context": "London session high with immediate reversal"}, {"type": "session_low", "level": 23533.5, "formation_time": "03:44:00", "touches": ["03:44:00", "04:13:00"], "holds": true, "context": "London session low with subsequent retest"}]}, "level_interactions": [{"timestamp": "02:27:00", "level": 23575.375, "level_origin": "london_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "03:03:00", "level": 23550.875, "level_origin": "midnight_fpfvg", "interaction_type": "bounce", "result": "held"}, {"timestamp": "03:56:00", "level": 23550.875, "level_origin": "midnight_fpfvg", "interaction_type": "bounce", "result": "held"}], "consolidation_expansion_raw": {"consolidation_periods": [{"start": "03:04:00", "end": "03:21:00", "range_high": 23556.0, "range_low": 23543.25, "touches_high": 1, "touches_low": 1}, {"start": "03:31:00", "end": "03:44:00", "range_high": 23543.5, "range_low": 23533.5, "touches_high": 1, "touches_low": 1}, {"start": "04:13:00", "end": "04:59:00", "range_high": 23546.5, "range_low": 23533.5, "touches_high": 1, "touches_low": 2}], "expansion_periods": [{"start": "02:00:00", "end": "02:14:00", "direction": "up", "start_price": 23572.5, "end_price": 23585.25, "total_distance": 12.75}, {"start": "02:14:00", "end": "02:35:00", "direction": "down", "start_price": 23585.25, "end_price": 23564.0, "total_distance": 21.25}, {"start": "02:49:00", "end": "03:04:00", "direction": "down", "start_price": 23573.75, "end_price": 23548.75, "total_distance": 25.0}]}, "fpfvg_observations": {"session_fpfvg_created": true, "previous_fpfvg_interactions": [{"gap_origin": "midnight_session", "interaction_time": "03:03:00", "interaction_type": "complete_fill", "price_entered": 23550.875, "result": "respected"}, {"gap_origin": "midnight_session", "interaction_time": "03:56:00", "interaction_type": "complete_fill", "price_entered": 23550.875, "result": "respected"}]}, "behavioral_observations": {"session_type_observed": "trending", "directional_attempts": [{"time": "02:00:00", "direction": "up", "outcome": "successful"}, {"time": "02:14:00", "direction": "down", "outcome": "successful"}, {"time": "03:44:00", "direction": "up", "outcome": "partial"}], "institutional_activity": "strong_directional_bias_with_redelivery_respect", "session_completion": "complete"}, "processing_metadata": {"original_file": "/Users/<USER>/grok-claude-automation/data/sessions/level_1/LONDON_Lvl-1_2025_07_28.json", "standardization_date": "2025-08-07T19:29:57.235289", "conversion_type": "legacy_schema_converted", "schema_version": "target_v1.0", "data_recovery": {"recovery_date": "2025-08-07T19:45:57.074331", "corrections_applied": ["Recovered 15 price movements", "Added recovered price movements"], "recovery_source": "backup_file_recovery"}, "relativity_enhancement": {"applied": true, "timestamp": "2025-08-14T13:06:58.542865", "features_added": ["normalized_price", "pct_from_open", "pct_from_high", "pct_from_low", "time_since_session_open", "normalized_time", "time_to_next_event", "price_momentum", "range_position", "absolute_price"], "permanent_validity": true, "regime_independence": true}}, "phase2_enhancement": {"enhancement_date": "2025-08-14T12:15:27.180319", "enhancement_version": "phase2_v1.0", "features_enhanced": ["htf_carryover_strength", "energy_density", "session_liquidity_events"], "authenticity_method": "market_derived_calculations", "pre_enhancement_score": 0.0, "post_enhancement_score": 100.0}, "relativity_stats": {"session_high": 23585.25, "session_low": 23533.5, "session_open": 23573.75, "session_close": 23550.875, "session_range": 51.75, "session_duration_seconds": 10740, "normalization_applied": true, "structural_relationships_enabled": true, "permanent_pattern_capability": true}}}}, "success": true, "node_count": 29, "edge_count": 68}}, "enhanced_rel_LONDON_Lvl-1_2025_07_25.json": {"session_name": "enhanced_rel_LONDON_Lvl-1_2025_07_25.json", "discovery_success": true, "patterns_found": 6, "patterns": [{"type": "temporal_structural", "description": "Price position 92.9% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "london", "structural_position": 0.9294117647058824, "temporal_position": 0}, {"type": "temporal_structural", "description": "Price position 100.0% at 36000 minutes", "confidence": 0.7, "time_span_hours": 10.0, "session": "london", "structural_position": 1.0, "temporal_position": 36000}, {"type": "temporal_structural", "description": "Price position 0.0% at 37800 minutes", "confidence": 0.7, "time_span_hours": 10.5, "session": "london", "structural_position": 0.0, "temporal_position": 37800}, {"type": "temporal_structural", "description": "Price position 38.8% at 10740 minutes", "confidence": 0.7, "time_span_hours": 2.9833333333333334, "session": "london", "structural_position": 0.38823529411764707, "temporal_position": 10740}, {"type": "temporal_structural", "description": "Price position 92.9% at 0 minutes", "confidence": 0.7, "time_span_hours": 0.0, "session": "london", "structural_position": 0.9294117647058824, "temporal_position": 0}, {"type": "htf_confluence", "description": "HTF confluence 0.83 strength with 0.86 energy density", "confidence": 0.83, "time_span_hours": 4.0, "session": "london", "htf_strength": 0.83, "energy_level": 0.86}], "embeddings_shape": "<PERSON>.<PERSON><PERSON>([29, 256])", "graph_info": {"graph": {"nodes": {"1m": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11], "5m": [], "15m": [12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "1h": [24, 25, 26, 27, 28], "D": [], "W": []}, "rich_node_features": [{"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}, {"object_type": "RichNodeFeature", "tensor_shape": [37]}], "edges": {"temporal": [{"source": 0, "target": 4, "feature_idx": 0}, {"source": 4, "target": 8, "feature_idx": 1}, {"source": 8, "target": 9, "feature_idx": 2}, {"source": 9, "target": 10, "feature_idx": 3}, {"source": 10, "target": 11, "feature_idx": 4}, {"source": 11, "target": 3, "feature_idx": 5}, {"source": 3, "target": 7, "feature_idx": 6}, {"source": 7, "target": 1, "feature_idx": 7}, {"source": 1, "target": 5, "feature_idx": 8}, {"source": 5, "target": 2, "feature_idx": 9}, {"source": 2, "target": 6, "feature_idx": 10}, {"source": 12, "target": 16, "feature_idx": 11}, {"source": 16, "target": 20, "feature_idx": 12}, {"source": 20, "target": 21, "feature_idx": 13}, {"source": 21, "target": 22, "feature_idx": 14}, {"source": 22, "target": 23, "feature_idx": 15}, {"source": 23, "target": 15, "feature_idx": 16}, {"source": 15, "target": 19, "feature_idx": 17}, {"source": 19, "target": 13, "feature_idx": 18}, {"source": 13, "target": 17, "feature_idx": 19}, {"source": 17, "target": 14, "feature_idx": 20}, {"source": 14, "target": 18, "feature_idx": 21}, {"source": 24, "target": 25, "feature_idx": 22}, {"source": 25, "target": 26, "feature_idx": 23}, {"source": 26, "target": 27, "feature_idx": 24}, {"source": 27, "target": 28, "feature_idx": 25}], "scale": [{"source": 21, "target": 24, "feature_idx": 26}, {"source": 21, "target": 25, "feature_idx": 27}, {"source": 21, "target": 26, "feature_idx": 28}, {"source": 22, "target": 26, "feature_idx": 29}, {"source": 23, "target": 27, "feature_idx": 30}, {"source": 23, "target": 28, "feature_idx": 31}], "cascade": [], "pd_array": [], "cross_tf_confluence": [{"source": 12, "target": 24, "feature_idx": 32}, {"source": 12, "target": 25, "feature_idx": 33}, {"source": 12, "target": 26, "feature_idx": 34}, {"source": 12, "target": 27, "feature_idx": 35}, {"source": 12, "target": 28, "feature_idx": 36}, {"source": 13, "target": 24, "feature_idx": 37}, {"source": 13, "target": 25, "feature_idx": 38}, {"source": 13, "target": 26, "feature_idx": 39}, {"source": 13, "target": 27, "feature_idx": 40}, {"source": 13, "target": 28, "feature_idx": 41}, {"source": 14, "target": 24, "feature_idx": 42}, {"source": 14, "target": 25, "feature_idx": 43}, {"source": 14, "target": 26, "feature_idx": 44}, {"source": 14, "target": 27, "feature_idx": 45}, {"source": 14, "target": 28, "feature_idx": 46}, {"source": 15, "target": 24, "feature_idx": 47}, {"source": 15, "target": 25, "feature_idx": 48}, {"source": 15, "target": 26, "feature_idx": 49}, {"source": 15, "target": 27, "feature_idx": 50}, {"source": 15, "target": 28, "feature_idx": 51}, {"source": 16, "target": 24, "feature_idx": 52}, {"source": 16, "target": 25, "feature_idx": 53}, {"source": 16, "target": 26, "feature_idx": 54}, {"source": 16, "target": 27, "feature_idx": 55}, {"source": 16, "target": 28, "feature_idx": 56}, {"source": 17, "target": 24, "feature_idx": 57}, {"source": 17, "target": 25, "feature_idx": 58}, {"source": 17, "target": 26, "feature_idx": 59}, {"source": 17, "target": 27, "feature_idx": 60}, {"source": 17, "target": 28, "feature_idx": 61}, {"source": 18, "target": 24, "feature_idx": 62}, {"source": 18, "target": 25, "feature_idx": 63}, {"source": 18, "target": 26, "feature_idx": 64}, {"source": 18, "target": 27, "feature_idx": 65}, {"source": 18, "target": 28, "feature_idx": 66}, {"source": 19, "target": 24, "feature_idx": 67}, {"source": 19, "target": 25, "feature_idx": 68}, {"source": 19, "target": 26, "feature_idx": 69}, {"source": 19, "target": 27, "feature_idx": 70}, {"source": 19, "target": 28, "feature_idx": 71}, {"source": 20, "target": 24, "feature_idx": 72}, {"source": 20, "target": 25, "feature_idx": 73}, {"source": 20, "target": 26, "feature_idx": 74}, {"source": 20, "target": 27, "feature_idx": 75}, {"source": 20, "target": 28, "feature_idx": 76}, {"source": 21, "target": 24, "feature_idx": 77}, {"source": 21, "target": 25, "feature_idx": 78}, {"source": 21, "target": 26, "feature_idx": 79}, {"source": 21, "target": 27, "feature_idx": 80}, {"source": 21, "target": 28, "feature_idx": 81}, {"source": 22, "target": 24, "feature_idx": 82}, {"source": 22, "target": 25, "feature_idx": 83}, {"source": 22, "target": 26, "feature_idx": 84}, {"source": 22, "target": 27, "feature_idx": 85}, {"source": 22, "target": 28, "feature_idx": 86}, {"source": 23, "target": 24, "feature_idx": 87}, {"source": 23, "target": 25, "feature_idx": 88}, {"source": 23, "target": 26, "feature_idx": 89}, {"source": 23, "target": 27, "feature_idx": 90}, {"source": 23, "target": 28, "feature_idx": 91}], "temporal_echo": [{"source": 0, "target": 12, "feature_idx": 92}, {"source": 0, "target": 16, "feature_idx": 93}, {"source": 0, "target": 20, "feature_idx": 94}, {"source": 1, "target": 13, "feature_idx": 95}, {"source": 1, "target": 17, "feature_idx": 96}, {"source": 2, "target": 14, "feature_idx": 97}, {"source": 2, "target": 18, "feature_idx": 98}, {"source": 3, "target": 15, "feature_idx": 99}, {"source": 3, "target": 19, "feature_idx": 100}, {"source": 4, "target": 12, "feature_idx": 101}, {"source": 4, "target": 16, "feature_idx": 102}, {"source": 4, "target": 20, "feature_idx": 103}, {"source": 5, "target": 13, "feature_idx": 104}, {"source": 5, "target": 17, "feature_idx": 105}, {"source": 6, "target": 14, "feature_idx": 106}, {"source": 6, "target": 18, "feature_idx": 107}, {"source": 7, "target": 15, "feature_idx": 108}, {"source": 7, "target": 19, "feature_idx": 109}, {"source": 8, "target": 12, "feature_idx": 110}, {"source": 8, "target": 16, "feature_idx": 111}, {"source": 8, "target": 20, "feature_idx": 112}, {"source": 9, "target": 21, "feature_idx": 113}, {"source": 9, "target": 24, "feature_idx": 114}, {"source": 9, "target": 25, "feature_idx": 115}, {"source": 9, "target": 26, "feature_idx": 116}, {"source": 10, "target": 22, "feature_idx": 117}, {"source": 10, "target": 25, "feature_idx": 118}, {"source": 10, "target": 26, "feature_idx": 119}, {"source": 11, "target": 23, "feature_idx": 120}, {"source": 11, "target": 27, "feature_idx": 121}, {"source": 11, "target": 28, "feature_idx": 122}, {"source": 21, "target": 24, "feature_idx": 123}, {"source": 21, "target": 25, "feature_idx": 124}, {"source": 21, "target": 26, "feature_idx": 125}, {"source": 22, "target": 25, "feature_idx": 126}, {"source": 22, "target": 26, "feature_idx": 127}, {"source": 23, "target": 27, "feature_idx": 128}, {"source": 23, "target": 28, "feature_idx": 129}], "discovered": [{"source": 0, "target": 24, "feature_idx": 130}, {"source": 0, "target": 25, "feature_idx": 131}, {"source": 0, "target": 26, "feature_idx": 132}, {"source": 0, "target": 27, "feature_idx": 133}, {"source": 0, "target": 28, "feature_idx": 134}, {"source": 1, "target": 24, "feature_idx": 135}, {"source": 1, "target": 25, "feature_idx": 136}, {"source": 1, "target": 26, "feature_idx": 137}, {"source": 1, "target": 27, "feature_idx": 138}, {"source": 1, "target": 28, "feature_idx": 139}, {"source": 4, "target": 24, "feature_idx": 140}, {"source": 4, "target": 25, "feature_idx": 141}, {"source": 4, "target": 26, "feature_idx": 142}, {"source": 4, "target": 27, "feature_idx": 143}, {"source": 4, "target": 28, "feature_idx": 144}, {"source": 5, "target": 24, "feature_idx": 145}, {"source": 5, "target": 25, "feature_idx": 146}, {"source": 5, "target": 26, "feature_idx": 147}, {"source": 5, "target": 27, "feature_idx": 148}, {"source": 5, "target": 28, "feature_idx": 149}, {"source": 8, "target": 24, "feature_idx": 150}, {"source": 8, "target": 25, "feature_idx": 151}, {"source": 8, "target": 26, "feature_idx": 152}, {"source": 8, "target": 27, "feature_idx": 153}, {"source": 8, "target": 28, "feature_idx": 154}], "structural_context": []}, "rich_edge_features": [{"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}, {"object_type": "RichEdgeFeature", "tensor_shape": [17]}], "metadata": {"session_id": "unknown", "total_nodes": 29, "timeframe_counts": {"1m": 12, "5m": 0, "15m": 12, "1h": 5, "D": 0, "W": 0}, "feature_dimensions": 37, "preserved_raw": {"session_metadata": {"session_type": "london", "session_date": "2025-07-25", "session_start": "02:00:00", "session_end": "04:59:00", "session_duration": 179, "transcription_source": "live_market_data", "data_completeness": "complete_session", "timezone": "ET", "session_status": "completed"}, "session_fpfvg": {"fpfvg_present": false, "fpfvg_formation": {"formation_time": "00:00:00", "premium_high": 0.0, "discount_low": 0.0, "gap_size": 0.0, "interactions": []}}, "price_movements": [{"timestamp": "02:00:00", "price_level": 23408.0, "movement_type": "open", "normalized_price": 0.9294117647058824, "pct_from_open": 0.0, "pct_from_high": 7.0588235294117645, "pct_from_low": 92.94117647058823, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 36000, "price_momentum": 0.0, "range_position": 0.9294117647058824, "absolute_price": 23408.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487269"}, {"timestamp": "12:00:00", "price_level": 23412.5, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.019224196855775803, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 36000, "normalized_time": 3.35195530726257, "time_to_next_event": 1800, "price_momentum": 0.019224196855775803, "range_position": 1.0, "absolute_price": 23412.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487323"}, {"timestamp": "12:30:00", "price_level": 23348.75, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.2531185919343814, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 37800, "normalized_time": 3.5195530726256985, "time_to_next_event": -27060, "price_momentum": -0.27229044313934864, "range_position": 0.0, "absolute_price": 23348.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487369"}, {"timestamp": "04:59:00", "price_level": 23373.5, "movement_type": "close", "normalized_price": 0.38823529411764707, "pct_from_open": -0.14738550922761448, "pct_from_high": 61.1764705882353, "pct_from_low": 38.82352941176471, "time_since_session_open": 10740, "normalized_time": 1.0, "time_to_next_event": -10740, "price_momentum": 0.10600139193746988, "range_position": 0.38823529411764707, "absolute_price": 23373.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487413"}, {"timestamp": "02:00:00", "price_level": 23408.0, "movement_type": "open", "normalized_price": 0.9294117647058824, "pct_from_open": 0.0, "pct_from_high": 7.0588235294117645, "pct_from_low": 92.94117647058823, "time_since_session_open": 0, "normalized_time": 0.0, "time_to_next_event": 36000, "price_momentum": 0.14760305474148075, "range_position": 0.9294117647058824, "absolute_price": 23408.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487456"}, {"timestamp": "12:00:00", "price_level": 23412.5, "movement_type": "session_high", "normalized_price": 1.0, "pct_from_open": 0.019224196855775803, "pct_from_high": 0.0, "pct_from_low": 100.0, "time_since_session_open": 36000, "normalized_time": 3.35195530726257, "time_to_next_event": 1800, "price_momentum": 0.019224196855775803, "range_position": 1.0, "absolute_price": 23412.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487498"}, {"timestamp": "12:30:00", "price_level": 23348.75, "movement_type": "session_low", "normalized_price": 0.0, "pct_from_open": -0.2531185919343814, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 37800, "normalized_time": 3.5195530726256985, "time_to_next_event": -27060, "price_momentum": -0.27229044313934864, "range_position": 0.0, "absolute_price": 23348.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487540"}, {"timestamp": "04:59:00", "price_level": 23373.5, "movement_type": "close", "normalized_price": 0.38823529411764707, "pct_from_open": -0.14738550922761448, "pct_from_high": 61.1764705882353, "pct_from_low": 38.82352941176471, "time_since_session_open": 10740, "normalized_time": 1.0, "time_to_next_event": -10680, "price_momentum": 0.10600139193746988, "range_position": 0.38823529411764707, "absolute_price": 23373.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487581"}, {"timestamp": "02:01:00", "price": 23409.5, "action": "touch", "context": "London_FPFVG_formation_premium_high", "normalized_price": 0.9529411764705882, "pct_from_open": 0.006408065618591935, "pct_from_high": 4.705882352941177, "pct_from_low": 95.29411764705881, "time_since_session_open": 60, "normalized_time": 0.00558659217877095, "time_to_next_event": 3360, "price_momentum": 0.15402057886067555, "range_position": 0.9529411764705882, "absolute_price": 23409.5, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487623", "price_level": 23409.5}, {"timestamp": "02:57:00", "price": 23389.0, "action": "break", "context": "Asia_session_low_taken_out", "normalized_price": 0.6313725490196078, "pct_from_open": -0.08116883116883117, "pct_from_high": 36.86274509803922, "pct_from_low": 63.13725490196078, "time_since_session_open": 3420, "normalized_time": 0.31843575418994413, "time_to_next_event": 2220, "price_momentum": -0.08757128516200688, "range_position": 0.6313725490196078, "absolute_price": 23389.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487666", "price_level": 23389.0}, {"timestamp": "03:34:00", "price": 23360.0, "action": "break", "context": "previous_day_PM_session_low_taken_out", "normalized_price": 0.17647058823529413, "pct_from_open": -0.2050580997949419, "pct_from_high": 82.35294117647058, "pct_from_low": 17.647058823529413, "time_since_session_open": 5640, "normalized_time": 0.5251396648044693, "time_to_next_event": 2880, "price_momentum": -0.12398990978665185, "range_position": 0.17647058823529413, "absolute_price": 23360.0, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487707", "price_level": 23360.0}, {"timestamp": "04:22:00", "price": 23348.75, "action": "touch", "context": "London_session_low_formation_reversal_point", "normalized_price": 0.0, "pct_from_open": -0.2531185919343814, "pct_from_high": 100.0, "pct_from_low": 0.0, "time_since_session_open": 8520, "normalized_time": 0.7932960893854749, "time_to_next_event": 0, "price_momentum": -0.048159246575342464, "range_position": 0.0, "absolute_price": 23348.75, "relativity_enhanced": true, "enhancement_timestamp": "2025-08-14T13:06:58.487737", "price_level": 23348.75}], "session_liquidity_events": [{"timestamp": "02:00:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "04:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "02:00:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_open", "magnitude": "medium", "context": "generated_from_open"}, {"timestamp": "12:00:00", "event_type": "rebalance", "liquidity_type": "internal", "target_level": "session_session_high", "magnitude": "low", "context": "generated_from_session_high"}, {"timestamp": "12:30:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_session_low", "magnitude": "low", "context": "generated_from_session_low"}, {"timestamp": "04:59:00", "event_type": "interaction", "liquidity_type": "internal", "target_level": "session_close", "magnitude": "medium", "context": "generated_from_close"}, {"timestamp": "02:01:00", "event_type": "rebalance", "liquidity_type": "fpfvg", "target_level": "session_london_fpfvg_formation_premium_high", "magnitude": "low", "context": "generated_from_london_fpfvg_formation_premium_high"}, {"timestamp": "02:57:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_asia_session_low_taken_out", "magnitude": "low", "context": "generated_from_asia_session_low_taken_out"}, {"timestamp": "03:34:00", "event_type": "redelivery", "liquidity_type": "cross_session", "target_level": "session_previous_day_pm_session_low_taken_out", "magnitude": "low", "context": "generated_from_previous_day_pm_session_low_taken_out"}, {"timestamp": "04:22:00", "event_type": "redelivery", "liquidity_type": "internal", "target_level": "session_london_session_low_formation_reversal_point", "magnitude": "low", "context": "generated_from_london_session_low_formation_reversal_point"}], "energy_state": {"energy_density": 0.86, "total_accumulated": 89.5, "energy_rate": 30.0, "energy_source": "standardization_estimation", "session_duration": 179, "expansion_phases": 2, "retracement_phases": 2, "consolidation_phases": 1, "phase_transitions": 7, "session_status": "complete"}, "contamination_analysis": {"htf_contamination": {"immediate_cross_session_interaction": false, "htf_carryover_strength": 0.83, "cross_session_inheritance": 0.3}, "cross_session_inheritance": {"energy_carryover_coefficient": 0.3}}, "phase_transitions": [{"phase_type": "consolidation", "start_time": "02:00:00", "end_time": "02:34:00", "high": 23412.5, "low": 23404.0, "description": "opening_consolidation_before_major_expansion"}, {"phase_type": "expansion", "start_time": "02:34:00", "end_time": "02:57:00", "high": 23409.5, "low": 23389.0, "description": "initial_downward_expansion_delivering_multiple_FVGs"}, {"phase_type": "expansion", "start_time": "02:59:00", "end_time": "03:03:00", "high": 23394.25, "low": 23378.0, "description": "continuation_expansion_delivering_historical_FVGs"}, {"phase_type": "expansion", "start_time": "03:08:00", "end_time": "03:51:00", "high": 23394.0, "low": 23355.0, "description": "major_expansion_phase_with_liquidity_sweeps"}, {"phase_type": "expansion", "start_time": "04:10:00", "end_time": "04:22:00", "high": 23375.75, "low": 23348.75, "description": "final_push_creating_session_low"}, {"phase_type": "expansion", "start_time": "04:22:00", "end_time": "04:36:00", "high": 23379.75, "low": 23348.75, "description": "reversal_expansion_higher_from_session_low"}, {"phase_type": "consolidation", "start_time": "04:36:00", "end_time": "04:59:00", "high": 23381.25, "low": 23373.25, "description": "session_closing_consolidation"}], "structures_identified": {"fair_value_gaps": [{"id": "london_fpfvg_2025_07_25", "formation_time": "02:01:00", "type": "first_presentation", "premium_high": 23409.5, "discount_low": 23409.0, "size_points": 0.5, "formation_context": "London_session_opening_price_action", "delivery_status": "undelivered", "delivery_time": "pending"}], "session_levels": [{"type": "session_high", "level": 23412.5, "formation_time": "02:01:00", "touches": ["02:01:00"], "holds": true, "context": "London_opening_high_formation"}, {"type": "session_low", "level": 23348.75, "formation_time": "04:22:00", "touches": ["04:22:00"], "holds": true, "context": "major_expansion_low_reversal_point"}]}, "level_interactions": [{"timestamp": "02:39:00", "level": 23389.0, "level_origin": "Asia_FPFVG", "interaction_type": "break", "result": "delivered"}, {"timestamp": "02:50:00", "level": 23389.0, "level_origin": "Previous_Day_PM_FPFVG", "interaction_type": "break", "result": "delivered"}, {"timestamp": "02:57:00", "level": 23389.0, "level_origin": "Asia_Session_Low", "interaction_type": "break", "result": "broken"}, {"timestamp": "03:03:00", "level": 23378.0, "level_origin": "Multiple_Previous_Day_FVGs", "interaction_type": "break", "result": "delivered"}, {"timestamp": "03:34:00", "level": 23360.0, "level_origin": "Previous_Day_PM_Low", "interaction_type": "break", "result": "broken"}], "micro_timing_analysis": {"cascade_events": [{"timestamp": "02:34:00", "event_type": "cascade_initiation", "price_level": 23409.5, "magnitude": 20.5, "duration_minutes": 23.0}, {"timestamp": "02:59:00", "event_type": "cascade_continuation", "price_level": 23394.25, "magnitude": 16.25, "duration_minutes": 4.0}, {"timestamp": "03:08:00", "event_type": "major_expansion", "price_level": 23394.0, "magnitude": 39.0, "duration_minutes": 43.0}, {"timestamp": "04:10:00", "event_type": "final_push", "price_level": 23375.75, "magnitude": 27.0, "duration_minutes": 12.0}, {"timestamp": "04:22:00", "event_type": "reversal_expansion", "price_level": 23348.75, "magnitude": 31.0, "duration_minutes": 14.0}], "phase_transitions": [{"from_phase": "consolidation", "to_phase": "expansion", "transition_time": "02:34:00", "trigger_event": "consolidation_break"}, {"from_phase": "expansion", "to_phase": "expansion", "transition_time": "02:59:00", "trigger_event": "momentum_continuation"}, {"from_phase": "expansion", "to_phase": "expansion", "transition_time": "03:08:00", "trigger_event": "liquidity_cascade"}, {"from_phase": "expansion", "to_phase": "expansion", "transition_time": "04:22:00", "trigger_event": "reversal_point_formation"}, {"from_phase": "expansion", "to_phase": "consolidation", "transition_time": "04:36:00", "trigger_event": "momentum_exhaustion"}], "level_interactions": [{"level_type": "fvg", "price_level": 23389.0, "interaction_time": "02:39:00", "interaction_type": "delivery", "significance": "high"}, {"level_type": "fvg", "price_level": 23378.0, "interaction_time": "03:03:00", "interaction_type": "delivery", "significance": "high"}, {"level_type": "liquidity_zone", "price_level": 23389.0, "interaction_time": "02:57:00", "interaction_type": "break", "significance": "high"}, {"level_type": "liquidity_zone", "price_level": 23360.0, "interaction_time": "03:34:00", "interaction_type": "break", "significance": "high"}], "timing_metrics": {"total_cascade_events": 5, "average_phase_duration_minutes": 19.2, "consolidation_percentage": 25.7, "expansion_percentage": 74.3}}, "consolidation_expansion_raw": {"consolidation_periods": [{"start": "02:00:00", "end": "02:34:00", "range_high": 23412.5, "range_low": 23404.0, "touches_high": 2, "touches_low": 1}, {"start": "04:36:00", "end": "04:59:00", "range_high": 23381.25, "range_low": 23373.25, "touches_high": 1, "touches_low": 2}], "expansion_periods": [{"start": "02:34:00", "end": "02:57:00", "direction": "down", "start_price": 23409.5, "end_price": 23389.0, "total_distance": 20.5}, {"start": "02:59:00", "end": "03:03:00", "direction": "down", "start_price": 23394.25, "end_price": 23378.0, "total_distance": 16.25}, {"start": "03:08:00", "end": "03:51:00", "direction": "down", "start_price": 23394.0, "end_price": 23355.0, "total_distance": 39.0}, {"start": "04:10:00", "end": "04:22:00", "direction": "down", "start_price": 23375.75, "end_price": 23348.75, "total_distance": 27.0}, {"start": "04:22:00", "end": "04:36:00", "direction": "up", "start_price": 23348.75, "end_price": 23379.75, "total_distance": 31.0}]}, "fpfvg_observations": {"session_fpfvg_created": true, "previous_fpfvg_interactions": [{"gap_origin": "Asia_Session", "interaction_time": "02:39:00", "interaction_type": "complete_fill", "price_entered": 23389.0, "result": "respected"}, {"gap_origin": "Previous_Day_PM", "interaction_time": "02:50:00", "interaction_type": "complete_fill", "price_entered": 23389.0, "result": "respected"}, {"gap_origin": "Previous_Day_Multiple", "interaction_time": "03:03:00", "interaction_type": "complete_fill", "price_entered": 23378.0, "result": "respected"}]}, "news_context": {"news_impacted": false, "news_events": []}, "liquidity_analysis": {"untaken_liquidity": [{"level": 23412.5, "side": "sell", "significance": "high", "time_identified": "02:01:00"}, {"level": 23348.75, "side": "buy", "significance": "high", "time_identified": "04:22:00"}], "liquidity_sweeps": [{"timestamp": "02:57:00", "swept_level": 23389.0, "sweep_magnitude": 20.5, "follow_through": true}, {"timestamp": "03:34:00", "swept_level": 23360.0, "sweep_magnitude": 39.0, "follow_through": true}]}, "fvg_analysis": {"fair_value_gaps": [{"gap_id": "london_fpfvg_2025_07_25", "top": 23409.5, "bottom": 23409.0, "created_time": "02:01:00", "gap_type": "bearish", "status": "open"}]}, "behavioral_observations": {"session_type_observed": "volatile", "directional_attempts": [{"time": "02:34:00", "direction": "down", "outcome": "successful"}, {"time": "02:59:00", "direction": "down", "outcome": "successful"}, {"time": "03:08:00", "direction": "down", "outcome": "successful"}, {"time": "04:22:00", "direction": "up", "outcome": "successful"}], "institutional_activity": "massive_institutional_selling_with_multiple_liquidity_sweeps_and_fvg_deliveries", "session_completion": "complete"}, "processing_metadata": {"original_file": "/Users/<USER>/grok-claude-automation/data/preprocessing/level_1/LONDON_Lvl-1_2025_07_25.json", "standardization_date": "2025-08-07T19:29:57.216979", "conversion_type": "legacy_schema_converted", "schema_version": "target_v1.0", "data_recovery": {"recovery_date": "2025-08-07T19:45:57.092872", "corrections_applied": ["Recovered 12 price movements", "Added recovered price movements"], "recovery_source": "backup_file_recovery"}, "relativity_enhancement": {"applied": true, "timestamp": "2025-08-14T13:06:58.487749", "features_added": ["normalized_price", "pct_from_open", "pct_from_high", "pct_from_low", "time_since_session_open", "normalized_time", "time_to_next_event", "price_momentum", "range_position", "absolute_price"], "permanent_validity": true, "regime_independence": true}}, "phase2_enhancement": {"enhancement_date": "2025-08-14T12:15:27.185806", "enhancement_version": "phase2_v1.0", "features_enhanced": ["htf_carryover_strength", "energy_density", "session_liquidity_events"], "authenticity_method": "market_derived_calculations", "pre_enhancement_score": 0.0, "post_enhancement_score": 100.0}, "relativity_stats": {"session_high": 23412.5, "session_low": 23348.75, "session_open": 23408.0, "session_close": 23348.75, "session_range": 63.75, "session_duration_seconds": 10740, "normalization_applied": true, "structural_relationships_enabled": true, "permanent_pattern_capability": true}}}}, "success": true, "node_count": 29, "edge_count": 155}}}, "quality_metrics": {"total_patterns": 30, "unique_descriptions": 23, "duplication_rate": 23.333333333333332, "time_spans_analysis": {"total_patterns": 30, "zero_time_spans": 0, "non_zero_time_spans": 30, "zero_span_percentage": 0.0, "average_time_span": 2.7305555555555556, "max_time_span": 10.5}, "sessions_identified": 4, "sessions_list": ["london", "asia", "ny_pm", "ny_am"], "description_frequency": {"Price position 81.8% at 0 minutes": 2, "Price position 0.0% at 0 minutes": 2, "Price position 29.7% at 0 minutes": 2, "Price position 77.8% at 0 minutes": 2, "Price position 100.0% at 36000 minutes": 2, "Price position 0.0% at 37800 minutes": 2, "Price position 92.9% at 0 minutes": 2, "Price position 89.3% at 0 minutes": 1, "Price position 29.4% at 9540 minutes": 1, "HTF confluence 0.99 strength with 0.95 energy density": 1}, "temporal_coherence": true, "archaeological_authenticity_score": 92.33333333333334}, "comparative_analysis": {"contaminated_baseline": {"duplication_rate": 96.8, "unique_descriptions": 13, "zero_time_spans": 4840, "authenticity_score": 2.1}, "enhanced_results": {"total_patterns": 30, "unique_descriptions": 23, "duplication_rate": 23.333333333333332, "time_spans_analysis": {"total_patterns": 30, "zero_time_spans": 0, "non_zero_time_spans": 30, "zero_span_percentage": 0.0, "average_time_span": 2.7305555555555556, "max_time_span": 10.5}, "sessions_identified": 4, "sessions_list": ["london", "asia", "ny_pm", "ny_am"], "description_frequency": {"Price position 81.8% at 0 minutes": 2, "Price position 0.0% at 0 minutes": 2, "Price position 29.7% at 0 minutes": 2, "Price position 77.8% at 0 minutes": 2, "Price position 100.0% at 36000 minutes": 2, "Price position 0.0% at 37800 minutes": 2, "Price position 92.9% at 0 minutes": 2, "Price position 89.3% at 0 minutes": 1, "Price position 29.4% at 9540 minutes": 1, "HTF confluence 0.99 strength with 0.95 energy density": 1}, "temporal_coherence": true, "archaeological_authenticity_score": 92.33333333333334}, "improvements": {"duplication_improvement": 73.46666666666667, "unique_descriptions_improvement": 10, "temporal_improvement": 30, "authenticity_improvement": 90.23333333333335}, "success_threshold_met": true}, "assessment": "✅ SUCCESS: Significant improvement, archaeological capability restored", "recommendation": "Proceed with full validation across all 33 enhanced sessions"}